{"ast": null, "code": "export default {\n  name: 'InformationSection',\n  emits: ['open-help', 'contact-support']\n};", "map": {"version": 3, "names": ["name", "emits"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\client\\sections\\InformationSection.vue"], "sourcesContent": ["<template>\n  <section class=\"info-section\" aria-labelledby=\"info-title\">\n    <div class=\"container\">\n      <div class=\"info-grid\">\n        <!-- Requirements Card -->\n        <div class=\"info-card requirements-card\">\n          <div class=\"card-header\">\n            <svg class=\"card-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M19,3H5C3.9,3 3,3.9 3,5V19C3,20.1 3.9,21 5,21H19C20.1,21 21,20.1 21,19V5C21,3.9 20.1,3 19,3M19,19H5V5H19V19M17,12H7V10H17V12M15,16H7V14H15V16M17,8H7V6H17V8Z\"/>\n            </svg>\n            <h3>Before You Start</h3>\n          </div>\n          <div class=\"card-content\">\n            <ul class=\"requirements-list\" role=\"list\">\n              <li class=\"requirement-item\">\n                <svg class=\"check-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2M10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z\"/>\n                </svg>\n                <span>Complete and accurate profile information</span>\n              </li>\n              <li class=\"requirement-item\">\n                <svg class=\"check-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2M10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z\"/>\n                </svg>\n                <span>Valid government-issued ID ready for upload</span>\n              </li>\n              <li class=\"requirement-item\">\n                <svg class=\"check-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2M10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z\"/>\n                </svg>\n                <span>Supporting documents (if required)</span>\n              </li>\n              <li class=\"requirement-item\">\n                <svg class=\"check-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2M10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z\"/>\n                </svg>\n                <span>Payment method for processing fees</span>\n              </li>\n            </ul>\n          </div>\n        </div>\n\n        <!-- Process Card -->\n        <div class=\"info-card process-card\">\n          <div class=\"card-header\">\n            <svg class=\"card-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M14,2A8,8 0 0,1 22,10A8,8 0 0,1 14,18A8,8 0 0,1 6,10A8,8 0 0,1 14,2M14,4A6,6 0 0,0 8,10A6,6 0 0,0 14,16A6,6 0 0,0 20,10A6,6 0 0,0 14,4M14,5.5A4.5,4.5 0 0,1 18.5,10A4.5,4.5 0 0,1 14,14.5A4.5,4.5 0 0,1 9.5,10A4.5,4.5 0 0,1 14,5.5M14,7A3,3 0 0,0 11,10A3,3 0 0,0 14,13A3,3 0 0,0 17,10A3,3 0 0,0 14,7M2,18A2,2 0 0,1 4,16H6A2,2 0 0,1 8,18V20A2,2 0 0,1 6,22H4A2,2 0 0,1 2,20V18Z\"/>\n            </svg>\n            <h3>How It Works</h3>\n          </div>\n          <div class=\"card-content\">\n            <ol class=\"process-steps\" role=\"list\">\n              <li class=\"process-step\">\n                <div class=\"step-number\" aria-hidden=\"true\">1</div>\n                <div class=\"step-content\">\n                  <strong>Select Document</strong>\n                  <span>Choose the document type you need</span>\n                </div>\n              </li>\n              <li class=\"process-step\">\n                <div class=\"step-number\" aria-hidden=\"true\">2</div>\n                <div class=\"step-content\">\n                  <strong>Fill Application</strong>\n                  <span>Complete the required information</span>\n                </div>\n              </li>\n              <li class=\"process-step\">\n                <div class=\"step-number\" aria-hidden=\"true\">3</div>\n                <div class=\"step-content\">\n                  <strong>Submit & Pay</strong>\n                  <span>Review and submit with payment</span>\n                </div>\n              </li>\n              <li class=\"process-step\">\n                <div class=\"step-number\" aria-hidden=\"true\">4</div>\n                <div class=\"step-content\">\n                  <strong>Track Progress</strong>\n                  <span>Monitor your request status</span>\n                </div>\n              </li>\n            </ol>\n          </div>\n        </div>\n\n        <!-- Help Card -->\n        <div class=\"info-card help-card\">\n          <div class=\"card-header\">\n            <svg class=\"card-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z\"/>\n            </svg>\n            <h3>Need Assistance?</h3>\n          </div>\n          <div class=\"card-content\">\n            <p class=\"help-description\">\n              Our support team is here to help you with any questions about document requirements or the application process.\n            </p>\n            <div class=\"help-actions\">\n              <button \n                class=\"btn btn-outline help-btn\" \n                @click=\"$emit('open-help')\"\n                type=\"button\"\n              >\n                <svg class=\"btn-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M11,18H13V16H11V18M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,20C7.59,20 4,16.41 4,12C7.59,8 4,8 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,6A4,4 0 0,0 8,10H10A2,2 0 0,1 12,8A2,2 0 0,1 14,10C14,12 11,11.75 11,15H13C13,12.75 16,12.5 16,10A4,4 0 0,0 12,6Z\"/>\n                </svg>\n                View FAQ\n              </button>\n              <button \n                class=\"btn btn-outline contact-btn\" \n                @click=\"$emit('contact-support')\"\n                type=\"button\"\n              >\n                <svg class=\"btn-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M6.62,10.79C8.06,13.62 10.38,15.94 13.21,17.38L15.41,15.18C15.69,14.9 16.08,14.82 16.43,14.93C17.55,15.3 18.75,15.5 20,15.5A1,1 0 0,1 21,16.5V20A1,1 0 0,1 20,21A17,17 0 0,1 3,4A1,1 0 0,1 4,3H7.5A1,1 0 0,1 8.5,4C8.5,5.25 8.7,6.45 9.07,7.57C9.18,7.92 9.1,8.31 8.82,8.59L6.62,10.79Z\"/>\n                </svg>\n                Contact Support\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </section>\n</template>\n\n<script>\nexport default {\n  name: 'InformationSection',\n  emits: [\n    'open-help',\n    'contact-support'\n  ]\n};\n</script>\n\n<style scoped>\n/* Information Section */\n.info-section {\n  padding: var(--spacing-10) 0;\n  background: var(--color-bg-secondary);\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 var(--spacing-4);\n}\n\n.info-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\n  gap: var(--spacing-6);\n}\n\n.info-card {\n  background: var(--color-bg-primary);\n  border: 1px solid var(--color-border-light);\n  border-radius: var(--radius-xl);\n  padding: var(--spacing-6);\n  transition: all var(--duration-base) var(--easing-standard);\n}\n\n.info-card:hover {\n  transform: translateY(-4px);\n  box-shadow: var(--shadow-4);\n  border-color: var(--color-primary-light);\n}\n\n.card-header {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-3);\n  margin-bottom: var(--spacing-5);\n}\n\n.card-icon {\n  width: 32px;\n  height: 32px;\n  color: var(--color-primary);\n}\n\n.card-header h3 {\n  font-size: var(--font-size-lg);\n  font-weight: 600;\n  color: var(--color-text-primary);\n  margin: 0;\n  line-height: var(--line-height-3);\n}\n\n.card-content {\n  color: var(--color-text-secondary);\n  line-height: var(--line-height-5);\n}\n\n/* Requirements List */\n.requirements-list {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.requirement-item {\n  display: flex;\n  align-items: flex-start;\n  gap: var(--spacing-3);\n  margin-bottom: var(--spacing-3);\n  font-size: var(--font-size-sm);\n}\n\n.requirement-item:last-child {\n  margin-bottom: 0;\n}\n\n.check-icon {\n  width: 20px;\n  height: 20px;\n  color: var(--color-success);\n  flex-shrink: 0;\n  margin-top: 2px;\n}\n\n/* Process Steps */\n.process-steps {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.process-step {\n  display: flex;\n  align-items: flex-start;\n  gap: var(--spacing-4);\n  margin-bottom: var(--spacing-5);\n  position: relative;\n}\n\n.process-step:last-child {\n  margin-bottom: 0;\n}\n\n.process-step:not(:last-child)::after {\n  content: '';\n  position: absolute;\n  left: 18px;\n  top: 36px;\n  bottom: -20px;\n  width: 2px;\n  background: var(--color-border-medium);\n}\n\n.step-number {\n  width: 36px;\n  height: 36px;\n  background: var(--color-primary);\n  color: var(--color-text-inverse);\n  border-radius: var(--radius-full);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 700;\n  font-size: var(--font-size-sm);\n  flex-shrink: 0;\n  position: relative;\n  z-index: 1;\n}\n\n.step-content {\n  flex: 1;\n  padding-top: var(--spacing-1);\n}\n\n.step-content strong {\n  display: block;\n  font-size: var(--font-size-md);\n  font-weight: 600;\n  color: var(--color-text-primary);\n  margin-bottom: var(--spacing-1);\n  line-height: var(--line-height-3);\n}\n\n.step-content span {\n  font-size: var(--font-size-sm);\n  color: var(--color-text-secondary);\n  line-height: var(--line-height-4);\n}\n\n/* Help Section */\n.help-description {\n  font-size: var(--font-size-sm);\n  margin: 0 0 var(--spacing-5);\n  line-height: var(--line-height-5);\n}\n\n.help-actions {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-3);\n}\n\n.btn {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--spacing-2);\n  padding: var(--spacing-3) var(--spacing-5);\n  border: 2px solid var(--color-border-medium);\n  border-radius: var(--radius-lg);\n  font-size: var(--font-size-sm);\n  font-weight: 600;\n  text-decoration: none;\n  cursor: pointer;\n  transition: all var(--duration-base) var(--easing-standard);\n  min-height: 44px;\n  background: transparent;\n  color: var(--color-text-primary);\n  width: 100%;\n}\n\n.btn:hover,\n.btn:focus {\n  border-color: var(--color-primary);\n  color: var(--color-primary);\n  transform: translateY(-2px);\n  box-shadow: var(--shadow-2);\n}\n\n.btn:focus {\n  outline: var(--focus-ring-width) solid var(--color-accent);\n  outline-offset: var(--focus-ring-offset);\n}\n\n.btn-icon {\n  width: 18px;\n  height: 18px;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .info-grid {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-5);\n  }\n  \n  .info-card {\n    padding: var(--spacing-5);\n  }\n  \n  .help-actions {\n    flex-direction: column;\n  }\n}\n\n@media (max-width: 480px) {\n  .card-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: var(--spacing-2);\n  }\n  \n  .process-step {\n    gap: var(--spacing-3);\n  }\n  \n  .step-number {\n    width: 32px;\n    height: 32px;\n  }\n  \n  .process-step:not(:last-child)::after {\n    left: 15px;\n  }\n}\n</style>\n"], "mappings": "AA8HA,eAAe;EACbA,IAAI,EAAE,oBAAoB;EAC1BC,KAAK,EAAE,CACL,WAAW,EACX,iBAAgB;AAEpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}