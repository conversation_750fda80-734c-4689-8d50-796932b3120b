{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport documentRequestService from '@/services/documentRequestService';\nimport ClientHeader from './ClientHeader.vue';\nimport unifiedAuthService from '@/services/unifiedAuthService';\nexport default {\n  name: 'NewDocumentRequest',\n  components: {\n    ClientHeader\n  },\n  data() {\n    return {\n      documentTypes: [],\n      loading: true,\n      error: null,\n      // Header state\n      showUserDropdown: false,\n      sidebarCollapsed: false,\n      activeMenu: 'dashboard',\n      // User data\n      userName: 'User',\n      userEmail: '<EMAIL>',\n      userAvatar: null,\n      firstName: 'User',\n      totalRequests: 0,\n      pendingRequests: 0\n    };\n  },\n  async mounted() {\n    await this.loadUserData();\n    await this.loadDocumentTypes();\n    await this.loadUserStats();\n  },\n  methods: {\n    async loadUserData() {\n      try {\n        const currentUser = unifiedAuthService.getCurrentUser();\n        if (currentUser) {\n          this.userName = currentUser.username || 'User';\n          this.userEmail = currentUser.email || '<EMAIL>';\n          this.firstName = currentUser.first_name || currentUser.username || 'User';\n          // Set user avatar if available\n          this.userAvatar = currentUser.avatar || null;\n        }\n      } catch (error) {\n        console.error('Error loading user data:', error);\n      }\n    },\n    async loadUserStats() {\n      try {\n        // TODO: Implement API call to get user statistics\n        // For now, using placeholder data\n        this.totalRequests = 5;\n        this.pendingRequests = 2;\n      } catch (error) {\n        console.error('Error loading user stats:', error);\n      }\n    },\n    async loadDocumentTypes() {\n      try {\n        this.loading = true;\n        this.error = null;\n        const response = await documentRequestService.getDocumentTypes();\n        this.documentTypes = response.data || [];\n      } catch (error) {\n        console.error('Error loading document types:', error);\n        this.error = error.response?.data?.message || 'Failed to load available services';\n      } finally {\n        this.loading = false;\n      }\n    },\n    selectDocumentType(documentType) {\n      if (!documentType.is_active) return;\n\n      // Navigate to specific document request form\n      const routeName = this.getRouteForDocumentType(documentType.type_name);\n      if (routeName) {\n        this.$router.push({\n          name: routeName,\n          params: {\n            documentTypeId: documentType.id\n          }\n        });\n      }\n    },\n    getRouteForDocumentType(typeName) {\n      const routes = {\n        'Barangay Clearance': 'BarangayClearanceRequest',\n        'Cedula': 'CedulaRequest'\n      };\n      return routes[typeName];\n    },\n    getDocumentIcon(typeName) {\n      const icons = {\n        'Barangay Clearance': 'fas fa-certificate',\n        'Cedula': 'fas fa-id-card'\n      };\n      return icons[typeName] || 'fas fa-file-alt';\n    },\n    getProcessingTime(typeName) {\n      const times = {\n        'Barangay Clearance': '3-5 business days',\n        'Cedula': '1-2 business days'\n      };\n      return times[typeName] || '3-5 business days';\n    },\n    formatCurrency(amount) {\n      return parseFloat(amount).toFixed(2);\n    },\n    // Header event handlers\n    handleSidebarToggle() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n    },\n    handleUserDropdownToggle() {\n      this.showUserDropdown = !this.showUserDropdown;\n    },\n    handleMenuAction(action) {\n      console.log('Menu action:', action);\n      switch (action) {\n        case 'profile':\n          // TODO: Navigate to profile page\n          break;\n        case 'settings':\n          // TODO: Navigate to settings page\n          break;\n        case 'account':\n          // TODO: Navigate to account page\n          break;\n      }\n    },\n    handleLogout() {\n      try {\n        unifiedAuthService.logout();\n        this.$router.push({\n          name: 'WelcomePage'\n        });\n      } catch (error) {\n        console.error('Logout error:', error);\n      }\n    },\n    handleError(error) {\n      console.error('Header error:', error);\n    },\n    // Search handler\n    handleSearch(query) {\n      console.log('Search query:', query);\n      // TODO: Implement search functionality\n      // This could search through documents, services, or requests\n      // For now, we'll just log the query\n    },\n    // Navigation methods\n    scrollToServices() {\n      this.$refs.servicesSection?.scrollIntoView({\n        behavior: 'smooth'\n      });\n    },\n    goToMyRequests() {\n      this.$router.push({\n        name: 'MyRequests'\n      });\n    },\n    goToProfile() {\n      // TODO: Navigate to profile page\n      console.log('Navigate to profile');\n    },\n    goBack() {\n      this.$router.push({\n        name: 'ClientDashboard'\n      });\n    },\n    openHelp() {\n      // TODO: Implement help/FAQ modal or page\n      console.log('Opening help...');\n    },\n    contactSupport() {\n      // TODO: Implement contact support functionality\n      console.log('Contacting support...');\n      // Could open a modal, redirect to contact page, or open email client\n      // For now, we'll just log the action\n    }\n  }\n};", "map": {"version": 3, "names": ["documentRequestService", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unifiedAuthService", "name", "components", "data", "documentTypes", "loading", "error", "showUserDropdown", "sidebarCollapsed", "activeMenu", "userName", "userEmail", "userAvatar", "firstName", "totalRequests", "pendingRequests", "mounted", "loadUserData", "loadDocumentTypes", "loadUserStats", "methods", "currentUser", "getCurrentUser", "username", "email", "first_name", "avatar", "console", "response", "getDocumentTypes", "message", "selectDocumentType", "documentType", "is_active", "routeName", "getRouteForDocumentType", "type_name", "$router", "push", "params", "documentTypeId", "id", "typeName", "routes", "getDocumentIcon", "icons", "getProcessingTime", "times", "formatCurrency", "amount", "parseFloat", "toFixed", "handleSidebarToggle", "handleUserDropdownToggle", "handleMenuAction", "action", "log", "handleLogout", "logout", "handleError", "handleSearch", "query", "scrollToServices", "$refs", "servicesSection", "scrollIntoView", "behavior", "goToMyRequests", "goToProfile", "goBack", "openHelp", "contactSupport"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\client\\NewDocumentRequest.vue"], "sourcesContent": ["<template>\n  <div class=\"client-dashboard\">\n    <!-- Background -->\n    <div class=\"background-container\">\n      <div class=\"background-image\"></div>\n      <div class=\"background-overlay\"></div>\n    </div>\n\n    <!-- Client Header with Navigation -->\n    <ClientHeader\n      :userName=\"userName\"\n      :userEmail=\"userEmail\"\n      :userAvatar=\"userAvatar\"\n      :showUserDropdown=\"showUserDropdown\"\n      :sidebarCollapsed=\"sidebarCollapsed\"\n      :activeMenu=\"activeMenu\"\n      :showBreadcrumbs=\"true\"\n      @sidebar-toggle=\"handleSidebarToggle\"\n      @user-dropdown-toggle=\"handleUserDropdownToggle\"\n      @menu-action=\"handleMenuAction\"\n      @logout=\"handleLogout\"\n      @error=\"handleError\"\n      @search=\"handleSearch\"\n    />\n\n    <!-- Main Content -->\n    <main class=\"main-content\" :class=\"{ 'sidebar-collapsed': sidebarCollapsed }\">\n      <!-- Hero Section -->\n      <section class=\"hero-section\">\n        <div class=\"container\">\n          <div class=\"hero-content\">\n            <div class=\"hero-text\">\n              <h1 class=\"hero-title\">Welcome back, {{ firstName }}!</h1>\n              <p class=\"hero-subtitle\">Access government services and request official documents through our secure digital platform.</p>\n              <div class=\"hero-actions\">\n                <button class=\"btn btn-primary\" @click=\"scrollToServices\">\n                  <i class=\"fas fa-plus-circle\" aria-hidden=\"true\"></i>\n                  Start New Request\n                </button>\n                <button class=\"btn btn-secondary\" @click=\"goToMyRequests\">\n                  <i class=\"fas fa-list-alt\" aria-hidden=\"true\"></i>\n                  View My Requests\n                </button>\n              </div>\n            </div>\n            <div class=\"hero-stats\">\n              <div class=\"stats-grid\">\n                <div class=\"stat-card\">\n                  <div class=\"stat-icon\">\n                    <i class=\"fas fa-file-alt\" aria-hidden=\"true\"></i>\n                  </div>\n                  <div class=\"stat-content\">\n                    <div class=\"stat-number\">{{ totalRequests }}</div>\n                    <div class=\"stat-label\">Total Requests</div>\n                  </div>\n                </div>\n                <div class=\"stat-card\">\n                  <div class=\"stat-icon\">\n                    <i class=\"fas fa-clock\" aria-hidden=\"true\"></i>\n                  </div>\n                  <div class=\"stat-content\">\n                    <div class=\"stat-number\">{{ pendingRequests }}</div>\n                    <div class=\"stat-label\">Pending</div>\n                  </div>\n                </div>\n                <div class=\"stat-card\">\n                  <div class=\"stat-icon\">\n                    <i class=\"fas fa-check-circle\" aria-hidden=\"true\"></i>\n                  </div>\n                  <div class=\"stat-content\">\n                    <div class=\"stat-number\">{{ totalRequests - pendingRequests }}</div>\n                    <div class=\"stat-label\">Completed</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Quick Actions Section -->\n      <section class=\"quick-actions-section\">\n        <div class=\"container\">\n          <div class=\"section-header\">\n            <h2 class=\"section-title\">Quick Actions</h2>\n            <p class=\"section-description\">Common tasks and frequently used services</p>\n          </div>\n\n          <div class=\"quick-actions-grid\">\n            <div class=\"action-card primary\" @click=\"scrollToServices\" role=\"button\" tabindex=\"0\" @keyup.enter=\"scrollToServices\">\n              <div class=\"action-icon\">\n                <i class=\"fas fa-plus-circle\" aria-hidden=\"true\"></i>\n              </div>\n              <div class=\"action-content\">\n                <h3>New Document Request</h3>\n                <p>Start a new request for official documents</p>\n              </div>\n              <div class=\"action-arrow\">\n                <i class=\"fas fa-arrow-right\" aria-hidden=\"true\"></i>\n              </div>\n            </div>\n\n            <div class=\"action-card\" @click=\"goToMyRequests\" role=\"button\" tabindex=\"0\" @keyup.enter=\"goToMyRequests\">\n              <div class=\"action-icon\">\n                <i class=\"fas fa-list-alt\" aria-hidden=\"true\"></i>\n              </div>\n              <div class=\"action-content\">\n                <h3>Track Requests</h3>\n                <p>View status and track your submitted requests</p>\n              </div>\n              <div class=\"action-arrow\">\n                <i class=\"fas fa-arrow-right\" aria-hidden=\"true\"></i>\n              </div>\n            </div>\n\n            <div class=\"action-card\" @click=\"goToProfile\" role=\"button\" tabindex=\"0\" @keyup.enter=\"goToProfile\">\n              <div class=\"action-icon\">\n                <i class=\"fas fa-user-edit\" aria-hidden=\"true\"></i>\n              </div>\n              <div class=\"action-content\">\n                <h3>Update Profile</h3>\n                <p>Manage your account and personal information</p>\n              </div>\n              <div class=\"action-arrow\">\n                <i class=\"fas fa-arrow-right\" aria-hidden=\"true\"></i>\n              </div>\n            </div>\n\n            <div class=\"action-card\" @click=\"contactSupport\" role=\"button\" tabindex=\"0\" @keyup.enter=\"contactSupport\">\n              <div class=\"action-icon\">\n                <i class=\"fas fa-headset\" aria-hidden=\"true\"></i>\n              </div>\n              <div class=\"action-content\">\n                <h3>Get Support</h3>\n                <p>Contact our support team for assistance</p>\n              </div>\n              <div class=\"action-arrow\">\n                <i class=\"fas fa-arrow-right\" aria-hidden=\"true\"></i>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Document Services Section -->\n      <section class=\"services-section\" ref=\"servicesSection\">\n        <div class=\"container\">\n          <div class=\"section-header\">\n            <h2 class=\"section-title\">Available Document Services</h2>\n            <p class=\"section-description\">Select the type of document you need to request</p>\n          </div>\n\n          <!-- Loading State -->\n          <div v-if=\"loading\" class=\"loading-container\" role=\"status\" aria-live=\"polite\">\n            <div class=\"loading-spinner\">\n              <i class=\"fas fa-spinner fa-spin\" aria-hidden=\"true\"></i>\n            </div>\n            <p>Loading available services...</p>\n          </div>\n\n          <!-- Error State -->\n          <div v-else-if=\"error\" class=\"error-container\" role=\"alert\">\n            <div class=\"error-content\">\n              <i class=\"fas fa-exclamation-triangle\" aria-hidden=\"true\"></i>\n              <h3>Unable to Load Services</h3>\n              <p>{{ error }}</p>\n              <button class=\"btn btn-primary retry-btn\" @click=\"loadDocumentTypes\">\n                <i class=\"fas fa-redo\" aria-hidden=\"true\"></i>\n                Try Again\n              </button>\n            </div>\n          </div>\n\n          <!-- Document Types Grid -->\n          <div v-else class=\"document-types-grid\">\n            <div\n              v-for=\"documentType in documentTypes\"\n              :key=\"documentType.id\"\n              class=\"document-card\"\n              @click=\"selectDocumentType(documentType)\"\n              @keyup.enter=\"selectDocumentType(documentType)\"\n              :class=\"{ 'disabled': !documentType.is_active }\"\n              role=\"button\"\n              :tabindex=\"documentType.is_active ? 0 : -1\"\n              :aria-disabled=\"!documentType.is_active\"\n            >\n              <div class=\"document-header\">\n                <div class=\"document-icon\">\n                  <i :class=\"getDocumentIcon(documentType.type_name)\" aria-hidden=\"true\"></i>\n                </div>\n                <div class=\"document-status\">\n                  <span v-if=\"!documentType.is_active\" class=\"status-badge unavailable\">\n                    Unavailable\n                  </span>\n                  <span v-else class=\"status-badge available\">\n                    Available\n                  </span>\n                </div>\n              </div>\n\n              <div class=\"document-content\">\n                <h3 class=\"document-title\">{{ documentType.type_name }}</h3>\n                <p class=\"document-description\">{{ documentType.description }}</p>\n\n                <div class=\"document-details\">\n                  <div class=\"detail-item\">\n                    <i class=\"fas fa-peso-sign\" aria-hidden=\"true\"></i>\n                    <span class=\"detail-label\">Fee:</span>\n                    <span class=\"detail-value fee-amount\">₱{{ formatCurrency(documentType.base_fee) }}</span>\n                  </div>\n\n                  <div class=\"detail-item\">\n                    <i class=\"fas fa-clock\" aria-hidden=\"true\"></i>\n                    <span class=\"detail-label\">Processing:</span>\n                    <span class=\"detail-value\">{{ getProcessingTime(documentType.type_name) }}</span>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"document-action\">\n                <i v-if=\"documentType.is_active\" class=\"fas fa-chevron-right\" aria-hidden=\"true\"></i>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Information and Help Section -->\n      <section class=\"info-section\">\n        <div class=\"container\">\n          <div class=\"info-grid\">\n            <!-- Requirements Card -->\n            <div class=\"info-card requirements-card\">\n              <div class=\"card-header\">\n                <i class=\"fas fa-clipboard-list\" aria-hidden=\"true\"></i>\n                <h3>Before You Start</h3>\n              </div>\n              <div class=\"card-content\">\n                <ul class=\"requirements-list\">\n                  <li class=\"requirement-item\">\n                    <i class=\"fas fa-check-circle\" aria-hidden=\"true\"></i>\n                    <span>Complete and accurate profile information</span>\n                  </li>\n                  <li class=\"requirement-item\">\n                    <i class=\"fas fa-check-circle\" aria-hidden=\"true\"></i>\n                    <span>Valid government-issued ID ready for upload</span>\n                  </li>\n                  <li class=\"requirement-item\">\n                    <i class=\"fas fa-check-circle\" aria-hidden=\"true\"></i>\n                    <span>Supporting documents (if required)</span>\n                  </li>\n                  <li class=\"requirement-item\">\n                    <i class=\"fas fa-check-circle\" aria-hidden=\"true\"></i>\n                    <span>Payment method for processing fees</span>\n                  </li>\n                </ul>\n              </div>\n            </div>\n\n            <!-- Process Card -->\n            <div class=\"info-card process-card\">\n              <div class=\"card-header\">\n                <i class=\"fas fa-route\" aria-hidden=\"true\"></i>\n                <h3>How It Works</h3>\n              </div>\n              <div class=\"card-content\">\n                <ol class=\"process-steps\">\n                  <li class=\"process-step\">\n                    <div class=\"step-number\">1</div>\n                    <div class=\"step-content\">\n                      <strong>Select Document</strong>\n                      <span>Choose the document type you need</span>\n                    </div>\n                  </li>\n                  <li class=\"process-step\">\n                    <div class=\"step-number\">2</div>\n                    <div class=\"step-content\">\n                      <strong>Fill Application</strong>\n                      <span>Complete the required information</span>\n                    </div>\n                  </li>\n                  <li class=\"process-step\">\n                    <div class=\"step-number\">3</div>\n                    <div class=\"step-content\">\n                      <strong>Submit & Pay</strong>\n                      <span>Review and submit with payment</span>\n                    </div>\n                  </li>\n                  <li class=\"process-step\">\n                    <div class=\"step-number\">4</div>\n                    <div class=\"step-content\">\n                      <strong>Track Progress</strong>\n                      <span>Monitor your request status</span>\n                    </div>\n                  </li>\n                </ol>\n              </div>\n            </div>\n\n            <!-- Help Card -->\n            <div class=\"info-card help-card\">\n              <div class=\"card-header\">\n                <i class=\"fas fa-headset\" aria-hidden=\"true\"></i>\n                <h3>Need Assistance?</h3>\n              </div>\n              <div class=\"card-content\">\n                <p class=\"help-description\">Our support team is here to help you with any questions about document requirements or the application process.</p>\n                <div class=\"help-actions\">\n                  <button class=\"btn btn-outline help-btn\" @click=\"openHelp\">\n                    <i class=\"fas fa-question-circle\" aria-hidden=\"true\"></i>\n                    View FAQ\n                  </button>\n                  <button class=\"btn btn-outline contact-btn\" @click=\"contactSupport\">\n                    <i class=\"fas fa-phone\" aria-hidden=\"true\"></i>\n                    Contact Support\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n    </main>\n  </div>\n</template>\n\n<script>\nimport documentRequestService from '@/services/documentRequestService';\nimport ClientHeader from './ClientHeader.vue';\nimport unifiedAuthService from '@/services/unifiedAuthService';\n\nexport default {\n  name: 'NewDocumentRequest',\n  components: {\n    ClientHeader\n  },\n  data() {\n    return {\n      documentTypes: [],\n      loading: true,\n      error: null,\n      // Header state\n      showUserDropdown: false,\n      sidebarCollapsed: false,\n      activeMenu: 'dashboard',\n      // User data\n      userName: 'User',\n      userEmail: '<EMAIL>',\n      userAvatar: null,\n      firstName: 'User',\n      totalRequests: 0,\n      pendingRequests: 0\n    };\n  },\n  async mounted() {\n    await this.loadUserData();\n    await this.loadDocumentTypes();\n    await this.loadUserStats();\n  },\n  methods: {\n    async loadUserData() {\n      try {\n        const currentUser = unifiedAuthService.getCurrentUser();\n        if (currentUser) {\n          this.userName = currentUser.username || 'User';\n          this.userEmail = currentUser.email || '<EMAIL>';\n          this.firstName = currentUser.first_name || currentUser.username || 'User';\n          // Set user avatar if available\n          this.userAvatar = currentUser.avatar || null;\n        }\n      } catch (error) {\n        console.error('Error loading user data:', error);\n      }\n    },\n\n    async loadUserStats() {\n      try {\n        // TODO: Implement API call to get user statistics\n        // For now, using placeholder data\n        this.totalRequests = 5;\n        this.pendingRequests = 2;\n      } catch (error) {\n        console.error('Error loading user stats:', error);\n      }\n    },\n\n    async loadDocumentTypes() {\n      try {\n        this.loading = true;\n        this.error = null;\n\n        const response = await documentRequestService.getDocumentTypes();\n        this.documentTypes = response.data || [];\n\n      } catch (error) {\n        console.error('Error loading document types:', error);\n        this.error = error.response?.data?.message || 'Failed to load available services';\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    selectDocumentType(documentType) {\n      if (!documentType.is_active) return;\n\n      // Navigate to specific document request form\n      const routeName = this.getRouteForDocumentType(documentType.type_name);\n      if (routeName) {\n        this.$router.push({ \n          name: routeName,\n          params: { documentTypeId: documentType.id }\n        });\n      }\n    },\n\n    getRouteForDocumentType(typeName) {\n      const routes = {\n        'Barangay Clearance': 'BarangayClearanceRequest',\n        'Cedula': 'CedulaRequest'\n      };\n      return routes[typeName];\n    },\n\n    getDocumentIcon(typeName) {\n      const icons = {\n        'Barangay Clearance': 'fas fa-certificate',\n        'Cedula': 'fas fa-id-card'\n      };\n      return icons[typeName] || 'fas fa-file-alt';\n    },\n\n    getProcessingTime(typeName) {\n      const times = {\n        'Barangay Clearance': '3-5 business days',\n        'Cedula': '1-2 business days'\n      };\n      return times[typeName] || '3-5 business days';\n    },\n\n    formatCurrency(amount) {\n      return parseFloat(amount).toFixed(2);\n    },\n\n    // Header event handlers\n    handleSidebarToggle() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n    },\n\n    handleUserDropdownToggle() {\n      this.showUserDropdown = !this.showUserDropdown;\n    },\n\n    handleMenuAction(action) {\n      console.log('Menu action:', action);\n      switch (action) {\n        case 'profile':\n          // TODO: Navigate to profile page\n          break;\n        case 'settings':\n          // TODO: Navigate to settings page\n          break;\n        case 'account':\n          // TODO: Navigate to account page\n          break;\n      }\n    },\n\n    handleLogout() {\n      try {\n        unifiedAuthService.logout();\n        this.$router.push({ name: 'WelcomePage' });\n      } catch (error) {\n        console.error('Logout error:', error);\n      }\n    },\n\n    handleError(error) {\n      console.error('Header error:', error);\n    },\n\n    // Search handler\n    handleSearch(query) {\n      console.log('Search query:', query);\n      // TODO: Implement search functionality\n      // This could search through documents, services, or requests\n      // For now, we'll just log the query\n    },\n\n    // Navigation methods\n    scrollToServices() {\n      this.$refs.servicesSection?.scrollIntoView({ behavior: 'smooth' });\n    },\n\n    goToMyRequests() {\n      this.$router.push({ name: 'MyRequests' });\n    },\n\n    goToProfile() {\n      // TODO: Navigate to profile page\n      console.log('Navigate to profile');\n    },\n\n    goBack() {\n      this.$router.push({ name: 'ClientDashboard' });\n    },\n\n    openHelp() {\n      // TODO: Implement help/FAQ modal or page\n      console.log('Opening help...');\n    },\n\n    contactSupport() {\n      // TODO: Implement contact support functionality\n      console.log('Contacting support...');\n      // Could open a modal, redirect to contact page, or open email client\n      // For now, we'll just log the action\n    }\n  }\n};\n</script>\n\n<style scoped>\n/* Modern Government Portal Styles - USWDS Inspired */\n:root {\n  /* Government Colors */\n  --gov-blue: #005ea2;\n  --gov-blue-dark: #0f4c96;\n  --gov-blue-light: #2378c3;\n  --gov-blue-lighter: #e7f6f8;\n  --gov-red: #d63384;\n  --gov-green: #00a91c;\n  --gov-yellow: #ffbe2e;\n  --gov-yellow-light: #fef0cd;\n\n  /* Neutral Colors */\n  --text-primary: #1b1b1b;\n  --text-secondary: #454545;\n  --text-light: #757575;\n  --text-white: #ffffff;\n\n  /* Background Colors */\n  --bg-white: #ffffff;\n  --bg-gray-5: #f9f9f9;\n  --bg-gray-10: #f0f0f0;\n  --bg-gray-20: #dfe1e2;\n\n  /* Shadows */\n  --shadow-1: 0 1px 2px 0 rgba(0, 0, 0, 0.1);\n  --shadow-2: 0 1px 4px 0 rgba(0, 0, 0, 0.1);\n  --shadow-3: 0 4px 8px 0 rgba(0, 0, 0, 0.1);\n  --shadow-4: 0 8px 16px 0 rgba(0, 0, 0, 0.1);\n\n  /* Typography */\n  --font-family-sans: 'Source Sans Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n  --font-family-serif: 'Merriweather', Georgia, serif;\n\n  /* Spacing */\n  --spacing-1: 0.25rem;\n  --spacing-2: 0.5rem;\n  --spacing-3: 0.75rem;\n  --spacing-4: 1rem;\n  --spacing-5: 1.25rem;\n  --spacing-6: 1.5rem;\n  --spacing-8: 2rem;\n  --spacing-10: 2.5rem;\n  --spacing-12: 3rem;\n  --spacing-16: 4rem;\n\n  /* Border Radius */\n  --border-radius-sm: 0.125rem;\n  --border-radius-md: 0.25rem;\n  --border-radius-lg: 0.5rem;\n  --border-radius-xl: 1rem;\n\n  /* Transitions */\n  --transition-fast: 0.15s ease-in-out;\n  --transition-base: 0.2s ease-in-out;\n}\n\n/* Reset and base styles */\n* {\n  box-sizing: border-box;\n}\n\n.client-dashboard {\n  font-family: var(--font-family-sans);\n  line-height: 1.6;\n  color: var(--text-primary);\n  background: var(--bg-gray-5);\n  min-height: 100vh;\n}\n\n/* Background Setup */\n.background-container {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: -2;\n}\n\n.background-image {\n  width: 100%;\n  height: 100%;\n  background-image: url('@/assets/bula-request-background-pic.png');\n  background-size: cover;\n  background-position: center;\n  background-repeat: no-repeat;\n  background-attachment: fixed;\n  opacity: 0.03;\n}\n\n.background-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    135deg,\n    rgba(0, 94, 162, 0.02) 0%,\n    rgba(35, 120, 195, 0.03) 100%\n  );\n  z-index: -1;\n}\n\n/* Container utility */\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 var(--spacing-4);\n}\n\n/* Main Content */\n.main-content {\n  margin-top: 140px; /* Account for new fixed header (gov banner + main header + breadcrumb) */\n  transition: margin-left var(--transition-base);\n  padding-bottom: var(--spacing-16);\n}\n\n.main-content.sidebar-collapsed {\n  margin-left: 0;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .hero-content {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-6);\n    padding: var(--spacing-6);\n  }\n\n  .hero-title {\n    font-size: clamp(1.5rem, 6vw, 2rem);\n  }\n\n  .hero-actions {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .stats-grid {\n    grid-template-columns: repeat(3, 1fr);\n    gap: var(--spacing-2);\n  }\n\n  .stat-card {\n    padding: var(--spacing-3);\n    min-height: 100px;\n  }\n\n  .stat-number {\n    font-size: 1.5rem;\n  }\n\n  .quick-actions-grid {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-3);\n  }\n\n  .action-card {\n    padding: var(--spacing-4);\n    gap: var(--spacing-3);\n  }\n\n  .action-icon {\n    width: 48px;\n    height: 48px;\n    font-size: 1.25rem;\n  }\n\n  .hero-section,\n  .quick-actions-section {\n    margin: var(--spacing-2);\n  }\n}\n\n/* Modern Button Styles */\n.btn {\n  display: inline-flex;\n  align-items: center;\n  gap: var(--spacing-2);\n  padding: var(--spacing-3) var(--spacing-6);\n  font-family: var(--font-family-sans);\n  font-size: 0.875rem;\n  font-weight: 600;\n  line-height: 1.2;\n  text-decoration: none;\n  border: 2px solid transparent;\n  border-radius: var(--border-radius-md);\n  cursor: pointer;\n  transition: all var(--transition-fast);\n  white-space: nowrap;\n}\n\n.btn:focus {\n  outline: 2px solid var(--gov-yellow);\n  outline-offset: 2px;\n}\n\n.btn-primary {\n  background-color: var(--gov-blue);\n  color: var(--text-white);\n  border-color: var(--gov-blue);\n}\n\n.btn-primary:hover {\n  background-color: var(--gov-blue-dark);\n  border-color: var(--gov-blue-dark);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-3);\n}\n\n.btn-secondary {\n  background-color: var(--bg-white);\n  color: var(--gov-blue);\n  border-color: var(--gov-blue);\n}\n\n.btn-secondary:hover {\n  background-color: var(--gov-blue);\n  color: var(--text-white);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-3);\n}\n\n.btn-outline {\n  background-color: transparent;\n  color: var(--gov-blue);\n  border-color: var(--gov-blue);\n}\n\n.btn-outline:hover {\n  background-color: var(--gov-blue);\n  color: var(--text-white);\n}\n\n/* Hero Section */\n.hero-section {\n  padding: var(--spacing-8) 0;\n  position: relative;\n  z-index: 1;\n  background: var(--bg-white);\n  margin: var(--spacing-4);\n  border-radius: var(--border-radius-xl);\n  box-shadow: var(--shadow-2);\n}\n\n.hero-content {\n  display: grid;\n  grid-template-columns: 1fr auto;\n  gap: var(--spacing-8);\n  align-items: start;\n  padding: var(--spacing-8);\n}\n\n.hero-text {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-4);\n}\n\n.hero-title {\n  font-size: clamp(1.75rem, 4vw, 2.5rem);\n  font-weight: 700;\n  color: var(--gov-blue);\n  margin: 0;\n  line-height: 1.2;\n  font-family: var(--font-family-serif);\n}\n\n.hero-subtitle {\n  font-size: 1.125rem;\n  color: var(--text-secondary);\n  margin: 0;\n  line-height: 1.5;\n  max-width: 600px;\n}\n\n.hero-actions {\n  display: flex;\n  gap: var(--spacing-3);\n  flex-wrap: wrap;\n  margin-top: var(--spacing-2);\n}\n\n.hero-stats {\n  display: flex;\n  justify-content: center;\n  min-width: 280px;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: var(--spacing-3);\n  width: 100%;\n}\n\n.stat-card {\n  background: linear-gradient(135deg, var(--gov-blue), var(--gov-blue-light));\n  border-radius: var(--border-radius-lg);\n  padding: var(--spacing-4);\n  color: var(--text-white);\n  text-align: center;\n  flex: 1;\n  box-shadow: var(--shadow-2);\n  transition: all var(--transition-fast);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  min-height: 120px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n}\n\n.stat-card:hover {\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-3);\n}\n\n.stat-icon {\n  font-size: 1.5rem;\n  color: var(--gov-yellow);\n  margin-bottom: var(--spacing-2);\n  display: block;\n}\n\n.stat-number {\n  font-size: 1.75rem;\n  font-weight: 700;\n  margin-bottom: var(--spacing-1);\n  color: var(--gov-yellow);\n  font-family: var(--font-family-serif);\n  line-height: 1;\n}\n\n.stat-label {\n  font-size: 0.75rem;\n  opacity: 0.9;\n  font-weight: 500;\n  line-height: 1.2;\n}\n\n/* Quick Actions Section */\n.quick-actions-section {\n  padding: var(--spacing-8) 0;\n  position: relative;\n  z-index: 1;\n  background: var(--bg-gray-5);\n  margin: var(--spacing-4);\n  border-radius: var(--border-radius-xl);\n}\n\n.section-header {\n  text-align: center;\n  margin-bottom: var(--spacing-8);\n  padding: 0 var(--spacing-4);\n}\n\n.section-title {\n  font-size: clamp(1.75rem, 4vw, 2.25rem);\n  font-weight: 700;\n  color: var(--gov-blue);\n  margin-bottom: var(--spacing-3);\n  font-family: var(--font-family-serif);\n}\n\n.section-description {\n  font-size: 1rem;\n  color: var(--text-secondary);\n  margin: 0;\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n.quick-actions-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));\n  gap: var(--spacing-4);\n  padding: 0 var(--spacing-4);\n}\n\n.action-card {\n  background: var(--bg-white);\n  border-radius: var(--border-radius-lg);\n  padding: var(--spacing-6);\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-4);\n  cursor: pointer;\n  transition: all var(--transition-fast);\n  border: 2px solid var(--bg-gray-20);\n  box-shadow: var(--shadow-1);\n}\n\n.action-card:hover,\n.action-card:focus {\n  transform: translateY(-2px);\n  box-shadow: var(--shadow-3);\n  border-color: var(--gov-blue);\n  outline: none;\n}\n\n.action-card.primary {\n  background: linear-gradient(135deg, var(--gov-blue), var(--gov-blue-light));\n  color: var(--text-white);\n  border-color: var(--gov-blue);\n}\n\n.action-card.primary:hover,\n.action-card.primary:focus {\n  border-color: var(--gov-yellow);\n  box-shadow: var(--shadow-4);\n}\n\n.action-icon {\n  width: 4rem;\n  height: 4rem;\n  background: linear-gradient(135deg, #fbbf24, #f59e0b);\n  border-radius: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.5rem;\n  color: #1e3a8a;\n  flex-shrink: 0;\n}\n\n.action-card.primary .action-icon {\n  background: rgba(251, 191, 36, 0.2);\n  color: #fbbf24;\n}\n\n.action-content {\n  flex: 1;\n}\n\n.action-content h3 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  color: inherit;\n}\n\n.action-content p {\n  margin: 0;\n  opacity: 0.8;\n  line-height: 1.5;\n}\n\n.action-arrow {\n  color: #6b7280;\n  font-size: 1.25rem;\n}\n\n.action-card.primary .action-arrow {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n/* Services Section */\n.services-section {\n  padding: 2rem 0;\n  position: relative;\n  z-index: 1;\n}\n\n.services-section .section-title {\n  color: white;\n}\n\n.services-section .section-description {\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.loading-container, .error-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n  text-align: center;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: 1rem;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.loading-spinner i {\n  font-size: 3rem;\n  color: #1e3a8a;\n  margin-bottom: 1rem;\n}\n\n.error-content {\n  max-width: 400px;\n}\n\n.error-content i {\n  font-size: 3rem;\n  color: #dc2626;\n  margin-bottom: 1rem;\n}\n\n.retry-btn {\n  background: linear-gradient(135deg, #1e3a8a, #1e40af);\n  color: white;\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 0.75rem;\n  cursor: pointer;\n  margin-top: 1rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);\n}\n\n.retry-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.4);\n}\n\n.document-types-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n  gap: 1.5rem;\n}\n\n.document-card {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border: 2px solid rgba(30, 58, 138, 0.2);\n  border-radius: 1rem;\n  padding: 2rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n}\n\n.document-card:hover:not(.disabled) {\n  border-color: #1e3a8a;\n  transform: translateY(-5px);\n  box-shadow: 0 12px 30px rgba(30, 58, 138, 0.2);\n  background: rgba(255, 255, 255, 1);\n}\n\n.document-card.disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.document-icon {\n  flex-shrink: 0;\n  width: 4rem;\n  height: 4rem;\n  background: linear-gradient(135deg, #1e3a8a, #1e40af);\n  border-radius: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 1.5rem;\n  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);\n}\n\n.document-content {\n  flex: 1;\n}\n\n.document-title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1e3a8a;\n  margin-bottom: 0.5rem;\n}\n\n.document-description {\n  color: #6b7280;\n  margin-bottom: 1rem;\n  line-height: 1.5;\n}\n\n.document-details {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.fee-info {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.fee-label {\n  color: #9ca3af;\n  font-size: 0.9rem;\n}\n\n.fee-amount {\n  font-weight: 600;\n  color: #059669;\n  font-size: 1.1rem;\n}\n\n.processing-time {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #9ca3af;\n  font-size: 0.9rem;\n}\n\n.document-action {\n  flex-shrink: 0;\n  display: flex;\n  align-items: center;\n}\n\n.document-action i {\n  color: #d1d5db;\n  font-size: 1.25rem;\n}\n\n.status-badge.unavailable {\n  background: #fecaca;\n  color: #dc2626;\n  padding: 0.25rem 0.75rem;\n  border-radius: 1rem;\n  font-size: 0.8rem;\n  font-weight: 500;\n}\n\n/* Info Section */\n.info-section {\n  padding: 2rem 0;\n  position: relative;\n  z-index: 1;\n}\n\n.info-grid {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 2rem;\n}\n\n.info-card, .help-card {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(30, 58, 138, 0.2);\n  border-radius: 1rem;\n  padding: 2rem;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n}\n\n.info-card:hover, .help-card:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 8px 30px rgba(30, 58, 138, 0.15);\n  background: rgba(255, 255, 255, 1);\n  border-color: #1e3a8a;\n}\n\n.info-header, .help-header {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  margin-bottom: 1.5rem;\n}\n\n.info-header h3, .help-header h3 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1e3a8a;\n  margin: 0;\n}\n\n.info-header i {\n  color: #1e3a8a;\n  font-size: 1.25rem;\n}\n\n.help-header i {\n  color: #059669;\n  font-size: 1.25rem;\n}\n\n.info-list {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.info-list li {\n  display: flex;\n  align-items: flex-start;\n  gap: 0.75rem;\n  margin-bottom: 1rem;\n  color: #6b7280;\n  line-height: 1.6;\n}\n\n.info-list i {\n  color: #059669;\n  margin-top: 0.125rem;\n  flex-shrink: 0;\n}\n\n.help-content p {\n  color: #6b7280;\n  margin-bottom: 1.5rem;\n  line-height: 1.6;\n}\n\n.help-actions {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.help-btn, .contact-btn {\n  background: rgba(255, 255, 255, 0.9);\n  backdrop-filter: blur(5px);\n  border: 2px solid rgba(30, 58, 138, 0.3);\n  padding: 0.875rem 1.25rem;\n  border-radius: 0.75rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  color: #6b7280;\n  font-weight: 500;\n  font-size: 0.95rem;\n}\n\n.help-btn:hover {\n  border-color: #1e3a8a;\n  color: #1e3a8a;\n  background: rgba(30, 58, 138, 0.1);\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.2);\n}\n\n.contact-btn:hover {\n  border-color: #059669;\n  color: #059669;\n  background: rgba(5, 150, 105, 0.1);\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(5, 150, 105, 0.2);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .container {\n    padding: 0 0.75rem;\n  }\n\n  .welcome-header {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n    padding: 1.5rem;\n  }\n\n  .welcome-stats {\n    justify-content: center;\n  }\n\n  .stat-card {\n    min-width: 120px;\n  }\n\n  .quick-actions-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n\n  .action-card {\n    padding: 1.5rem;\n  }\n\n  .document-types-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n\n  .document-card {\n    flex-direction: column;\n    text-align: center;\n    padding: 1.5rem;\n  }\n\n  .info-grid {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n\n  .info-card, .help-card {\n    padding: 1.5rem;\n  }\n\n  .loading-container, .error-container {\n    padding: 3rem 1.5rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .container {\n    padding: 0 0.5rem;\n  }\n\n  .welcome-section,\n  .quick-actions-section,\n  .services-section,\n  .info-section {\n    padding: 1.5rem 0;\n  }\n\n  .welcome-header {\n    padding: 1rem;\n  }\n\n  .stat-card {\n    padding: 1rem;\n  }\n\n  .stat-number {\n    font-size: 1.5rem;\n  }\n\n  .action-card {\n    padding: 1rem;\n    flex-direction: column;\n    text-align: center;\n    gap: 1rem;\n  }\n\n  .action-icon {\n    width: 3rem;\n    height: 3rem;\n  }\n\n  .document-card {\n    padding: 1rem;\n  }\n\n  .info-card, .help-card {\n    padding: 1rem;\n  }\n\n  .loading-container, .error-container {\n    padding: 2rem 1rem;\n  }\n}\n\n/* Animations */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.welcome-section,\n.quick-actions-section,\n.services-section,\n.info-section {\n  animation: fadeInUp 0.8s ease-out;\n}\n\n.quick-actions-section {\n  animation-delay: 0.2s;\n}\n\n.services-section {\n  animation-delay: 0.4s;\n}\n\n.info-section {\n  animation-delay: 0.6s;\n}\n\n/* Accessibility */\n@media (prefers-reduced-motion: reduce) {\n  * {\n    animation: none !important;\n    transition: none !important;\n  }\n\n  .action-card:hover,\n  .document-card:hover,\n  .info-card:hover,\n  .help-card:hover {\n    transform: none;\n  }\n}\n\n/* Focus styles */\n.action-card:focus,\n.document-card:focus,\n.help-btn:focus,\n.contact-btn:focus,\n.retry-btn:focus {\n  outline: 3px solid #fbbf24;\n  outline-offset: 2px;\n}\n</style>\n"], "mappings": ";AAuUA,OAAOA,sBAAqB,MAAO,mCAAmC;AACtE,OAAOC,YAAW,MAAO,oBAAoB;AAC7C,OAAOC,kBAAiB,MAAO,+BAA+B;AAE9D,eAAe;EACbC,IAAI,EAAE,oBAAoB;EAC1BC,UAAU,EAAE;IACVH;EACF,CAAC;EACDI,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,aAAa,EAAE,EAAE;MACjBC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,IAAI;MACX;MACAC,gBAAgB,EAAE,KAAK;MACvBC,gBAAgB,EAAE,KAAK;MACvBC,UAAU,EAAE,WAAW;MACvB;MACAC,QAAQ,EAAE,MAAM;MAChBC,SAAS,EAAE,kBAAkB;MAC7BC,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAE,MAAM;MACjBC,aAAa,EAAE,CAAC;MAChBC,eAAe,EAAE;IACnB,CAAC;EACH,CAAC;EACD,MAAMC,OAAOA,CAAA,EAAG;IACd,MAAM,IAAI,CAACC,YAAY,CAAC,CAAC;IACzB,MAAM,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAC9B,MAAM,IAAI,CAACC,aAAa,CAAC,CAAC;EAC5B,CAAC;EACDC,OAAO,EAAE;IACP,MAAMH,YAAYA,CAAA,EAAG;MACnB,IAAI;QACF,MAAMI,WAAU,GAAIrB,kBAAkB,CAACsB,cAAc,CAAC,CAAC;QACvD,IAAID,WAAW,EAAE;UACf,IAAI,CAACX,QAAO,GAAIW,WAAW,CAACE,QAAO,IAAK,MAAM;UAC9C,IAAI,CAACZ,SAAQ,GAAIU,WAAW,CAACG,KAAI,IAAK,kBAAkB;UACxD,IAAI,CAACX,SAAQ,GAAIQ,WAAW,CAACI,UAAS,IAAKJ,WAAW,CAACE,QAAO,IAAK,MAAM;UACzE;UACA,IAAI,CAACX,UAAS,GAAIS,WAAW,CAACK,MAAK,IAAK,IAAI;QAC9C;MACF,EAAE,OAAOpB,KAAK,EAAE;QACdqB,OAAO,CAACrB,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;IACF,CAAC;IAED,MAAMa,aAAaA,CAAA,EAAG;MACpB,IAAI;QACF;QACA;QACA,IAAI,CAACL,aAAY,GAAI,CAAC;QACtB,IAAI,CAACC,eAAc,GAAI,CAAC;MAC1B,EAAE,OAAOT,KAAK,EAAE;QACdqB,OAAO,CAACrB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;IACF,CAAC;IAED,MAAMY,iBAAiBA,CAAA,EAAG;MACxB,IAAI;QACF,IAAI,CAACb,OAAM,GAAI,IAAI;QACnB,IAAI,CAACC,KAAI,GAAI,IAAI;QAEjB,MAAMsB,QAAO,GAAI,MAAM9B,sBAAsB,CAAC+B,gBAAgB,CAAC,CAAC;QAChE,IAAI,CAACzB,aAAY,GAAIwB,QAAQ,CAACzB,IAAG,IAAK,EAAE;MAE1C,EAAE,OAAOG,KAAK,EAAE;QACdqB,OAAO,CAACrB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,IAAI,CAACA,KAAI,GAAIA,KAAK,CAACsB,QAAQ,EAAEzB,IAAI,EAAE2B,OAAM,IAAK,mCAAmC;MACnF,UAAU;QACR,IAAI,CAACzB,OAAM,GAAI,KAAK;MACtB;IACF,CAAC;IAED0B,kBAAkBA,CAACC,YAAY,EAAE;MAC/B,IAAI,CAACA,YAAY,CAACC,SAAS,EAAE;;MAE7B;MACA,MAAMC,SAAQ,GAAI,IAAI,CAACC,uBAAuB,CAACH,YAAY,CAACI,SAAS,CAAC;MACtE,IAAIF,SAAS,EAAE;QACb,IAAI,CAACG,OAAO,CAACC,IAAI,CAAC;UAChBrC,IAAI,EAAEiC,SAAS;UACfK,MAAM,EAAE;YAAEC,cAAc,EAAER,YAAY,CAACS;UAAG;QAC5C,CAAC,CAAC;MACJ;IACF,CAAC;IAEDN,uBAAuBA,CAACO,QAAQ,EAAE;MAChC,MAAMC,MAAK,GAAI;QACb,oBAAoB,EAAE,0BAA0B;QAChD,QAAQ,EAAE;MACZ,CAAC;MACD,OAAOA,MAAM,CAACD,QAAQ,CAAC;IACzB,CAAC;IAEDE,eAAeA,CAACF,QAAQ,EAAE;MACxB,MAAMG,KAAI,GAAI;QACZ,oBAAoB,EAAE,oBAAoB;QAC1C,QAAQ,EAAE;MACZ,CAAC;MACD,OAAOA,KAAK,CAACH,QAAQ,KAAK,iBAAiB;IAC7C,CAAC;IAEDI,iBAAiBA,CAACJ,QAAQ,EAAE;MAC1B,MAAMK,KAAI,GAAI;QACZ,oBAAoB,EAAE,mBAAmB;QACzC,QAAQ,EAAE;MACZ,CAAC;MACD,OAAOA,KAAK,CAACL,QAAQ,KAAK,mBAAmB;IAC/C,CAAC;IAEDM,cAAcA,CAACC,MAAM,EAAE;MACrB,OAAOC,UAAU,CAACD,MAAM,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC;IACtC,CAAC;IAED;IACAC,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAAC5C,gBAAe,GAAI,CAAC,IAAI,CAACA,gBAAgB;IAChD,CAAC;IAED6C,wBAAwBA,CAAA,EAAG;MACzB,IAAI,CAAC9C,gBAAe,GAAI,CAAC,IAAI,CAACA,gBAAgB;IAChD,CAAC;IAED+C,gBAAgBA,CAACC,MAAM,EAAE;MACvB5B,OAAO,CAAC6B,GAAG,CAAC,cAAc,EAAED,MAAM,CAAC;MACnC,QAAQA,MAAM;QACZ,KAAK,SAAS;UACZ;UACA;QACF,KAAK,UAAU;UACb;UACA;QACF,KAAK,SAAS;UACZ;UACA;MACJ;IACF,CAAC;IAEDE,YAAYA,CAAA,EAAG;MACb,IAAI;QACFzD,kBAAkB,CAAC0D,MAAM,CAAC,CAAC;QAC3B,IAAI,CAACrB,OAAO,CAACC,IAAI,CAAC;UAAErC,IAAI,EAAE;QAAc,CAAC,CAAC;MAC5C,EAAE,OAAOK,KAAK,EAAE;QACdqB,OAAO,CAACrB,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACvC;IACF,CAAC;IAEDqD,WAAWA,CAACrD,KAAK,EAAE;MACjBqB,OAAO,CAACrB,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC;IAED;IACAsD,YAAYA,CAACC,KAAK,EAAE;MAClBlC,OAAO,CAAC6B,GAAG,CAAC,eAAe,EAAEK,KAAK,CAAC;MACnC;MACA;MACA;IACF,CAAC;IAED;IACAC,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAACC,KAAK,CAACC,eAAe,EAAEC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IACpE,CAAC;IAEDC,cAAcA,CAAA,EAAG;MACf,IAAI,CAAC9B,OAAO,CAACC,IAAI,CAAC;QAAErC,IAAI,EAAE;MAAa,CAAC,CAAC;IAC3C,CAAC;IAEDmE,WAAWA,CAAA,EAAG;MACZ;MACAzC,OAAO,CAAC6B,GAAG,CAAC,qBAAqB,CAAC;IACpC,CAAC;IAEDa,MAAMA,CAAA,EAAG;MACP,IAAI,CAAChC,OAAO,CAACC,IAAI,CAAC;QAAErC,IAAI,EAAE;MAAkB,CAAC,CAAC;IAChD,CAAC;IAEDqE,QAAQA,CAAA,EAAG;MACT;MACA3C,OAAO,CAAC6B,GAAG,CAAC,iBAAiB,CAAC;IAChC,CAAC;IAEDe,cAAcA,CAAA,EAAG;MACf;MACA5C,OAAO,CAAC6B,GAAG,CAAC,uBAAuB,CAAC;MACpC;MACA;IACF;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}