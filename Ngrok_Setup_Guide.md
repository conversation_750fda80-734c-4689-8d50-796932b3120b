# 🚀 Complete Ngrok Setup Guide for PayMongo Webhooks

## Table of Contents
1. [Download & Install Ngrok](#step-1-download--install-ngrok)
2. [Create Account & Get Token](#step-2-create-ngrok-account--get-token)
3. [Configure <PERSON><PERSON>](#step-3-configure-ngrok-with-your-token)
4. [Start Backend Server](#step-4-start-your-backend-server)
5. [Start Ngrok Tunnel](#step-5-start-ngrok-tunnel)
6. [Update Configuration Files](#step-6-update-your-configuration-files)
7. [Create PayMongo Webhook](#step-7-create-paymongo-webhook)
8. [Update Webhook Secret](#step-8-update-webhook-secret)
9. [Restart Backend](#step-9-restart-backend-server)
10. [Test Setup](#step-10-test-the-setup)
11. [Complete Example](#complete-example-workflow)
12. [Restart Procedure](#when-you-restart-important)
13. [Pro Tips](#pro-tips)
14. [Troubleshooting](#troubleshooting)

---

## 📥 Step 1: Download & Install Ngrok

### Option A: Download from Website
1. Go to **https://ngrok.com/download**
2. Click **"Download for Windows"**
3. Extract the `ngrok.exe` file to a folder
   - Recommended: `C:\ngrok\` or keep in `Downloads`
   - Example path: `C:\Users\<USER>\Downloads\ngrok-v3-stable-windows-amd64`

### Option B: Using Package Managers (Alternative)
```powershell
# Using Chocolatey
choco install ngrok

# Using Scoop
scoop install ngrok
```

---

## 🔐 Step 2: Create Ngrok Account & Get Token

1. **Sign up**: Go to **https://dashboard.ngrok.com/signup**
2. **Create account** with email/password or use Google/GitHub
3. **Get your token**: After signup, go to **https://dashboard.ngrok.com/get-started/your-authtoken**
4. **Copy the token** (looks like: `2abc123def456ghi789jkl_1MnOpQrStUvWxYz`)

**⚠️ Important**: Keep this token safe - you'll need it for configuration!

---

## ⚙️ Step 3: Configure Ngrok with Your Token

Open **PowerShell** or **Command Prompt** and navigate to where you extracted ngrok:

```powershell
# Navigate to ngrok folder
cd C:\Users\<USER>\Downloads\ngrok-v3-stable-windows-amd64

# Configure with your token (replace with your actual token)
.\ngrok.exe config add-authtoken YOUR_TOKEN_HERE
```

**Example:**
```powershell
.\ngrok.exe config add-authtoken 2abc123def456ghi789jkl_1MnOpQrStUvWxYz
```

**Success message**: You should see a confirmation that the authtoken was added.

---

## 🚀 Step 4: Start Your Backend Server

**Before starting ngrok**, make sure your backend is running:

```powershell
# Navigate to your backend folder
cd D:\cap2_rhai_front_and_back\rhai_backend

# Start the server
npm run dev
```

**Wait until you see**: `🚀 Server is running on port 7000`

**⚠️ Keep this terminal open** - your server must stay running!

---

## 🌐 Step 5: Start Ngrok Tunnel

Open a **NEW** PowerShell window (don't close the backend server):

```powershell
# Navigate to ngrok folder
cd C:\Users\<USER>\Downloads\ngrok-v3-stable-windows-amd64

# Start tunnel for port 7000
.\ngrok.exe http 7000
```

**You'll see output like:**
```
Session Status                online
Account                       <EMAIL> (Plan: Free)
Forwarding                    https://abc123def456.ngrok-free.app -> http://localhost:7000
```

**📝 COPY THE HTTPS URL** (e.g., `https://abc123def456.ngrok-free.app`)

**⚠️ Keep this terminal open** - ngrok must stay running!

---

## 🔧 Step 6: Update Your Configuration Files

### A. Update `.env` file:
1. Navigate to your backend folder: `D:\cap2_rhai_front_and_back\rhai_backend`
2. Open `rhai_backend\.env` file
3. Find the line: `WEBHOOK_URL=...`
4. Update it with your new ngrok URL:

```env
WEBHOOK_URL=https://YOUR_NEW_NGROK_URL.ngrok-free.app/api/webhooks/paymongo
```

**Example:**
```env
WEBHOOK_URL=https://abc123def456.ngrok-free.app/api/webhooks/paymongo
```

### B. Update `paymongo_webhook_manager.js`:
1. Open `rhai_backend\paymongo_webhook_manager.js`
2. Find line 6: `const WEBHOOK_URL = '...'`
3. Update it with your new ngrok URL:

```javascript
const WEBHOOK_URL = 'https://YOUR_NEW_NGROK_URL.ngrok-free.app/api/webhooks/paymongo';
```

---

## 🔗 Step 7: Create PayMongo Webhook

Open a **third** PowerShell window:

```powershell
# Navigate to backend folder
cd D:\cap2_rhai_front_and_back\rhai_backend

# List existing webhooks (to see what's there)
node paymongo_webhook_manager.js list

# Delete old webhooks if any (replace with actual webhook ID)
node paymongo_webhook_manager.js delete hook_OLD_WEBHOOK_ID

# Create new webhook
node paymongo_webhook_manager.js create
```

**After creating**, you'll get output like:
```
✅ Webhook created successfully!
📋 Webhook Details:
   ID: hook_NzWTGvTKcEkuMmEW29qP85Zb
   Secret: whsk_pLUE6a2nzFbVSRUbjhEdYEq7
```

**📝 COPY THE SECRET** (the `whsk_...` part)

---

## 🔑 Step 8: Update Webhook Secret

Update your `.env` file with the new webhook secret:

1. Open `rhai_backend\.env`
2. Find: `PAYMONGO_WEBHOOK_SECRET=...`
3. Replace with your new secret:

```env
PAYMONGO_WEBHOOK_SECRET=whsk_pLUE6a2nzFbVSRUbjhEdYEq7
```

---

## 🔄 Step 9: Restart Backend Server

1. Go back to your **backend server terminal**
2. **Stop the server**: Press `Ctrl+C`
3. **Restart it**:

```powershell
npm run dev
```

Wait for: `🚀 Server is running on port 7000`

---

## ✅ Step 10: Test the Setup

### Test 1: Check if ngrok tunnel works
1. Open your web browser
2. Go to: `https://YOUR_NGROK_URL.ngrok-free.app/api/webhooks/paymongo/test`
3. You should see:

```json
{"message":"PayMongo webhook endpoint is active","timestamp":"..."}
```

### Test 2: Monitor webhook traffic
1. Open: **http://127.0.0.1:4040** (Ngrok web interface)
2. This shows all incoming requests to your tunnel

---

## 📋 Complete Example Workflow

Here's what your setup should look like with **3 terminals**:

### Terminal 1: Backend Server
```powershell
cd D:\cap2_rhai_front_and_back\rhai_backend
npm run dev
# ✅ Keep this running
```

### Terminal 2: Ngrok
```powershell
cd C:\Users\<USER>\Downloads\ngrok-v3-stable-windows-amd64
.\ngrok.exe http 7000
# ✅ Keep this running
```

### Terminal 3: Configuration Commands
```powershell
cd D:\cap2_rhai_front_and_back\rhai_backend
node paymongo_webhook_manager.js create
# Use this for webhook management
```

---

## 🔄 When You Restart (IMPORTANT!)

Every time you restart ngrok, you get a **NEW URL**. You must:

### ✅ Checklist:
1. **Copy the new ngrok URL** from the terminal
2. **Update `.env` file** → `WEBHOOK_URL=...`
3. **Update `paymongo_webhook_manager.js`** → `const WEBHOOK_URL = '...'`
4. **Delete old webhook**: `node paymongo_webhook_manager.js delete OLD_ID`
5. **Create new webhook**: `node paymongo_webhook_manager.js create`
6. **Update webhook secret in `.env`** → `PAYMONGO_WEBHOOK_SECRET=...`
7. **Restart backend server**: `Ctrl+C` then `npm run dev`

### 🔄 Quick Restart Commands:
```powershell
# 1. Get new ngrok URL (copy from ngrok terminal)
# 2. Update files manually
# 3. Run these commands:

cd D:\cap2_rhai_front_and_back\rhai_backend
node paymongo_webhook_manager.js list
node paymongo_webhook_manager.js delete OLD_WEBHOOK_ID
node paymongo_webhook_manager.js create
# Copy the new secret to .env
# Restart backend server
```

---

## 🎯 Pro Tips

### 1. Create a Batch Script
Create `start_ngrok.bat` in your ngrok folder:

```batch
@echo off
echo Starting Ngrok tunnel...
cd C:\Users\<USER>\Downloads\ngrok-v3-stable-windows-amd64
.\ngrok.exe http 7000
pause
```

Double-click this file to start ngrok easily!

### 2. Add Ngrok to PATH (Optional)
1. Copy `ngrok.exe` to `C:\Windows\System32\`
2. Or add the ngrok folder to your system PATH
3. Then you can run `ngrok http 7000` from anywhere

### 3. Bookmark Important URLs
- Ngrok Dashboard: https://dashboard.ngrok.com
- Ngrok Web Interface: http://127.0.0.1:4040
- PayMongo Dashboard: https://dashboard.paymongo.com

---

## 🚨 Troubleshooting

### ❌ "Authentication failed" error
**Problem**: Ngrok can't authenticate
**Solution**: 
```powershell
.\ngrok.exe config add-authtoken YOUR_TOKEN
```
Make sure you copied the token correctly from the dashboard.

### ❌ "Address already in use" error
**Problem**: Another process is using port 7000
**Solutions**:
1. Find the process: `netstat -ano | findstr :7000`
2. Kill it: `taskkill /F /PID PROCESS_ID`
3. Or use a different port: `.\ngrok.exe http 8000`

### ❌ Webhook not receiving data
**Check these**:
1. ✅ Ngrok is running and shows "online"
2. ✅ Backend server is running on port 7000
3. ✅ Webhook URL is correct in PayMongo
4. ✅ Check ngrok web interface (http://127.0.0.1:4040) for incoming requests

### ❌ "Cannot resolve ngrok URL" error
**Problem**: Ngrok tunnel is down
**Solution**: 
1. Check ngrok terminal - should show "Session Status: online"
2. If not, restart ngrok: `Ctrl+C` then `.\ngrok.exe http 7000`

### ❌ Backend server won't start
**Problem**: Port 7000 is busy
**Solution**:
```powershell
# Find what's using port 7000
netstat -ano | findstr :7000

# Kill the process (replace PID with actual number)
taskkill /F /PID 12345
```

---

## 📱 Final Test Procedure

### 1. Verify Everything is Running
- ✅ Backend server: `🚀 Server is running on port 7000`
- ✅ Ngrok: `Session Status: online`
- ✅ Webhook created: Check with `node paymongo_webhook_manager.js list`

### 2. Test the Connection
1. **Browser test**: Go to `https://YOUR_NGROK_URL.ngrok-free.app/api/webhooks/paymongo/test`
2. **Should see**: `{"message":"PayMongo webhook endpoint is active"}`

### 3. Test with Real Payment
1. **Make a test payment** in your app
2. **Check ngrok web interface** (http://127.0.0.1:4040) - you should see the webhook request
3. **Check backend logs** - should show webhook processing messages
4. **Check AdminRequests.vue** - should receive payment confirmation notification

### 4. Success Indicators
- ✅ Payment status changes to "Payment Confirmed"
- ✅ Admin receives notification
- ✅ Webhook appears in ngrok web interface
- ✅ Backend logs show successful webhook processing

---

## 🎉 Congratulations!

Your ngrok setup is now complete! You can now receive PayMongo webhooks in your local development environment.

**Remember**: Keep both ngrok and your backend server running while testing payments!

---

**📞 Need Help?**
- Ngrok Documentation: https://ngrok.com/docs
- PayMongo Documentation: https://developers.paymongo.com/docs
- Check the ngrok web interface: http://127.0.0.1:4040

**Last Updated**: July 27, 2025
