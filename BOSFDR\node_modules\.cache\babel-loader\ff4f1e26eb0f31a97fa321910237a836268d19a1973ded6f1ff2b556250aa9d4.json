{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, normalizeClass as _normalizeClass, openBlock as _openBlock, createElementBlock as _createElementBlock, vModelText as _vModelText, withKeys as _withKeys, withDirectives as _withDirectives, resolveComponent as _resolveComponent, createVNode as _createVNode, toDisplayString as _toDisplayString, createStaticVNode as _createStaticVNode, Fragment as _Fragment } from \"vue\";\nimport _imports_0 from '@/assets/icon-of-bula.jpg';\nconst _hoisted_1 = {\n  class: \"client-header\",\n  role: \"banner\",\n  \"aria-label\": \"Site header\"\n};\nconst _hoisted_2 = {\n  class: \"gov-banner\",\n  \"aria-label\": \"Official website of Barangay Bula, General Santos City\"\n};\nconst _hoisted_3 = {\n  class: \"usa-accordion\"\n};\nconst _hoisted_4 = {\n  class: \"usa-banner__header\"\n};\nconst _hoisted_5 = {\n  class: \"usa-banner__inner\"\n};\nconst _hoisted_6 = [\"aria-expanded\"];\nconst _hoisted_7 = [\"hidden\"];\nconst _hoisted_8 = {\n  class: \"main-header\"\n};\nconst _hoisted_9 = {\n  class: \"header-container\"\n};\nconst _hoisted_10 = {\n  class: \"header-left\"\n};\nconst _hoisted_11 = [\"aria-label\"];\nconst _hoisted_12 = [\"aria-label\", \"aria-expanded\"];\nconst _hoisted_13 = {\n  class: \"main-navigation\",\n  role: \"navigation\",\n  \"aria-label\": \"Main navigation\"\n};\nconst _hoisted_14 = {\n  class: \"nav-list\",\n  role: \"menubar\"\n};\nconst _hoisted_15 = {\n  class: \"nav-item\",\n  role: \"none\"\n};\nconst _hoisted_16 = [\"aria-current\"];\nconst _hoisted_17 = {\n  class: \"nav-item\",\n  role: \"none\"\n};\nconst _hoisted_18 = [\"aria-current\"];\nconst _hoisted_19 = {\n  class: \"nav-item\",\n  role: \"none\"\n};\nconst _hoisted_20 = [\"aria-current\"];\nconst _hoisted_21 = {\n  class: \"nav-item\",\n  role: \"none\"\n};\nconst _hoisted_22 = [\"aria-current\"];\nconst _hoisted_23 = {\n  class: \"header-actions\"\n};\nconst _hoisted_24 = {\n  class: \"search-container\"\n};\nconst _hoisted_25 = [\"aria-expanded\"];\nconst _hoisted_26 = [\"aria-expanded\"];\nconst _hoisted_27 = {\n  class: \"user-avatar\"\n};\nconst _hoisted_28 = [\"src\", \"alt\"];\nconst _hoisted_29 = {\n  key: 1,\n  class: \"fas fa-user-circle avatar-icon\",\n  \"aria-hidden\": \"true\"\n};\nconst _hoisted_30 = {\n  class: \"user-info\"\n};\nconst _hoisted_31 = {\n  class: \"user-name\"\n};\nconst _hoisted_32 = {\n  key: 0,\n  class: \"user-dropdown-menu\",\n  role: \"menu\",\n  \"aria-label\": \"User account options\"\n};\nconst _hoisted_33 = {\n  class: \"dropdown-header\"\n};\nconst _hoisted_34 = {\n  class: \"user-details\"\n};\nconst _hoisted_35 = {\n  class: \"user-email\"\n};\nconst _hoisted_36 = {\n  key: 0,\n  class: \"breadcrumb-section\"\n};\nconst _hoisted_37 = {\n  class: \"header-container\"\n};\nconst _hoisted_38 = {\n  class: \"breadcrumb-nav\",\n  \"aria-label\": \"Breadcrumb navigation\"\n};\nconst _hoisted_39 = {\n  class: \"breadcrumb-list\"\n};\nconst _hoisted_40 = {\n  class: \"breadcrumb-item\"\n};\nconst _hoisted_41 = {\n  class: \"breadcrumb-item active\",\n  \"aria-current\": \"page\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_ClientNotifications = _resolveComponent(\"ClientNotifications\");\n  return _openBlock(), _createElementBlock(_Fragment, null, [_createCommentVNode(\" Skip Navigation Link for Accessibility \"), _cache[42] || (_cache[42] = _createElementVNode(\"a\", {\n    href: \"#main-content\",\n    class: \"skip-link\"\n  }, \"Skip to main content\", -1 /* HOISTED */)), _createElementVNode(\"header\", _hoisted_1, [_createCommentVNode(\" Government Banner - USWDS Compliant \"), _createElementVNode(\"section\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"header\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_cache[20] || (_cache[20] = _createStaticVNode(\"<div class=\\\"grid-col-auto\\\" data-v-237c3b88><img aria-hidden=\\\"true\\\" class=\\\"usa-banner__header-flag\\\" src=\\\"/assets/images/ph_flag_small.png\\\" alt=\\\"\\\" data-v-237c3b88></div><div class=\\\"grid-col-fill tablet:grid-col-auto\\\" aria-hidden=\\\"true\\\" data-v-237c3b88><p class=\\\"usa-banner__header-text\\\" data-v-237c3b88> An official website of Barangay Bula, General Santos City </p><p class=\\\"usa-banner__header-action\\\" data-v-237c3b88>Here&#39;s how you know</p></div>\", 2)), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"usa-accordion__button usa-banner__button\",\n    \"aria-expanded\": $setup.showBannerDetails,\n    \"aria-controls\": \"gov-banner-content\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $setup.toggleBannerDetails && $setup.toggleBannerDetails(...args))\n  }, _cache[19] || (_cache[19] = [_createElementVNode(\"span\", {\n    class: \"usa-banner__button-text\"\n  }, \"Here's how you know\", -1 /* HOISTED */)]), 8 /* PROPS */, _hoisted_6)])]), _createElementVNode(\"div\", {\n    class: \"usa-banner__content usa-accordion__content\",\n    id: \"gov-banner-content\",\n    hidden: !$setup.showBannerDetails\n  }, _cache[21] || (_cache[21] = [_createElementVNode(\"div\", {\n    class: \"grid-row grid-gap-lg\"\n  }, [_createElementVNode(\"div\", {\n    class: \"usa-banner__guidance tablet:grid-col-6\"\n  }, [_createElementVNode(\"img\", {\n    class: \"usa-banner__icon usa-media-block__img\",\n    src: \"/assets/images/icon-dot-gov.svg\",\n    role: \"img\",\n    alt: \"\",\n    \"aria-hidden\": \"true\"\n  }), _createElementVNode(\"div\", {\n    class: \"usa-media-block__body\"\n  }, [_createElementVNode(\"p\", null, [_createElementVNode(\"strong\", null, \"Official websites use .gov.ph\"), _createElementVNode(\"br\"), _createTextVNode(\" A \"), _createElementVNode(\"strong\", null, \".gov.ph\"), _createTextVNode(\" website belongs to an official government organization in the Philippines. \")])])]), _createElementVNode(\"div\", {\n    class: \"usa-banner__guidance tablet:grid-col-6\"\n  }, [_createElementVNode(\"img\", {\n    class: \"usa-banner__icon usa-media-block__img\",\n    src: \"/assets/images/icon-https.svg\",\n    role: \"img\",\n    alt: \"\",\n    \"aria-hidden\": \"true\"\n  }), _createElementVNode(\"div\", {\n    class: \"usa-media-block__body\"\n  }, [_createElementVNode(\"p\", null, [_createElementVNode(\"strong\", null, \"Secure .gov.ph websites use HTTPS\"), _createElementVNode(\"br\"), _createTextVNode(\" A \"), _createElementVNode(\"strong\", null, \"lock\"), _createTextVNode(\" (🔒) or \"), _createElementVNode(\"strong\", null, \"https://\"), _createTextVNode(\" means you've safely connected to the .gov.ph website. Share sensitive information only on official, secure websites. \")])])])], -1 /* HOISTED */)]), 8 /* PROPS */, _hoisted_7)])]), _createCommentVNode(\" Main Header \"), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_createCommentVNode(\" Left Section: Logo and Navigation \"), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"button\", {\n    class: \"logo-section\",\n    onClick: _cache[1] || (_cache[1] = $event => $setup.handleMenuAction('dashboard')),\n    \"aria-label\": `Go to ${$setup.getPageTitle()} dashboard`,\n    type: \"button\"\n  }, _cache[22] || (_cache[22] = [_createElementVNode(\"img\", {\n    src: _imports_0,\n    alt: \"Barangay Bula Logo\",\n    class: \"logo\",\n    width: \"40\",\n    height: \"40\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"div\", {\n    class: \"site-identity\"\n  }, [_createElementVNode(\"h1\", {\n    class: \"site-title\"\n  }, \"Barangay Bula\"), _createElementVNode(\"span\", {\n    class: \"site-subtitle\"\n  }, \"Digital Services Portal\")], -1 /* HOISTED */)]), 8 /* PROPS */, _hoisted_11), _createCommentVNode(\" Mobile Menu Toggle \"), _createElementVNode(\"button\", {\n    class: \"mobile-menu-toggle\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $setup.handleSidebarToggle && $setup.handleSidebarToggle(...args)),\n    \"aria-label\": $props.sidebarCollapsed ? 'Open navigation menu' : 'Close navigation menu',\n    \"aria-expanded\": !$props.sidebarCollapsed,\n    type: \"button\"\n  }, [_createElementVNode(\"span\", {\n    class: _normalizeClass([\"hamburger-line\", {\n      active: !$props.sidebarCollapsed\n    }])\n  }, null, 2 /* CLASS */), _createElementVNode(\"span\", {\n    class: _normalizeClass([\"hamburger-line\", {\n      active: !$props.sidebarCollapsed\n    }])\n  }, null, 2 /* CLASS */), _createElementVNode(\"span\", {\n    class: _normalizeClass([\"hamburger-line\", {\n      active: !$props.sidebarCollapsed\n    }])\n  }, null, 2 /* CLASS */)], 8 /* PROPS */, _hoisted_12)]), _createCommentVNode(\" Center Section: Navigation (Desktop) \"), _createElementVNode(\"nav\", _hoisted_13, [_createElementVNode(\"ul\", _hoisted_14, [_createElementVNode(\"li\", _hoisted_15, [_createElementVNode(\"button\", {\n    class: _normalizeClass([\"nav-link\", {\n      active: $props.activeMenu === 'dashboard'\n    }]),\n    onClick: _cache[3] || (_cache[3] = $event => $setup.handleMenuAction('dashboard')),\n    role: \"menuitem\",\n    \"aria-current\": $props.activeMenu === 'dashboard' ? 'page' : undefined,\n    type: \"button\"\n  }, _cache[23] || (_cache[23] = [_createElementVNode(\"svg\", {\n    class: \"nav-icon\",\n    \"aria-hidden\": \"true\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z\"\n  })], -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Dashboard\", -1 /* HOISTED */)]), 10 /* CLASS, PROPS */, _hoisted_16)]), _createElementVNode(\"li\", _hoisted_17, [_createElementVNode(\"button\", {\n    class: _normalizeClass([\"nav-link\", {\n      active: $props.activeMenu === 'services'\n    }]),\n    onClick: _cache[4] || (_cache[4] = $event => $setup.handleMenuAction('services')),\n    role: \"menuitem\",\n    \"aria-current\": $props.activeMenu === 'services' ? 'page' : undefined,\n    type: \"button\"\n  }, _cache[24] || (_cache[24] = [_createElementVNode(\"svg\", {\n    class: \"nav-icon\",\n    \"aria-hidden\": \"true\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n  })], -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Services\", -1 /* HOISTED */)]), 10 /* CLASS, PROPS */, _hoisted_18)]), _createElementVNode(\"li\", _hoisted_19, [_createElementVNode(\"button\", {\n    class: _normalizeClass([\"nav-link\", {\n      active: $props.activeMenu === 'requests'\n    }]),\n    onClick: _cache[5] || (_cache[5] = $event => $setup.handleMenuAction('requests')),\n    role: \"menuitem\",\n    \"aria-current\": $props.activeMenu === 'requests' ? 'page' : undefined,\n    type: \"button\"\n  }, _cache[25] || (_cache[25] = [_createElementVNode(\"svg\", {\n    class: \"nav-icon\",\n    \"aria-hidden\": \"true\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z\"\n  })], -1 /* HOISTED */), _createElementVNode(\"span\", null, \"My Requests\", -1 /* HOISTED */)]), 10 /* CLASS, PROPS */, _hoisted_20)]), _createElementVNode(\"li\", _hoisted_21, [_createElementVNode(\"button\", {\n    class: _normalizeClass([\"nav-link\", {\n      active: $props.activeMenu === 'help'\n    }]),\n    onClick: _cache[6] || (_cache[6] = $event => $setup.handleMenuAction('help')),\n    role: \"menuitem\",\n    \"aria-current\": $props.activeMenu === 'help' ? 'page' : undefined,\n    type: \"button\"\n  }, _cache[26] || (_cache[26] = [_createElementVNode(\"svg\", {\n    class: \"nav-icon\",\n    \"aria-hidden\": \"true\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M11,18H13V16H11V18M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,20C7.59,20 4,16.41 4,12C7.59,8 4,8 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,6A4,4 0 0,0 8,10H10A2,2 0 0,1 12,8A2,2 0 0,1 14,10C14,12 11,11.75 11,15H13C13,12.75 16,12.5 16,10A4,4 0 0,0 12,6Z\"\n  })], -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Help\", -1 /* HOISTED */)]), 10 /* CLASS, PROPS */, _hoisted_22)])])]), _createCommentVNode(\" Right Section: User Actions \"), _createElementVNode(\"div\", _hoisted_23, [_createCommentVNode(\" Search \"), _createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"button\", {\n    class: \"search-toggle\",\n    onClick: _cache[7] || (_cache[7] = (...args) => $setup.toggleSearch && $setup.toggleSearch(...args)),\n    \"aria-label\": \"Search documents and services\",\n    \"aria-expanded\": $setup.showSearch\n  }, _cache[27] || (_cache[27] = [_createElementVNode(\"i\", {\n    class: \"fas fa-search\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */)]), 8 /* PROPS */, _hoisted_25), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"search-box\", {\n      active: $setup.showSearch\n    }]),\n    role: \"search\"\n  }, [_cache[29] || (_cache[29] = _createElementVNode(\"label\", {\n    for: \"header-search\",\n    class: \"sr-only\"\n  }, \"Search documents and services\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    id: \"header-search\",\n    type: \"search\",\n    placeholder: \"Search documents, services...\",\n    class: \"search-input\",\n    \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $setup.searchQuery = $event),\n    onKeyup: _cache[9] || (_cache[9] = _withKeys((...args) => $setup.performSearch && $setup.performSearch(...args), [\"enter\"])),\n    autocomplete: \"off\"\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelText, $setup.searchQuery]]), _createElementVNode(\"button\", {\n    class: \"search-submit\",\n    onClick: _cache[10] || (_cache[10] = (...args) => $setup.performSearch && $setup.performSearch(...args)),\n    \"aria-label\": \"Submit search\"\n  }, _cache[28] || (_cache[28] = [_createElementVNode(\"i\", {\n    class: \"fas fa-search\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */)]))], 2 /* CLASS */)]), _createCommentVNode(\" Notifications \"), _createVNode(_component_ClientNotifications, {\n    onNewNotification: $setup.handleNewNotification,\n    onNotificationClick: $setup.handleNotificationClick,\n    onError: $setup.handleNotificationError\n  }, null, 8 /* PROPS */, [\"onNewNotification\", \"onNotificationClick\", \"onError\"]), _createCommentVNode(\" User Profile \"), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"user-profile\", {\n      active: $props.showUserDropdown\n    }])\n  }, [_createElementVNode(\"button\", {\n    class: \"user-btn\",\n    onClick: _cache[11] || (_cache[11] = (...args) => $setup.handleUserDropdownToggle && $setup.handleUserDropdownToggle(...args)),\n    \"aria-label\": \"User account menu\",\n    \"aria-expanded\": $props.showUserDropdown\n  }, [_createElementVNode(\"div\", _hoisted_27, [$props.userAvatar ? (_openBlock(), _createElementBlock(\"img\", {\n    key: 0,\n    src: $props.userAvatar,\n    alt: $props.userName,\n    class: \"avatar-image\"\n  }, null, 8 /* PROPS */, _hoisted_28)) : (_openBlock(), _createElementBlock(\"i\", _hoisted_29))]), _createElementVNode(\"div\", _hoisted_30, [_createElementVNode(\"span\", _hoisted_31, _toDisplayString($props.userName), 1 /* TEXT */), _cache[30] || (_cache[30] = _createElementVNode(\"span\", {\n    class: \"user-role\"\n  }, \"Client Portal\", -1 /* HOISTED */))]), _cache[31] || (_cache[31] = _createElementVNode(\"i\", {\n    class: \"fas fa-chevron-down dropdown-arrow\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */))], 8 /* PROPS */, _hoisted_26), $props.showUserDropdown ? (_openBlock(), _createElementBlock(\"div\", _hoisted_32, [_createElementVNode(\"div\", _hoisted_33, [_createElementVNode(\"div\", _hoisted_34, [_createElementVNode(\"strong\", null, _toDisplayString($props.userName), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_35, _toDisplayString($props.userEmail), 1 /* TEXT */)])]), _cache[38] || (_cache[38] = _createElementVNode(\"div\", {\n    class: \"dropdown-divider\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"dropdown-item\",\n    onClick: _cache[12] || (_cache[12] = $event => $setup.handleMenuAction('profile')),\n    role: \"menuitem\"\n  }, _cache[32] || (_cache[32] = [_createElementVNode(\"i\", {\n    class: \"fas fa-user\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"My Profile\", -1 /* HOISTED */)])), _createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"dropdown-item\",\n    onClick: _cache[13] || (_cache[13] = $event => $setup.handleMenuAction('settings')),\n    role: \"menuitem\"\n  }, _cache[33] || (_cache[33] = [_createElementVNode(\"i\", {\n    class: \"fas fa-cog\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Account Settings\", -1 /* HOISTED */)])), _createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"dropdown-item\",\n    onClick: _cache[14] || (_cache[14] = $event => $setup.handleMenuAction('documents')),\n    role: \"menuitem\"\n  }, _cache[34] || (_cache[34] = [_createElementVNode(\"i\", {\n    class: \"fas fa-folder\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"My Documents\", -1 /* HOISTED */)])), _createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"dropdown-item\",\n    onClick: _cache[15] || (_cache[15] = $event => $setup.handleMenuAction('history')),\n    role: \"menuitem\"\n  }, _cache[35] || (_cache[35] = [_createElementVNode(\"i\", {\n    class: \"fas fa-history\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Request History\", -1 /* HOISTED */)])), _cache[39] || (_cache[39] = _createElementVNode(\"div\", {\n    class: \"dropdown-divider\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"dropdown-item\",\n    onClick: _cache[16] || (_cache[16] = $event => $setup.handleMenuAction('help')),\n    role: \"menuitem\"\n  }, _cache[36] || (_cache[36] = [_createElementVNode(\"i\", {\n    class: \"fas fa-question-circle\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Help & Support\", -1 /* HOISTED */)])), _cache[40] || (_cache[40] = _createElementVNode(\"div\", {\n    class: \"dropdown-divider\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"dropdown-item logout-item\",\n    onClick: _cache[17] || (_cache[17] = (...args) => $setup.handleLogout && $setup.handleLogout(...args)),\n    role: \"menuitem\"\n  }, _cache[37] || (_cache[37] = [_createElementVNode(\"i\", {\n    class: \"fas fa-sign-out-alt\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Sign Out\", -1 /* HOISTED */)]))])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)])])]), _createCommentVNode(\" Breadcrumb Navigation \"), $props.showBreadcrumbs ? (_openBlock(), _createElementBlock(\"div\", _hoisted_36, [_createElementVNode(\"div\", _hoisted_37, [_createElementVNode(\"nav\", _hoisted_38, [_createElementVNode(\"ol\", _hoisted_39, [_createElementVNode(\"li\", _hoisted_40, [_createElementVNode(\"a\", {\n    href: \"#\",\n    onClick: _cache[18] || (_cache[18] = $event => $setup.handleMenuAction('dashboard'))\n  }, _cache[41] || (_cache[41] = [_createElementVNode(\"i\", {\n    class: \"fas fa-home\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Dashboard \")]))]), _createElementVNode(\"li\", _hoisted_41, _toDisplayString($setup.getPageTitle()), 1 /* TEXT */)])])])])) : _createCommentVNode(\"v-if\", true)])], 64 /* STABLE_FRAGMENT */);\n}", "map": {"version": 3, "names": ["_imports_0", "class", "role", "_createCommentVNode", "_createElementVNode", "href", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "type", "$setup", "showBannerDetails", "onClick", "_cache", "args", "toggleBannerDetails", "id", "hidden", "src", "alt", "_hoisted_8", "_hoisted_9", "_hoisted_10", "$event", "handleMenuAction", "getPageTitle", "width", "height", "handleSidebarToggle", "$props", "sidebarCollapsed", "_normalizeClass", "active", "_hoisted_13", "_hoisted_14", "_hoisted_15", "activeMenu", "undefined", "viewBox", "fill", "d", "_hoisted_17", "_hoisted_19", "_hoisted_21", "_hoisted_23", "_hoisted_24", "toggleSearch", "showSearch", "for", "placeholder", "searchQuery", "onKeyup", "_with<PERSON><PERSON><PERSON>", "performSearch", "autocomplete", "_createVNode", "_component_ClientNotifications", "onNewNotification", "handleNewNotification", "onNotificationClick", "handleNotificationClick", "onError", "handleNotificationError", "showUserDropdown", "handleUserDropdownToggle", "_hoisted_27", "userAvatar", "_createElementBlock", "userName", "_hoisted_29", "_hoisted_30", "_hoisted_31", "_toDisplayString", "_hoisted_32", "_hoisted_33", "_hoisted_34", "_hoisted_35", "userEmail", "handleLogout", "showBreadcrumbs", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_hoisted_39", "_hoisted_40", "_hoisted_41"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\client\\ClientHeader.vue"], "sourcesContent": ["<template>\n  <!-- Skip Navigation Link for Accessibility -->\n  <a href=\"#main-content\" class=\"skip-link\">Skip to main content</a>\n\n  <header class=\"client-header\" role=\"banner\" aria-label=\"Site header\">\n    <!-- Government Banner - USWDS Compliant -->\n    <section\n      class=\"gov-banner\"\n      aria-label=\"Official website of Barangay Bula, General Santos City\"\n    >\n      <div class=\"usa-accordion\">\n        <header class=\"usa-banner__header\">\n          <div class=\"usa-banner__inner\">\n            <div class=\"grid-col-auto\">\n              <img\n                aria-hidden=\"true\"\n                class=\"usa-banner__header-flag\"\n                src=\"/assets/images/ph_flag_small.png\"\n                alt=\"\"\n              />\n            </div>\n            <div class=\"grid-col-fill tablet:grid-col-auto\" aria-hidden=\"true\">\n              <p class=\"usa-banner__header-text\">\n                An official website of Barangay Bula, General Santos City\n              </p>\n              <p class=\"usa-banner__header-action\">Here's how you know</p>\n            </div>\n            <button\n              type=\"button\"\n              class=\"usa-accordion__button usa-banner__button\"\n              :aria-expanded=\"showBannerDetails\"\n              aria-controls=\"gov-banner-content\"\n              @click=\"toggleBannerDetails\"\n            >\n              <span class=\"usa-banner__button-text\">Here's how you know</span>\n            </button>\n          </div>\n        </header>\n        <div\n          class=\"usa-banner__content usa-accordion__content\"\n          id=\"gov-banner-content\"\n          :hidden=\"!showBannerDetails\"\n        >\n          <div class=\"grid-row grid-gap-lg\">\n            <div class=\"usa-banner__guidance tablet:grid-col-6\">\n              <img\n                class=\"usa-banner__icon usa-media-block__img\"\n                src=\"/assets/images/icon-dot-gov.svg\"\n                role=\"img\"\n                alt=\"\"\n                aria-hidden=\"true\"\n              />\n              <div class=\"usa-media-block__body\">\n                <p>\n                  <strong>Official websites use .gov.ph</strong><br />\n                  A <strong>.gov.ph</strong> website belongs to an official government\n                  organization in the Philippines.\n                </p>\n              </div>\n            </div>\n            <div class=\"usa-banner__guidance tablet:grid-col-6\">\n              <img\n                class=\"usa-banner__icon usa-media-block__img\"\n                src=\"/assets/images/icon-https.svg\"\n                role=\"img\"\n                alt=\"\"\n                aria-hidden=\"true\"\n              />\n              <div class=\"usa-media-block__body\">\n                <p>\n                  <strong>Secure .gov.ph websites use HTTPS</strong><br />\n                  A <strong>lock</strong> (🔒) or <strong>https://</strong> means you've\n                  safely connected to the .gov.ph website. Share sensitive information\n                  only on official, secure websites.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- Main Header -->\n    <div class=\"main-header\">\n      <div class=\"header-container\">\n        <!-- Left Section: Logo and Navigation -->\n        <div class=\"header-left\">\n          <button\n            class=\"logo-section\"\n            @click=\"handleMenuAction('dashboard')\"\n            :aria-label=\"`Go to ${getPageTitle()} dashboard`\"\n            type=\"button\"\n          >\n            <img\n              src=\"@/assets/icon-of-bula.jpg\"\n              alt=\"Barangay Bula Logo\"\n              class=\"logo\"\n              width=\"40\"\n              height=\"40\"\n            />\n            <div class=\"site-identity\">\n              <h1 class=\"site-title\">Barangay Bula</h1>\n              <span class=\"site-subtitle\">Digital Services Portal</span>\n            </div>\n          </button>\n\n          <!-- Mobile Menu Toggle -->\n          <button\n            class=\"mobile-menu-toggle\"\n            @click=\"handleSidebarToggle\"\n            :aria-label=\"sidebarCollapsed ? 'Open navigation menu' : 'Close navigation menu'\"\n            :aria-expanded=\"!sidebarCollapsed\"\n            type=\"button\"\n          >\n            <span class=\"hamburger-line\" :class=\"{ active: !sidebarCollapsed }\"></span>\n            <span class=\"hamburger-line\" :class=\"{ active: !sidebarCollapsed }\"></span>\n            <span class=\"hamburger-line\" :class=\"{ active: !sidebarCollapsed }\"></span>\n          </button>\n        </div>\n\n        <!-- Center Section: Navigation (Desktop) -->\n        <nav class=\"main-navigation\" role=\"navigation\" aria-label=\"Main navigation\">\n          <ul class=\"nav-list\" role=\"menubar\">\n            <li class=\"nav-item\" role=\"none\">\n              <button\n                class=\"nav-link\"\n                :class=\"{ active: activeMenu === 'dashboard' }\"\n                @click=\"handleMenuAction('dashboard')\"\n                role=\"menuitem\"\n                :aria-current=\"activeMenu === 'dashboard' ? 'page' : undefined\"\n                type=\"button\"\n              >\n                <svg class=\"nav-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z\"/>\n                </svg>\n                <span>Dashboard</span>\n              </button>\n            </li>\n            <li class=\"nav-item\" role=\"none\">\n              <button\n                class=\"nav-link\"\n                :class=\"{ active: activeMenu === 'services' }\"\n                @click=\"handleMenuAction('services')\"\n                role=\"menuitem\"\n                :aria-current=\"activeMenu === 'services' ? 'page' : undefined\"\n                type=\"button\"\n              >\n                <svg class=\"nav-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"/>\n                </svg>\n                <span>Services</span>\n              </button>\n            </li>\n            <li class=\"nav-item\" role=\"none\">\n              <button\n                class=\"nav-link\"\n                :class=\"{ active: activeMenu === 'requests' }\"\n                @click=\"handleMenuAction('requests')\"\n                role=\"menuitem\"\n                :aria-current=\"activeMenu === 'requests' ? 'page' : undefined\"\n                type=\"button\"\n              >\n                <svg class=\"nav-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z\"/>\n                </svg>\n                <span>My Requests</span>\n              </button>\n            </li>\n            <li class=\"nav-item\" role=\"none\">\n              <button\n                class=\"nav-link\"\n                :class=\"{ active: activeMenu === 'help' }\"\n                @click=\"handleMenuAction('help')\"\n                role=\"menuitem\"\n                :aria-current=\"activeMenu === 'help' ? 'page' : undefined\"\n                type=\"button\"\n              >\n                <svg class=\"nav-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M11,18H13V16H11V18M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,20C7.59,20 4,16.41 4,12C7.59,8 4,8 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,6A4,4 0 0,0 8,10H10A2,2 0 0,1 12,8A2,2 0 0,1 14,10C14,12 11,11.75 11,15H13C13,12.75 16,12.5 16,10A4,4 0 0,0 12,6Z\"/>\n                </svg>\n                <span>Help</span>\n              </button>\n            </li>\n          </ul>\n        </nav>\n\n        <!-- Right Section: User Actions -->\n        <div class=\"header-actions\">\n          <!-- Search -->\n          <div class=\"search-container\">\n            <button class=\"search-toggle\" @click=\"toggleSearch\" aria-label=\"Search documents and services\" :aria-expanded=\"showSearch\">\n              <i class=\"fas fa-search\" aria-hidden=\"true\"></i>\n            </button>\n            <div class=\"search-box\" :class=\"{ active: showSearch }\" role=\"search\">\n              <label for=\"header-search\" class=\"sr-only\">Search documents and services</label>\n              <input\n                id=\"header-search\"\n                type=\"search\"\n                placeholder=\"Search documents, services...\"\n                class=\"search-input\"\n                v-model=\"searchQuery\"\n                @keyup.enter=\"performSearch\"\n                autocomplete=\"off\"\n              />\n              <button class=\"search-submit\" @click=\"performSearch\" aria-label=\"Submit search\">\n                <i class=\"fas fa-search\" aria-hidden=\"true\"></i>\n              </button>\n            </div>\n          </div>\n\n          <!-- Notifications -->\n          <ClientNotifications\n            @new-notification=\"handleNewNotification\"\n            @notification-click=\"handleNotificationClick\"\n            @error=\"handleNotificationError\"\n          />\n\n          <!-- User Profile -->\n          <div class=\"user-profile\" :class=\"{ active: showUserDropdown }\">\n            <button class=\"user-btn\" @click=\"handleUserDropdownToggle\" aria-label=\"User account menu\" :aria-expanded=\"showUserDropdown\">\n              <div class=\"user-avatar\">\n                <img v-if=\"userAvatar\" :src=\"userAvatar\" :alt=\"userName\" class=\"avatar-image\" />\n                <i v-else class=\"fas fa-user-circle avatar-icon\" aria-hidden=\"true\"></i>\n              </div>\n              <div class=\"user-info\">\n                <span class=\"user-name\">{{ userName }}</span>\n                <span class=\"user-role\">Client Portal</span>\n              </div>\n              <i class=\"fas fa-chevron-down dropdown-arrow\" aria-hidden=\"true\"></i>\n            </button>\n\n            <div v-if=\"showUserDropdown\" class=\"user-dropdown-menu\" role=\"menu\" aria-label=\"User account options\">\n              <div class=\"dropdown-header\">\n                <div class=\"user-details\">\n                  <strong>{{ userName }}</strong>\n                  <span class=\"user-email\">{{ userEmail }}</span>\n                </div>\n              </div>\n              <div class=\"dropdown-divider\"></div>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('profile')\" role=\"menuitem\">\n                <i class=\"fas fa-user\" aria-hidden=\"true\"></i>\n                <span>My Profile</span>\n              </a>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('settings')\" role=\"menuitem\">\n                <i class=\"fas fa-cog\" aria-hidden=\"true\"></i>\n                <span>Account Settings</span>\n              </a>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('documents')\" role=\"menuitem\">\n                <i class=\"fas fa-folder\" aria-hidden=\"true\"></i>\n                <span>My Documents</span>\n              </a>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('history')\" role=\"menuitem\">\n                <i class=\"fas fa-history\" aria-hidden=\"true\"></i>\n                <span>Request History</span>\n              </a>\n              <div class=\"dropdown-divider\"></div>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('help')\" role=\"menuitem\">\n                <i class=\"fas fa-question-circle\" aria-hidden=\"true\"></i>\n                <span>Help & Support</span>\n              </a>\n              <div class=\"dropdown-divider\"></div>\n              <a href=\"#\" class=\"dropdown-item logout-item\" @click=\"handleLogout\" role=\"menuitem\">\n                <i class=\"fas fa-sign-out-alt\" aria-hidden=\"true\"></i>\n                <span>Sign Out</span>\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Breadcrumb Navigation -->\n    <div class=\"breadcrumb-section\" v-if=\"showBreadcrumbs\">\n      <div class=\"header-container\">\n        <nav class=\"breadcrumb-nav\" aria-label=\"Breadcrumb navigation\">\n          <ol class=\"breadcrumb-list\">\n            <li class=\"breadcrumb-item\">\n              <a href=\"#\" @click=\"handleMenuAction('dashboard')\">\n                <i class=\"fas fa-home\" aria-hidden=\"true\"></i>\n                Dashboard\n              </a>\n            </li>\n            <li class=\"breadcrumb-item active\" aria-current=\"page\">\n              {{ getPageTitle() }}\n            </li>\n          </ol>\n        </nav>\n      </div>\n    </div>\n  </header>\n</template>\n\n<script>\nimport { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue';\nimport ClientNotifications from './ClientNotifications.vue';\n\nexport default {\n  name: 'ClientHeader',\n  components: {\n    ClientNotifications\n  },\n  props: {\n    userName: {\n      type: String,\n      default: 'User'\n    },\n    userEmail: {\n      type: String,\n      default: '<EMAIL>'\n    },\n    userAvatar: {\n      type: String,\n      default: null\n    },\n    showUserDropdown: {\n      type: Boolean,\n      default: false\n    },\n    sidebarCollapsed: {\n      type: Boolean,\n      default: false\n    },\n    activeMenu: {\n      type: String,\n      default: 'dashboard'\n    },\n    showBreadcrumbs: {\n      type: Boolean,\n      default: true\n    }\n  },\n\n  emits: [\n    'sidebar-toggle',\n    'user-dropdown-toggle',\n    'menu-action',\n    'logout',\n    'error',\n    'search',\n    'notification-click'\n  ],\n\n  setup(props, { emit }) {\n    // Reactive state\n    const showSearch = ref(false);\n    const showBannerDetails = ref(false);\n    const searchQuery = ref('');\n    const searchInput = ref(null);\n\n    // Computed properties\n    const getPageTitle = computed(() => {\n      const titles = {\n        dashboard: 'Dashboard',\n        services: 'Services',\n        requests: 'My Requests',\n        profile: 'My Profile',\n        settings: 'Settings',\n        help: 'Help & Support',\n        documents: 'My Documents',\n        history: 'Request History'\n      };\n      return titles[props.activeMenu] || 'Dashboard';\n    });\n\n    // Methods\n    const toggleSearch = async () => {\n      showSearch.value = !showSearch.value;\n\n      if (showSearch.value) {\n        await nextTick();\n        searchInput.value?.focus();\n      }\n    };\n\n    const closeSearch = () => {\n      showSearch.value = false;\n      searchQuery.value = '';\n    };\n\n    const toggleBannerDetails = () => {\n      showBannerDetails.value = !showBannerDetails.value;\n    };\n\n    const handleSearch = () => {\n      if (searchQuery.value.trim()) {\n        emit('search', searchQuery.value.trim());\n        closeSearch();\n      }\n    };\n\n    const performSearch = () => {\n      handleSearch();\n    };\n\n    const handleSidebarToggle = () => {\n      emit('sidebar-toggle');\n    };\n\n    const handleUserDropdownToggle = () => {\n      emit('user-dropdown-toggle');\n    };\n\n    const handleMenuAction = (action) => {\n      emit('menu-action', action);\n    };\n\n    const handleLogout = () => {\n      emit('logout');\n    };\n\n    const handleOutsideClick = (event) => {\n      // Check if click is outside user dropdown\n      if (!event.target.closest('.user-profile')) {\n        if (props.showUserDropdown) {\n          emit('user-dropdown-toggle');\n        }\n      }\n\n      // Check if click is outside search\n      if (!event.target.closest('.search-container')) {\n        showSearch.value = false;\n      }\n\n      // Check if click is outside banner\n      if (!event.target.closest('.gov-banner')) {\n        showBannerDetails.value = false;\n      }\n    };\n\n    // Notification event handlers\n    const handleNewNotification = (notification) => {\n      console.log('New notification received:', notification);\n      // Handle new notification - could show toast, update UI, etc.\n    };\n\n    const handleNotificationClick = (notification) => {\n      console.log('📊 ClientHeader: Handling notification click:', notification);\n\n      try {\n        // Parse notification data\n        const notificationData = typeof notification.data === 'string'\n          ? JSON.parse(notification.data)\n          : notification.data || {};\n\n        // Log navigation for debugging\n        console.log('📊 ClientHeader: Notification data:', notificationData);\n\n        // Additional header-specific logic can go here\n        // For example, updating header state, showing badges, etc.\n\n        // The navigation is now handled by the ClientNotifications component\n        // This handler can focus on header-specific updates\n\n      } catch (error) {\n        console.error('❌ ClientHeader: Error handling notification click:', error);\n      }\n\n      // Always emit the event for other components that might need it\n      emit('notification-click', notification);\n    };\n\n    const handleNotificationError = (error) => {\n      console.error('Notification error:', error);\n      emit('error', error);\n    };\n\n    // Lifecycle hooks\n    onMounted(() => {\n      document.addEventListener('click', handleOutsideClick);\n    });\n\n    onBeforeUnmount(() => {\n      document.removeEventListener('click', handleOutsideClick);\n    });\n\n    return {\n      // Reactive state\n      showSearch,\n      showBannerDetails,\n      searchQuery,\n      searchInput,\n\n      // Computed\n      getPageTitle,\n\n      // Methods\n      toggleSearch,\n      closeSearch,\n      toggleBannerDetails,\n      handleSearch,\n      performSearch,\n      handleSidebarToggle,\n      handleUserDropdownToggle,\n      handleMenuAction,\n      handleLogout,\n      handleOutsideClick,\n      handleNewNotification,\n      handleNotificationClick,\n      handleNotificationError\n    };\n  }\n};\n</script>\n\n<style scoped src=\"./css/clientHeader.css\"></style>\n"], "mappings": ";OA8FcA,UAA+B;;EA1FnCC,KAAK,EAAC,eAAe;EAACC,IAAI,EAAC,QAAQ;EAAC,YAAU,EAAC;;;EAGnDD,KAAK,EAAC,YAAY;EAClB,YAAU,EAAC;;;EAENA,KAAK,EAAC;AAAe;;EAChBA,KAAK,EAAC;AAAoB;;EAC3BA,KAAK,EAAC;AAAmB;;;;EAuE/BA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAkB;;EAEtBA,KAAK,EAAC;AAAa;;;;EAmCnBA,KAAK,EAAC,iBAAiB;EAACC,IAAI,EAAC,YAAY;EAAC,YAAU,EAAC;;;EACpDD,KAAK,EAAC,UAAU;EAACC,IAAI,EAAC;;;EACpBD,KAAK,EAAC,UAAU;EAACC,IAAI,EAAC;;;;EAetBD,KAAK,EAAC,UAAU;EAACC,IAAI,EAAC;;;;EAetBD,KAAK,EAAC,UAAU;EAACC,IAAI,EAAC;;;;EAetBD,KAAK,EAAC,UAAU;EAACC,IAAI,EAAC;;;;EAmBzBD,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAkB;;;;EA+BpBA,KAAK,EAAC;AAAa;;;;EAEZA,KAAK,EAAC,gCAAgC;EAAC,aAAW,EAAC;;;EAE1DA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAW;;;EAMEA,KAAK,EAAC,oBAAoB;EAACC,IAAI,EAAC,MAAM;EAAC,YAAU,EAAC;;;EACxED,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAc;;EAEjBA,KAAK,EAAC;AAAY;;;EAqCjCA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC,gBAAgB;EAAC,YAAU,EAAC;;;EACjCA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAiB;;EAMvBA,KAAK,EAAC,wBAAwB;EAAC,cAAY,EAAC;;;;6DAzR1DE,mBAAA,4CAA+C,E,4BAC/CC,mBAAA,CAAkE;IAA/DC,IAAI,EAAC,eAAe;IAACJ,KAAK,EAAC;KAAY,sBAAoB,sBAE9DG,mBAAA,CA6RS,UA7RTE,UA6RS,GA5RPH,mBAAA,yCAA4C,EAC5CC,mBAAA,CA0EU,WA1EVG,UA0EU,GAtERH,mBAAA,CAqEM,OArENI,UAqEM,GApEJJ,mBAAA,CA0BS,UA1BTK,UA0BS,GAzBPL,mBAAA,CAwBM,OAxBNM,UAwBM,G,4gBATJN,mBAAA,CAQS;IAPPO,IAAI,EAAC,QAAQ;IACbV,KAAK,EAAC,0CAA0C;IAC/C,eAAa,EAAEW,MAAA,CAAAC,iBAAiB;IACjC,eAAa,EAAC,oBAAoB;IACjCC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEJ,MAAA,CAAAK,mBAAA,IAAAL,MAAA,CAAAK,mBAAA,IAAAD,IAAA,CAAmB;kCAE3BZ,mBAAA,CAAgE;IAA1DH,KAAK,EAAC;EAAyB,GAAC,qBAAmB,oB,mCAI/DG,mBAAA,CAwCM;IAvCJH,KAAK,EAAC,4CAA4C;IAClDiB,EAAE,EAAC,oBAAoB;IACtBC,MAAM,GAAGP,MAAA,CAAAC;kCAEVT,mBAAA,CAkCM;IAlCDH,KAAK,EAAC;EAAsB,IAC/BG,mBAAA,CAeM;IAfDH,KAAK,EAAC;EAAwC,IACjDG,mBAAA,CAME;IALAH,KAAK,EAAC,uCAAuC;IAC7CmB,GAAG,EAAC,iCAAiC;IACrClB,IAAI,EAAC,KAAK;IACVmB,GAAG,EAAC,EAAE;IACN,aAAW,EAAC;MAEdjB,mBAAA,CAMM;IANDH,KAAK,EAAC;EAAuB,IAChCG,mBAAA,CAII,YAHFA,mBAAA,CAA8C,gBAAtC,+BAA6B,GAASA,mBAAA,CAAM,O,iBAAA,KAClD,GAAAA,mBAAA,CAAwB,gBAAhB,SAAO,G,iBAAS,8EAE5B,E,OAGJA,mBAAA,CAgBM;IAhBDH,KAAK,EAAC;EAAwC,IACjDG,mBAAA,CAME;IALAH,KAAK,EAAC,uCAAuC;IAC7CmB,GAAG,EAAC,+BAA+B;IACnClB,IAAI,EAAC,KAAK;IACVmB,GAAG,EAAC,EAAE;IACN,aAAW,EAAC;MAEdjB,mBAAA,CAOM;IAPDH,KAAK,EAAC;EAAuB,IAChCG,mBAAA,CAKI,YAJFA,mBAAA,CAAkD,gBAA1C,mCAAiC,GAASA,mBAAA,CAAM,O,iBAAA,KACtD,GAAAA,mBAAA,CAAqB,gBAAb,MAAI,G,iBAAS,WAAS,GAAAA,mBAAA,CAAyB,gBAAjB,UAAQ,G,iBAAS,wHAG3D,E,6DAQZD,mBAAA,iBAAoB,EACpBC,mBAAA,CA0LM,OA1LNkB,UA0LM,GAzLJlB,mBAAA,CAwLM,OAxLNmB,UAwLM,GAvLJpB,mBAAA,uCAA0C,EAC1CC,mBAAA,CAgCM,OAhCNoB,WAgCM,GA/BJpB,mBAAA,CAiBS;IAhBPH,KAAK,EAAC,cAAc;IACnBa,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAU,MAAA,IAAEb,MAAA,CAAAc,gBAAgB;IACvB,YAAU,WAAWd,MAAA,CAAAe,YAAY;IAClChB,IAAI,EAAC;kCAELP,mBAAA,CAME;IALAgB,GAA+B,EAA/BpB,UAA+B;IAC/BqB,GAAG,EAAC,oBAAoB;IACxBpB,KAAK,EAAC,MAAM;IACZ2B,KAAK,EAAC,IAAI;IACVC,MAAM,EAAC;8BAETzB,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAe,IACxBG,mBAAA,CAAyC;IAArCH,KAAK,EAAC;EAAY,GAAC,eAAa,GACpCG,mBAAA,CAA0D;IAApDH,KAAK,EAAC;EAAe,GAAC,yBAAuB,E,oDAIvDE,mBAAA,wBAA2B,EAC3BC,mBAAA,CAUS;IATPH,KAAK,EAAC,oBAAoB;IACzBa,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEJ,MAAA,CAAAkB,mBAAA,IAAAlB,MAAA,CAAAkB,mBAAA,IAAAd,IAAA,CAAmB;IAC1B,YAAU,EAAEe,MAAA,CAAAC,gBAAgB;IAC5B,eAAa,GAAGD,MAAA,CAAAC,gBAAgB;IACjCrB,IAAI,EAAC;MAELP,mBAAA,CAA2E;IAArEH,KAAK,EAAAgC,eAAA,EAAC,gBAAgB;MAAAC,MAAA,GAAoBH,MAAA,CAAAC;IAAgB;2BAChE5B,mBAAA,CAA2E;IAArEH,KAAK,EAAAgC,eAAA,EAAC,gBAAgB;MAAAC,MAAA,GAAoBH,MAAA,CAAAC;IAAgB;2BAChE5B,mBAAA,CAA2E;IAArEH,KAAK,EAAAgC,eAAA,EAAC,gBAAgB;MAAAC,MAAA,GAAoBH,MAAA,CAAAC;IAAgB;2DAIpE7B,mBAAA,0CAA6C,EAC7CC,mBAAA,CA+DM,OA/DN+B,WA+DM,GA9DJ/B,mBAAA,CA6DK,MA7DLgC,WA6DK,GA5DHhC,mBAAA,CAcK,MAdLiC,WAcK,GAbHjC,mBAAA,CAYS;IAXPH,KAAK,EAAAgC,eAAA,EAAC,UAAU;MAAAC,MAAA,EACEH,MAAA,CAAAO,UAAU;IAAA;IAC3BxB,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAU,MAAA,IAAEb,MAAA,CAAAc,gBAAgB;IACxBxB,IAAI,EAAC,UAAU;IACd,cAAY,EAAE6B,MAAA,CAAAO,UAAU,4BAA4BC,SAAS;IAC9D5B,IAAI,EAAC;kCAELP,mBAAA,CAEM;IAFDH,KAAK,EAAC,UAAU;IAAC,aAAW,EAAC,MAAM;IAACuC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MAChErC,mBAAA,CAA+C;IAAzCsC,CAAC,EAAC;EAAqC,G,qBAE/CtC,mBAAA,CAAsB,cAAhB,WAAS,oB,0CAGnBA,mBAAA,CAcK,MAdLuC,WAcK,GAbHvC,mBAAA,CAYS;IAXPH,KAAK,EAAAgC,eAAA,EAAC,UAAU;MAAAC,MAAA,EACEH,MAAA,CAAAO,UAAU;IAAA;IAC3BxB,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAU,MAAA,IAAEb,MAAA,CAAAc,gBAAgB;IACxBxB,IAAI,EAAC,UAAU;IACd,cAAY,EAAE6B,MAAA,CAAAO,UAAU,2BAA2BC,SAAS;IAC7D5B,IAAI,EAAC;kCAELP,mBAAA,CAEM;IAFDH,KAAK,EAAC,UAAU;IAAC,aAAW,EAAC,MAAM;IAACuC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MAChErC,mBAAA,CAAmG;IAA7FsC,CAAC,EAAC;EAAyF,G,qBAEnGtC,mBAAA,CAAqB,cAAf,UAAQ,oB,0CAGlBA,mBAAA,CAcK,MAdLwC,WAcK,GAbHxC,mBAAA,CAYS;IAXPH,KAAK,EAAAgC,eAAA,EAAC,UAAU;MAAAC,MAAA,EACEH,MAAA,CAAAO,UAAU;IAAA;IAC3BxB,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAU,MAAA,IAAEb,MAAA,CAAAc,gBAAgB;IACxBxB,IAAI,EAAC,UAAU;IACd,cAAY,EAAE6B,MAAA,CAAAO,UAAU,2BAA2BC,SAAS;IAC7D5B,IAAI,EAAC;kCAELP,mBAAA,CAEM;IAFDH,KAAK,EAAC,UAAU;IAAC,aAAW,EAAC,MAAM;IAACuC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MAChErC,mBAAA,CAA8M;IAAxMsC,CAAC,EAAC;EAAoM,G,qBAE9MtC,mBAAA,CAAwB,cAAlB,aAAW,oB,0CAGrBA,mBAAA,CAcK,MAdLyC,WAcK,GAbHzC,mBAAA,CAYS;IAXPH,KAAK,EAAAgC,eAAA,EAAC,UAAU;MAAAC,MAAA,EACEH,MAAA,CAAAO,UAAU;IAAA;IAC3BxB,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAU,MAAA,IAAEb,MAAA,CAAAc,gBAAgB;IACxBxB,IAAI,EAAC,UAAU;IACd,cAAY,EAAE6B,MAAA,CAAAO,UAAU,uBAAuBC,SAAS;IACzD5B,IAAI,EAAC;kCAELP,mBAAA,CAEM;IAFDH,KAAK,EAAC,UAAU;IAAC,aAAW,EAAC,MAAM;IAACuC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MAChErC,mBAAA,CAAuT;IAAjTsC,CAAC,EAAC;EAA6S,G,qBAEvTtC,mBAAA,CAAiB,cAAX,MAAI,oB,8CAMlBD,mBAAA,iCAAoC,EACpCC,mBAAA,CAgFM,OAhFN0C,WAgFM,GA/EJ3C,mBAAA,YAAe,EACfC,mBAAA,CAmBM,OAnBN2C,WAmBM,GAlBJ3C,mBAAA,CAES;IAFDH,KAAK,EAAC,eAAe;IAAEa,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEJ,MAAA,CAAAoC,YAAA,IAAApC,MAAA,CAAAoC,YAAA,IAAAhC,IAAA,CAAY;IAAE,YAAU,EAAC,+BAA+B;IAAE,eAAa,EAAEJ,MAAA,CAAAqC;kCAC7G7C,mBAAA,CAAgD;IAA7CH,KAAK,EAAC,eAAe;IAAC,aAAW,EAAC;6DAEvCG,mBAAA,CAcM;IAdDH,KAAK,EAAAgC,eAAA,EAAC,YAAY;MAAAC,MAAA,EAAmBtB,MAAA,CAAAqC;IAAU;IAAI/C,IAAI,EAAC;kCAC3DE,mBAAA,CAAgF;IAAzE8C,GAAG,EAAC,eAAe;IAACjD,KAAK,EAAC;KAAU,+BAA6B,sB,gBACxEG,mBAAA,CAQE;IAPAc,EAAE,EAAC,eAAe;IAClBP,IAAI,EAAC,QAAQ;IACbwC,WAAW,EAAC,+BAA+B;IAC3ClD,KAAK,EAAC,cAAc;+DACXW,MAAA,CAAAwC,WAAW,GAAA3B,MAAA;IACnB4B,OAAK,EAAAtC,MAAA,QAAAA,MAAA,MAAAuC,SAAA,KAAAtC,IAAA,KAAQJ,MAAA,CAAA2C,aAAA,IAAA3C,MAAA,CAAA2C,aAAA,IAAAvC,IAAA,CAAa;IAC3BwC,YAAY,EAAC;iEAFJ5C,MAAA,CAAAwC,WAAW,E,GAItBhD,mBAAA,CAES;IAFDH,KAAK,EAAC,eAAe;IAAEa,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEJ,MAAA,CAAA2C,aAAA,IAAA3C,MAAA,CAAA2C,aAAA,IAAAvC,IAAA,CAAa;IAAE,YAAU,EAAC;kCAC9DZ,mBAAA,CAAgD;IAA7CH,KAAK,EAAC,eAAe;IAAC,aAAW,EAAC;oDAK3CE,mBAAA,mBAAsB,EACtBsD,YAAA,CAIEC,8BAAA;IAHCC,iBAAgB,EAAE/C,MAAA,CAAAgD,qBAAqB;IACvCC,mBAAkB,EAAEjD,MAAA,CAAAkD,uBAAuB;IAC3CC,OAAK,EAAEnD,MAAA,CAAAoD;oFAGV7D,mBAAA,kBAAqB,EACrBC,mBAAA,CAgDM;IAhDDH,KAAK,EAAAgC,eAAA,EAAC,cAAc;MAAAC,MAAA,EAAmBH,MAAA,CAAAkC;IAAgB;MAC1D7D,mBAAA,CAUS;IAVDH,KAAK,EAAC,UAAU;IAAEa,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEJ,MAAA,CAAAsD,wBAAA,IAAAtD,MAAA,CAAAsD,wBAAA,IAAAlD,IAAA,CAAwB;IAAE,YAAU,EAAC,mBAAmB;IAAE,eAAa,EAAEe,MAAA,CAAAkC;MACxG7D,mBAAA,CAGM,OAHN+D,WAGM,GAFOpC,MAAA,CAAAqC,UAAU,I,cAArBC,mBAAA,CAAgF;;IAAxDjD,GAAG,EAAEW,MAAA,CAAAqC,UAAU;IAAG/C,GAAG,EAAEU,MAAA,CAAAuC,QAAQ;IAAErE,KAAK,EAAC;yDAC/DoE,mBAAA,CAAwE,KAAxEE,WAAwE,G,GAE1EnE,mBAAA,CAGM,OAHNoE,WAGM,GAFJpE,mBAAA,CAA6C,QAA7CqE,WAA6C,EAAAC,gBAAA,CAAlB3C,MAAA,CAAAuC,QAAQ,kB,4BACnClE,mBAAA,CAA4C;IAAtCH,KAAK,EAAC;EAAW,GAAC,eAAa,qB,+BAEvCG,mBAAA,CAAqE;IAAlEH,KAAK,EAAC,oCAAoC;IAAC,aAAW,EAAC;6DAGjD8B,MAAA,CAAAkC,gBAAgB,I,cAA3BI,mBAAA,CAkCM,OAlCNM,WAkCM,GAjCJvE,mBAAA,CAKM,OALNwE,WAKM,GAJJxE,mBAAA,CAGM,OAHNyE,WAGM,GAFJzE,mBAAA,CAA+B,gBAAAsE,gBAAA,CAApB3C,MAAA,CAAAuC,QAAQ,kBACnBlE,mBAAA,CAA+C,QAA/C0E,WAA+C,EAAAJ,gBAAA,CAAnB3C,MAAA,CAAAgD,SAAS,iB,iCAGzC3E,mBAAA,CAAoC;IAA/BH,KAAK,EAAC;EAAkB,6BAC7BG,mBAAA,CAGI;IAHDC,IAAI,EAAC,GAAG;IAACJ,KAAK,EAAC,eAAe;IAAEa,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAU,MAAA,IAAEb,MAAA,CAAAc,gBAAgB;IAAaxB,IAAI,EAAC;kCAC1EE,mBAAA,CAA8C;IAA3CH,KAAK,EAAC,aAAa;IAAC,aAAW,EAAC;8BACnCG,mBAAA,CAAuB,cAAjB,YAAU,oB,IAElBA,mBAAA,CAGI;IAHDC,IAAI,EAAC,GAAG;IAACJ,KAAK,EAAC,eAAe;IAAEa,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAU,MAAA,IAAEb,MAAA,CAAAc,gBAAgB;IAAcxB,IAAI,EAAC;kCAC3EE,mBAAA,CAA6C;IAA1CH,KAAK,EAAC,YAAY;IAAC,aAAW,EAAC;8BAClCG,mBAAA,CAA6B,cAAvB,kBAAgB,oB,IAExBA,mBAAA,CAGI;IAHDC,IAAI,EAAC,GAAG;IAACJ,KAAK,EAAC,eAAe;IAAEa,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAU,MAAA,IAAEb,MAAA,CAAAc,gBAAgB;IAAexB,IAAI,EAAC;kCAC5EE,mBAAA,CAAgD;IAA7CH,KAAK,EAAC,eAAe;IAAC,aAAW,EAAC;8BACrCG,mBAAA,CAAyB,cAAnB,cAAY,oB,IAEpBA,mBAAA,CAGI;IAHDC,IAAI,EAAC,GAAG;IAACJ,KAAK,EAAC,eAAe;IAAEa,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAU,MAAA,IAAEb,MAAA,CAAAc,gBAAgB;IAAaxB,IAAI,EAAC;kCAC1EE,mBAAA,CAAiD;IAA9CH,KAAK,EAAC,gBAAgB;IAAC,aAAW,EAAC;8BACtCG,mBAAA,CAA4B,cAAtB,iBAAe,oB,gCAEvBA,mBAAA,CAAoC;IAA/BH,KAAK,EAAC;EAAkB,6BAC7BG,mBAAA,CAGI;IAHDC,IAAI,EAAC,GAAG;IAACJ,KAAK,EAAC,eAAe;IAAEa,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAU,MAAA,IAAEb,MAAA,CAAAc,gBAAgB;IAAUxB,IAAI,EAAC;kCACvEE,mBAAA,CAAyD;IAAtDH,KAAK,EAAC,wBAAwB;IAAC,aAAW,EAAC;8BAC9CG,mBAAA,CAA2B,cAArB,gBAAc,oB,gCAEtBA,mBAAA,CAAoC;IAA/BH,KAAK,EAAC;EAAkB,6BAC7BG,mBAAA,CAGI;IAHDC,IAAI,EAAC,GAAG;IAACJ,KAAK,EAAC,2BAA2B;IAAEa,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEJ,MAAA,CAAAoE,YAAA,IAAApE,MAAA,CAAAoE,YAAA,IAAAhE,IAAA,CAAY;IAAEd,IAAI,EAAC;kCACvEE,mBAAA,CAAsD;IAAnDH,KAAK,EAAC,qBAAqB;IAAC,aAAW,EAAC;8BAC3CG,mBAAA,CAAqB,cAAf,UAAQ,oB,kEAQ1BD,mBAAA,2BAA8B,EACQ4B,MAAA,CAAAkD,eAAe,I,cAArDZ,mBAAA,CAgBM,OAhBNa,WAgBM,GAfJ9E,mBAAA,CAcM,OAdN+E,WAcM,GAbJ/E,mBAAA,CAYM,OAZNgF,WAYM,GAXJhF,mBAAA,CAUK,MAVLiF,WAUK,GATHjF,mBAAA,CAKK,MALLkF,WAKK,GAJHlF,mBAAA,CAGI;IAHDC,IAAI,EAAC,GAAG;IAAES,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAU,MAAA,IAAEb,MAAA,CAAAc,gBAAgB;kCAClCtB,mBAAA,CAA8C;IAA3CH,KAAK,EAAC,aAAa;IAAC,aAAW,EAAC;+CAAW,aAEhD,E,MAEFG,mBAAA,CAEK,MAFLmF,WAEK,EAAAb,gBAAA,CADA9D,MAAA,CAAAe,YAAY,mB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}