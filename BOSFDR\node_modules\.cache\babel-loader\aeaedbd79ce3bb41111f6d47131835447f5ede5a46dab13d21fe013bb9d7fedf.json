{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"client-dashboard\"\n};\nconst _hoisted_2 = {\n  id: \"main-content\",\n  class: \"main-content\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_ClientHeader = _resolveComponent(\"ClientHeader\");\n  const _component_HeroSection = _resolveComponent(\"HeroSection\");\n  const _component_QuickActionsSection = _resolveComponent(\"QuickActionsSection\");\n  const _component_DocumentServicesSection = _resolveComponent(\"DocumentServicesSection\");\n  const _component_InformationSection = _resolveComponent(\"InformationSection\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" Background \"), _cache[0] || (_cache[0] = _createElementVNode(\"div\", {\n    class: \"background-container\"\n  }, [_createElementVNode(\"div\", {\n    class: \"background-image\"\n  }), _createElementVNode(\"div\", {\n    class: \"background-overlay\"\n  })], -1 /* HOISTED */)), _createCommentVNode(\" Client Header with Navigation \"), _createVNode(_component_ClientHeader, {\n    userName: $setup.userName,\n    userEmail: $setup.userEmail,\n    userAvatar: $setup.userAvatar,\n    showUserDropdown: $setup.showUserDropdown,\n    showBreadcrumbs: true,\n    onUserDropdownToggle: $setup.handleUserDropdownToggle,\n    onMenuAction: $setup.handleMenuAction,\n    onLogout: $setup.handleLogout,\n    onError: $setup.handleError,\n    onSearch: $setup.handleSearch\n  }, null, 8 /* PROPS */, [\"userName\", \"userEmail\", \"userAvatar\", \"showUserDropdown\", \"onUserDropdownToggle\", \"onMenuAction\", \"onLogout\", \"onError\", \"onSearch\"]), _createCommentVNode(\" Main Content \"), _createElementVNode(\"main\", _hoisted_2, [_createCommentVNode(\" Hero Section \"), _createVNode(_component_HeroSection, {\n    firstName: $setup.firstName,\n    totalRequests: $setup.totalRequests,\n    pendingRequests: $setup.pendingRequests,\n    onStartNewRequest: $setup.scrollToServices,\n    onViewRequests: $setup.goToMyRequests\n  }, null, 8 /* PROPS */, [\"firstName\", \"totalRequests\", \"pendingRequests\", \"onStartNewRequest\", \"onViewRequests\"]), _createCommentVNode(\" Quick Actions Section \"), _createVNode(_component_QuickActionsSection, {\n    onStartNewRequest: $setup.scrollToServices,\n    onViewRequests: $setup.goToMyRequests,\n    onViewDocuments: $setup.goToMyDocuments,\n    onGetHelp: $setup.openHelp\n  }, null, 8 /* PROPS */, [\"onStartNewRequest\", \"onViewRequests\", \"onViewDocuments\", \"onGetHelp\"]), _createCommentVNode(\" Document Services Section \"), _createVNode(_component_DocumentServicesSection, {\n    ref: \"servicesSection\",\n    documentTypes: $setup.documentTypes,\n    loading: $setup.loading,\n    error: $setup.error,\n    onSelectDocumentType: $setup.selectDocumentType,\n    onRetry: $setup.loadDocumentTypes\n  }, null, 8 /* PROPS */, [\"documentTypes\", \"loading\", \"error\", \"onSelectDocumentType\", \"onRetry\"]), _createCommentVNode(\" Information and Help Section \"), _createVNode(_component_InformationSection, {\n    onOpenHelp: $setup.openHelp,\n    onContactSupport: $setup.contactSupport\n  }, null, 8 /* PROPS */, [\"onOpenHelp\", \"onContactSupport\"])])]);\n}", "map": {"version": 3, "names": ["class", "id", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_createVNode", "_component_ClientHeader", "userName", "$setup", "userEmail", "userAvatar", "showUserDropdown", "showBreadcrumbs", "onUserDropdownToggle", "handleUserDropdownToggle", "onMenuAction", "handleMenuAction", "onLogout", "handleLogout", "onError", "handleError", "onSearch", "handleSearch", "_hoisted_2", "_component_HeroSection", "firstName", "totalRequests", "pendingRequests", "onStartNewRequest", "scrollToServices", "onViewRequests", "goToMyRequests", "_component_QuickActionsSection", "onViewDocuments", "goToMyDocuments", "onGetHelp", "openHelp", "_component_DocumentServicesSection", "ref", "documentTypes", "loading", "error", "onSelectDocumentType", "selectDocumentType", "onRetry", "loadDocumentTypes", "_component_InformationSection", "onOpenHelp", "onContactSupport", "contactSupport"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\client\\NewDocumentRequest.vue"], "sourcesContent": ["<template>\n  <div class=\"client-dashboard\">\n    <!-- Background -->\n    <div class=\"background-container\">\n      <div class=\"background-image\"></div>\n      <div class=\"background-overlay\"></div>\n    </div>\n\n    <!-- Client Header with Navigation -->\n    <ClientHeader\n      :userName=\"userName\"\n      :userEmail=\"userEmail\"\n      :userAvatar=\"userAvatar\"\n      :showUserDropdown=\"showUserDropdown\"\n      :showBreadcrumbs=\"true\"\n      @user-dropdown-toggle=\"handleUserDropdownToggle\"\n      @menu-action=\"handleMenuAction\"\n      @logout=\"handleLogout\"\n      @error=\"handleError\"\n      @search=\"handleSearch\"\n    />\n\n    <!-- Main Content -->\n    <main id=\"main-content\" class=\"main-content\">\n      <!-- Hero Section -->\n      <HeroSection\n        :firstName=\"firstName\"\n        :totalRequests=\"totalRequests\"\n        :pendingRequests=\"pendingRequests\"\n        @start-new-request=\"scrollToServices\"\n        @view-requests=\"goToMyRequests\"\n      />\n\n      <!-- Quick Actions Section -->\n      <QuickActionsSection\n        @start-new-request=\"scrollToServices\"\n        @view-requests=\"goToMyRequests\"\n        @view-documents=\"goToMyDocuments\"\n        @get-help=\"openHelp\"\n      />\n\n      <!-- Document Services Section -->\n      <DocumentServicesSection\n        ref=\"servicesSection\"\n        :documentTypes=\"documentTypes\"\n        :loading=\"loading\"\n        :error=\"error\"\n        @select-document-type=\"selectDocumentType\"\n        @retry=\"loadDocumentTypes\"\n      />\n\n      <!-- Information and Help Section -->\n      <InformationSection\n        @open-help=\"openHelp\"\n        @contact-support=\"contactSupport\"\n      />\n    </main>\n  </div>\n</template>\n\n<script>\nimport { ref, onMounted, onBeforeUnmount, nextTick } from 'vue';\nimport documentRequestService from '@/services/documentRequestService';\nimport ClientHeader from './ClientHeader.vue';\nimport HeroSection from './sections/HeroSection.vue';\nimport QuickActionsSection from './sections/QuickActionsSection.vue';\nimport DocumentServicesSection from './sections/DocumentServicesSection.vue';\nimport InformationSection from './sections/InformationSection.vue';\nimport unifiedAuthService from '@/services/unifiedAuthService';\n\nexport default {\n  name: 'NewDocumentRequest',\n  components: {\n    ClientHeader,\n    HeroSection,\n    QuickActionsSection,\n    DocumentServicesSection,\n    InformationSection\n  },\n  setup() {\n    // Reactive state\n    const documentTypes = ref([]);\n    const loading = ref(true);\n    const error = ref(null);\n\n    // Header state\n    const showUserDropdown = ref(false);\n\n    // User data\n    const userName = ref('User');\n    const userEmail = ref('<EMAIL>');\n    const userAvatar = ref(null);\n    const firstName = ref('User');\n    const totalRequests = ref(0);\n    const pendingRequests = ref(0);\n\n    // Template refs\n    const servicesSection = ref(null);\n    // Methods\n    const loadUserData = async () => {\n      try {\n        const currentUser = unifiedAuthService.getCurrentUser();\n        if (currentUser) {\n          userName.value = currentUser.username || 'User';\n          userEmail.value = currentUser.email || '<EMAIL>';\n          firstName.value = currentUser.first_name || currentUser.username || 'User';\n          userAvatar.value = currentUser.avatar || null;\n        }\n      } catch (error) {\n        console.error('Error loading user data:', error);\n      }\n    };\n\n    const loadUserStats = async () => {\n      try {\n        // TODO: Implement API call to get user statistics\n        // For now, using placeholder data\n        totalRequests.value = 5;\n        pendingRequests.value = 2;\n      } catch (error) {\n        console.error('Error loading user stats:', error);\n      }\n    };\n\n    const loadDocumentTypes = async () => {\n      try {\n        loading.value = true;\n        error.value = null;\n\n        const response = await documentRequestService.getDocumentTypes();\n        documentTypes.value = response.data || [];\n\n      } catch (err) {\n        console.error('Error loading document types:', err);\n        error.value = err.response?.data?.message || 'Failed to load available services';\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    const selectDocumentType = (documentType) => {\n      if (!documentType.is_active) return;\n\n      const routeName = getRouteForDocumentType(documentType.type_name);\n      if (routeName) {\n        console.log('Navigate to:', routeName, 'with ID:', documentType.id);\n      }\n    };\n\n    const getRouteForDocumentType = (typeName) => {\n      const routes = {\n        'Barangay Clearance': 'BarangayClearanceRequest',\n        'Cedula': 'CedulaRequest'\n      };\n      return routes[typeName];\n    };\n\n    // Header event handlers\n    const handleUserDropdownToggle = () => {\n      showUserDropdown.value = !showUserDropdown.value;\n    };\n\n    const handleMenuAction = (action) => {\n      console.log('Menu action:', action);\n    };\n\n    const handleLogout = () => {\n      try {\n        unifiedAuthService.logout();\n        console.log('Logout and navigate to WelcomePage');\n      } catch (err) {\n        console.error('Logout error:', err);\n      }\n    };\n\n    const handleError = (err) => {\n      console.error('Header error:', err);\n    };\n\n    const handleSearch = (query) => {\n      console.log('Search query:', query);\n    };\n\n    // Navigation methods\n    const scrollToServices = () => {\n      servicesSection.value?.$el?.scrollIntoView({ behavior: 'smooth' });\n    };\n\n    const goToMyRequests = () => {\n      console.log('Navigate to MyRequests');\n    };\n\n    const goToMyDocuments = () => {\n      console.log('Navigate to MyDocuments');\n    };\n\n    const openHelp = () => {\n      console.log('Opening help...');\n    };\n\n    const contactSupport = () => {\n      console.log('Contacting support...');\n    };\n\n    // Lifecycle\n    onMounted(async () => {\n      await loadUserData();\n      await loadDocumentTypes();\n      await loadUserStats();\n    });\n\n    // Return reactive state and methods for template\n    return {\n      // State\n      documentTypes,\n      loading,\n      error,\n      showUserDropdown,\n      userName,\n      userEmail,\n      userAvatar,\n      firstName,\n      totalRequests,\n      pendingRequests,\n      servicesSection,\n\n      // Methods\n      selectDocumentType,\n      loadDocumentTypes,\n      handleUserDropdownToggle,\n      handleMenuAction,\n      handleLogout,\n      handleError,\n      handleSearch,\n      scrollToServices,\n      goToMyRequests,\n      goToMyDocuments,\n      openHelp,\n      contactSupport\n    };\n  }\n};\n</script>\n\n<style scoped>\n/* Import design tokens from ClientHeader */\n@import './css/clientHeader.css';\n\n/* Client Dashboard Layout */\n.client-dashboard {\n  min-height: 100vh;\n  background: var(--color-bg-primary);\n  position: relative;\n/* Background Setup */\n.background-container {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: -2;\n}\n\n.background-image {\n  width: 100%;\n  height: 100%;\n  background-image: url('@/assets/bula-request-background-pic.png');\n  background-size: cover;\n  background-position: center;\n  background-repeat: no-repeat;\n  background-attachment: fixed;\n  opacity: 0.03;\n}\n\n.background-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    135deg,\n    rgba(0, 94, 162, 0.02) 0%,\n    rgba(35, 120, 195, 0.03) 100%\n  );\n  z-index: -1;\n}\n\n/* Main Content */\n.main-content {\n  margin-top: 140px; /* Account for fixed header */\n  padding-bottom: var(--spacing-16);\n  position: relative;\n  z-index: 1;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAkB;;EAsBrBC,EAAE,EAAC,cAAc;EAACD,KAAK,EAAC;;;;;;;;uBAtBhCE,mBAAA,CAwDM,OAxDNC,UAwDM,GAvDJC,mBAAA,gBAAmB,E,0BACnBC,mBAAA,CAGM;IAHDL,KAAK,EAAC;EAAsB,IAC/BK,mBAAA,CAAoC;IAA/BL,KAAK,EAAC;EAAkB,IAC7BK,mBAAA,CAAsC;IAAjCL,KAAK,EAAC;EAAoB,G,sBAGjCI,mBAAA,mCAAsC,EACtCE,YAAA,CAWEC,uBAAA;IAVCC,QAAQ,EAAEC,MAAA,CAAAD,QAAQ;IAClBE,SAAS,EAAED,MAAA,CAAAC,SAAS;IACpBC,UAAU,EAAEF,MAAA,CAAAE,UAAU;IACtBC,gBAAgB,EAAEH,MAAA,CAAAG,gBAAgB;IAClCC,eAAe,EAAE,IAAI;IACrBC,oBAAoB,EAAEL,MAAA,CAAAM,wBAAwB;IAC9CC,YAAW,EAAEP,MAAA,CAAAQ,gBAAgB;IAC7BC,QAAM,EAAET,MAAA,CAAAU,YAAY;IACpBC,OAAK,EAAEX,MAAA,CAAAY,WAAW;IAClBC,QAAM,EAAEb,MAAA,CAAAc;mKAGXnB,mBAAA,kBAAqB,EACrBC,mBAAA,CAiCO,QAjCPmB,UAiCO,GAhCLpB,mBAAA,kBAAqB,EACrBE,YAAA,CAMEmB,sBAAA;IALCC,SAAS,EAAEjB,MAAA,CAAAiB,SAAS;IACpBC,aAAa,EAAElB,MAAA,CAAAkB,aAAa;IAC5BC,eAAe,EAAEnB,MAAA,CAAAmB,eAAe;IAChCC,iBAAiB,EAAEpB,MAAA,CAAAqB,gBAAgB;IACnCC,cAAa,EAAEtB,MAAA,CAAAuB;qHAGlB5B,mBAAA,2BAA8B,EAC9BE,YAAA,CAKE2B,8BAAA;IAJCJ,iBAAiB,EAAEpB,MAAA,CAAAqB,gBAAgB;IACnCC,cAAa,EAAEtB,MAAA,CAAAuB,cAAc;IAC7BE,eAAc,EAAEzB,MAAA,CAAA0B,eAAe;IAC/BC,SAAQ,EAAE3B,MAAA,CAAA4B;oGAGbjC,mBAAA,+BAAkC,EAClCE,YAAA,CAOEgC,kCAAA;IANAC,GAAG,EAAC,iBAAiB;IACpBC,aAAa,EAAE/B,MAAA,CAAA+B,aAAa;IAC5BC,OAAO,EAAEhC,MAAA,CAAAgC,OAAO;IAChBC,KAAK,EAAEjC,MAAA,CAAAiC,KAAK;IACZC,oBAAoB,EAAElC,MAAA,CAAAmC,kBAAkB;IACxCC,OAAK,EAAEpC,MAAA,CAAAqC;qGAGV1C,mBAAA,kCAAqC,EACrCE,YAAA,CAGEyC,6BAAA;IAFCC,UAAS,EAAEvC,MAAA,CAAA4B,QAAQ;IACnBY,gBAAe,EAAExC,MAAA,CAAAyC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}