{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, createVNode as _createVNode, openBlock as _openBlock, createBlock as _createBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"document-request-page\"\n};\nconst _hoisted_2 = {\n  id: \"main-content\",\n  class: \"main-content\",\n  role: \"main\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" Skip to main content link for accessibility \"), _cache[0] || (_cache[0] = _createElementVNode(\"a\", {\n    href: \"#main-content\",\n    class: \"skip-link\"\n  }, \"Skip to main content\", -1 /* HOISTED */)), _createCommentVNode(\" Client Header with Navigation \"), _createVNode($setup[\"ClientHeader\"], {\n    \"user-name\": $setup.userState.name,\n    \"user-email\": $setup.userState.email,\n    \"user-avatar\": $setup.userState.avatar,\n    \"show-user-dropdown\": $setup.uiState.showUserDropdown,\n    \"active-menu\": 'services',\n    \"show-breadcrumbs\": true,\n    onUserDropdownToggle: $setup.toggleUserDropdown,\n    onMenuAction: $setup.handleMenuAction,\n    onLogout: $setup.handleLogout,\n    onError: $setup.handleError,\n    onSearch: $setup.handleSearch\n  }, null, 8 /* PROPS */, [\"user-name\", \"user-email\", \"user-avatar\", \"show-user-dropdown\"]), _createCommentVNode(\" Main Content \"), _createElementVNode(\"main\", _hoisted_2, [_createCommentVNode(\" Page Header \"), _createVNode($setup[\"PageHeader\"], {\n    title: \"Document Request Services\",\n    subtitle: \"Request official documents from Barangay Bula through our secure digital platform\",\n    breadcrumbs: $setup.breadcrumbs\n  }, null, 8 /* PROPS */, [\"breadcrumbs\"]), _createCommentVNode(\" User Dashboard Stats \"), _createVNode($setup[\"UserStatsSection\"], {\n    \"total-requests\": $setup.userStats.totalRequests,\n    \"pending-requests\": $setup.userStats.pendingRequests,\n    \"completed-requests\": $setup.userStats.completedRequests,\n    loading: $setup.userStats.loading,\n    onViewRequests: $setup.navigateToRequests\n  }, null, 8 /* PROPS */, [\"total-requests\", \"pending-requests\", \"completed-requests\", \"loading\", \"onViewRequests\"]), _createCommentVNode(\" Quick Actions \"), _createVNode($setup[\"QuickActionsSection\"], {\n    onNewRequest: $setup.scrollToServices,\n    onViewRequests: $setup.navigateToRequests,\n    onViewProfile: $setup.navigateToProfile,\n    onContactSupport: $setup.openSupportModal\n  }, null, 8 /* PROPS */, [\"onViewRequests\", \"onViewProfile\"]), _createCommentVNode(\" Document Services \"), _createVNode($setup[\"DocumentServicesSection\"], {\n    ref: \"servicesSection\",\n    \"document-types\": $setup.documentTypes.data,\n    loading: $setup.documentTypes.loading,\n    error: $setup.documentTypes.error,\n    onSelectDocument: $setup.selectDocumentType,\n    onRetryLoad: $setup.loadDocumentTypes\n  }, null, 8 /* PROPS */, [\"document-types\", \"loading\", \"error\", \"onRetryLoad\"]), _createCommentVNode(\" Information Section \"), _createVNode($setup[\"InformationSection\"], {\n    onOpenHelp: $setup.openHelpModal,\n    onContactSupport: $setup.openSupportModal\n  })]), _createCommentVNode(\" Modals \"), $setup.uiState.showHelpModal ? (_openBlock(), _createBlock($setup[\"HelpModal\"], {\n    key: 0,\n    onClose: $setup.closeHelpModal\n  })) : _createCommentVNode(\"v-if\", true), $setup.uiState.showSupportModal ? (_openBlock(), _createBlock($setup[\"SupportModal\"], {\n    key: 1,\n    onClose: $setup.closeSupportModal\n  })) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "id", "role", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "href", "_createVNode", "$setup", "userState", "name", "email", "avatar", "uiState", "showUserDropdown", "onUserDropdownToggle", "toggleUserDropdown", "onMenuAction", "handleMenuAction", "onLogout", "handleLogout", "onError", "handleError", "onSearch", "handleSearch", "_hoisted_2", "title", "subtitle", "breadcrumbs", "userStats", "totalRequests", "pendingRequests", "completedRequests", "loading", "onViewRequests", "navigateToRequests", "onNewRequest", "scrollToServices", "onViewProfile", "navigateToProfile", "onContactSupport", "openSupportModal", "ref", "documentTypes", "data", "error", "onSelectDocument", "selectDocumentType", "onRetryLoad", "loadDocumentTypes", "onOpenHelp", "openHelpModal", "showHelpModal", "_createBlock", "onClose", "closeHelpModal", "showSupportModal", "closeSupportModal"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\client\\NewDocumentRequest.vue"], "sourcesContent": ["<template>\n  <div class=\"document-request-page\">\n    <!-- Skip to main content link for accessibility -->\n    <a href=\"#main-content\" class=\"skip-link\">Skip to main content</a>\n\n    <!-- Client Header with Navigation -->\n    <ClientHeader\n      :user-name=\"userState.name\"\n      :user-email=\"userState.email\"\n      :user-avatar=\"userState.avatar\"\n      :show-user-dropdown=\"uiState.showUserDropdown\"\n      :active-menu=\"'services'\"\n      :show-breadcrumbs=\"true\"\n      @user-dropdown-toggle=\"toggleUserDropdown\"\n      @menu-action=\"handleMenuAction\"\n      @logout=\"handleLogout\"\n      @error=\"handleError\"\n      @search=\"handleSearch\"\n    />\n\n    <!-- Main Content -->\n    <main id=\"main-content\" class=\"main-content\" role=\"main\">\n      <!-- Page Header -->\n      <PageHeader\n        title=\"Document Request Services\"\n        subtitle=\"Request official documents from Barangay Bula through our secure digital platform\"\n        :breadcrumbs=\"breadcrumbs\"\n      />\n\n      <!-- User Dashboard Stats -->\n      <UserStatsSection\n        :total-requests=\"userStats.totalRequests\"\n        :pending-requests=\"userStats.pendingRequests\"\n        :completed-requests=\"userStats.completedRequests\"\n        :loading=\"userStats.loading\"\n        @view-requests=\"navigateToRequests\"\n      />\n\n      <!-- Quick Actions -->\n      <QuickActionsSection\n        @new-request=\"scrollToServices\"\n        @view-requests=\"navigateToRequests\"\n        @view-profile=\"navigateToProfile\"\n        @contact-support=\"openSupportModal\"\n      />\n\n      <!-- Document Services -->\n      <DocumentServicesSection\n        ref=\"servicesSection\"\n        :document-types=\"documentTypes.data\"\n        :loading=\"documentTypes.loading\"\n        :error=\"documentTypes.error\"\n        @select-document=\"selectDocumentType\"\n        @retry-load=\"loadDocumentTypes\"\n      />\n\n      <!-- Information Section -->\n      <InformationSection\n        @open-help=\"openHelpModal\"\n        @contact-support=\"openSupportModal\"\n      />\n    </main>\n\n    <!-- Modals -->\n    <HelpModal\n      v-if=\"uiState.showHelpModal\"\n      @close=\"closeHelpModal\"\n    />\n\n    <SupportModal\n      v-if=\"uiState.showSupportModal\"\n      @close=\"closeSupportModal\"\n    />\n  </div>\n\n\n\n\n\n\n</template>\n\n<script setup>\nimport { ref, reactive, computed, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { useUserStore } from '@/stores/userStore'\nimport { useDocumentStore } from '@/stores/documentStore'\nimport { useNotificationStore } from '@/stores/notificationStore'\n\n// Components\nimport ClientHeader from './ClientHeader.vue'\nimport PageHeader from './components/PageHeader.vue'\nimport UserStatsSection from './components/UserStatsSection.vue'\nimport QuickActionsSection from './components/QuickActionsSection.vue'\nimport DocumentServicesSection from './components/DocumentServicesSection.vue'\nimport InformationSection from './components/InformationSection.vue'\nimport HelpModal from './components/HelpModal.vue'\nimport SupportModal from './components/SupportModal.vue'\n\n// Composables\nimport { useDocumentTypes } from '@/composables/useDocumentTypes'\nimport { useUserStats } from '@/composables/useUserStats'\nimport { useNavigation } from '@/composables/useNavigation'\n\n// Router and stores\nconst router = useRouter()\nconst userStore = useUserStore()\nconst documentStore = useDocumentStore()\nconst notificationStore = useNotificationStore()\n\n// Reactive state\nconst uiState = reactive({\n  showUserDropdown: false,\n  showHelpModal: false,\n  showSupportModal: false\n})\n\n// User state\nconst userState = computed(() => ({\n  name: userStore.currentUser?.username || 'User',\n  email: userStore.currentUser?.email || '<EMAIL>',\n  avatar: userStore.currentUser?.avatar || null,\n  firstName: userStore.currentUser?.first_name || userStore.currentUser?.username || 'User'\n}))\n\n// Breadcrumbs\nconst breadcrumbs = computed(() => [\n  { label: 'Dashboard', href: '/dashboard', icon: 'fas fa-home' },\n  { label: 'Document Services', current: true }\n])\n\n// Composables\nconst { documentTypes, loadDocumentTypes } = useDocumentTypes()\nconst { userStats } = useUserStats()\nconst { navigateToRequests, navigateToProfile, navigateToDocument } = useNavigation()\n\n// Refs\nconst servicesSection = ref(null)\n\n// Methods\nconst toggleUserDropdown = () => {\n  uiState.showUserDropdown = !uiState.showUserDropdown\n}\n\nconst handleMenuAction = (action) => {\n  switch (action) {\n    case 'dashboard':\n      router.push({ name: 'ClientDashboard' })\n      break\n    case 'services':\n      scrollToServices()\n      break\n    case 'requests':\n      navigateToRequests()\n      break\n    case 'profile':\n      navigateToProfile()\n      break\n    case 'help':\n      openHelpModal()\n      break\n    default:\n      console.log('Menu action:', action)\n  }\n}\n\nconst handleLogout = async () => {\n  try {\n    await userStore.logout()\n    router.push({ name: 'WelcomePage' })\n  } catch (error) {\n    console.error('Logout error:', error)\n    notificationStore.addNotification({\n      type: 'error',\n      title: 'Logout Failed',\n      message: 'Unable to logout. Please try again.'\n    })\n  }\n}\n\nconst handleError = (error) => {\n  console.error('Component error:', error)\n  notificationStore.addNotification({\n    type: 'error',\n    title: 'Error',\n    message: error.message || 'An unexpected error occurred'\n  })\n}\n\nconst handleSearch = (query) => {\n  // TODO: Implement search functionality\n  console.log('Search query:', query)\n  // Could search through documents, services, or requests\n}\n\nconst scrollToServices = () => {\n  servicesSection.value?.$el?.scrollIntoView({ behavior: 'smooth' })\n}\n\nconst selectDocumentType = (documentType) => {\n  if (!documentType.is_active) {\n    notificationStore.addNotification({\n      type: 'warning',\n      title: 'Service Unavailable',\n      message: `${documentType.type_name} service is currently unavailable.`\n    })\n    return\n  }\n\n  navigateToDocument(documentType)\n}\n\nconst openHelpModal = () => {\n  uiState.showHelpModal = true\n}\n\nconst closeHelpModal = () => {\n  uiState.showHelpModal = false\n}\n\nconst openSupportModal = () => {\n  uiState.showSupportModal = true\n}\n\nconst closeSupportModal = () => {\n  uiState.showSupportModal = false\n}\n\n// Lifecycle\nonMounted(async () => {\n  try {\n    await Promise.all([\n      userStore.loadCurrentUser(),\n      loadDocumentTypes(),\n      userStats.load()\n    ])\n  } catch (error) {\n    console.error('Failed to load initial data:', error)\n    handleError(error)\n  }\n})\n</script>\n\n<style scoped>\n/* Modern Government Portal Styles - USWDS 3.0 Inspired */\n\n/* Skip link for accessibility */\n.skip-link {\n  position: absolute;\n  top: -40px;\n  left: 6px;\n  background: var(--uswds-color-primary-darker);\n  color: white;\n  padding: 8px;\n  text-decoration: none;\n  border-radius: 0 0 4px 4px;\n  z-index: 1000;\n  font-weight: 600;\n}\n\n.skip-link:focus {\n  top: 0;\n}\n\n/* Main page container */\n.document-request-page {\n  min-height: 100vh;\n  background: var(--uswds-color-base-lightest);\n  font-family: var(--uswds-font-family-sans);\n  line-height: 1.6;\n  color: var(--uswds-color-ink);\n}\n\n/* Main content area */\n.main-content {\n  margin-top: var(--header-height, 140px);\n  padding-bottom: var(--uswds-spacing-8);\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .main-content {\n    margin-top: var(--header-height-mobile, 120px);\n    padding: 0 var(--uswds-spacing-2);\n  }\n}\n\n/* Focus management for accessibility */\n.main-content:focus {\n  outline: 2px solid var(--uswds-color-focus);\n  outline-offset: 2px;\n}\n\n/* Animation for smooth page transitions */\n.document-request-page {\n  animation: fadeIn 0.3s ease-in-out;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Reduced motion support */\n@media (prefers-reduced-motion: reduce) {\n  .document-request-page {\n    animation: none;\n  }\n\n  * {\n    transition: none !important;\n    animation: none !important;\n  }\n}\n\n\n\n\n\n/* Services Section */\n.services-section {\n  padding: 2rem 0;\n  position: relative;\n  z-index: 1;\n}\n\n.services-section .section-title {\n  color: white;\n}\n\n.services-section .section-description {\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.loading-container, .error-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n  text-align: center;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: 1rem;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.loading-spinner i {\n  font-size: 3rem;\n  color: #1e3a8a;\n  margin-bottom: 1rem;\n}\n\n.error-content {\n  max-width: 400px;\n}\n\n.error-content i {\n  font-size: 3rem;\n  color: #dc2626;\n  margin-bottom: 1rem;\n}\n\n.retry-btn {\n  background: linear-gradient(135deg, #1e3a8a, #1e40af);\n  color: white;\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 0.75rem;\n  cursor: pointer;\n  margin-top: 1rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);\n}\n\n.retry-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.4);\n}\n\n.document-types-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n  gap: 1.5rem;\n}\n\n.document-card {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border: 2px solid rgba(30, 58, 138, 0.2);\n  border-radius: 1rem;\n  padding: 2rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n}\n\n.document-card:hover:not(.disabled) {\n  border-color: #1e3a8a;\n  transform: translateY(-5px);\n  box-shadow: 0 12px 30px rgba(30, 58, 138, 0.2);\n  background: rgba(255, 255, 255, 1);\n}\n\n.document-card.disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.document-icon {\n  flex-shrink: 0;\n  width: 4rem;\n  height: 4rem;\n  background: linear-gradient(135deg, #1e3a8a, #1e40af);\n  border-radius: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 1.5rem;\n  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);\n}\n\n.document-content {\n  flex: 1;\n}\n\n.document-title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1e3a8a;\n  margin-bottom: 0.5rem;\n}\n\n.document-description {\n  color: #6b7280;\n  margin-bottom: 1rem;\n  line-height: 1.5;\n}\n\n.document-details {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.fee-info {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.fee-label {\n  color: #9ca3af;\n  font-size: 0.9rem;\n}\n\n.fee-amount {\n  font-weight: 600;\n  color: #059669;\n  font-size: 1.1rem;\n}\n\n.processing-time {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #9ca3af;\n  font-size: 0.9rem;\n}\n\n.document-action {\n  flex-shrink: 0;\n  display: flex;\n  align-items: center;\n}\n\n.document-action i {\n  color: #d1d5db;\n  font-size: 1.25rem;\n}\n\n.status-badge.unavailable {\n  background: #fecaca;\n  color: #dc2626;\n  padding: 0.25rem 0.75rem;\n  border-radius: 1rem;\n  font-size: 0.8rem;\n  font-weight: 500;\n}\n\n/* Info Section */\n.info-section {\n  padding: 2rem 0;\n  position: relative;\n  z-index: 1;\n}\n\n.info-grid {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 2rem;\n}\n\n.info-card, .help-card {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(30, 58, 138, 0.2);\n  border-radius: 1rem;\n  padding: 2rem;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n}\n\n.info-card:hover, .help-card:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 8px 30px rgba(30, 58, 138, 0.15);\n  background: rgba(255, 255, 255, 1);\n  border-color: #1e3a8a;\n}\n\n.info-header, .help-header {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  margin-bottom: 1.5rem;\n}\n\n.info-header h3, .help-header h3 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1e3a8a;\n  margin: 0;\n}\n\n.info-header i {\n  color: #1e3a8a;\n  font-size: 1.25rem;\n}\n\n.help-header i {\n  color: #059669;\n  font-size: 1.25rem;\n}\n\n.info-list {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.info-list li {\n  display: flex;\n  align-items: flex-start;\n  gap: 0.75rem;\n  margin-bottom: 1rem;\n  color: #6b7280;\n  line-height: 1.6;\n}\n\n.info-list i {\n  color: #059669;\n  margin-top: 0.125rem;\n  flex-shrink: 0;\n}\n\n.help-content p {\n  color: #6b7280;\n  margin-bottom: 1.5rem;\n  line-height: 1.6;\n}\n\n.help-actions {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.help-btn, .contact-btn {\n  background: rgba(255, 255, 255, 0.9);\n  backdrop-filter: blur(5px);\n  border: 2px solid rgba(30, 58, 138, 0.3);\n  padding: 0.875rem 1.25rem;\n  border-radius: 0.75rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  color: #6b7280;\n  font-weight: 500;\n  font-size: 0.95rem;\n}\n\n.help-btn:hover {\n  border-color: #1e3a8a;\n  color: #1e3a8a;\n  background: rgba(30, 58, 138, 0.1);\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.2);\n}\n\n.contact-btn:hover {\n  border-color: #059669;\n  color: #059669;\n  background: rgba(5, 150, 105, 0.1);\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(5, 150, 105, 0.2);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .container {\n    padding: 0 0.75rem;\n  }\n\n  .welcome-header {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n    padding: 1.5rem;\n  }\n\n  .welcome-stats {\n    justify-content: center;\n  }\n\n  .stat-card {\n    min-width: 120px;\n  }\n\n  .quick-actions-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n\n  .action-card {\n    padding: 1.5rem;\n  }\n\n  .document-types-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n\n  .document-card {\n    flex-direction: column;\n    text-align: center;\n    padding: 1.5rem;\n  }\n\n  .info-grid {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n\n  .info-card, .help-card {\n    padding: 1.5rem;\n  }\n\n  .loading-container, .error-container {\n    padding: 3rem 1.5rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .container {\n    padding: 0 0.5rem;\n  }\n\n  .welcome-section,\n  .quick-actions-section,\n  .services-section,\n  .info-section {\n    padding: 1.5rem 0;\n  }\n\n  .welcome-header {\n    padding: 1rem;\n  }\n\n  .stat-card {\n    padding: 1rem;\n  }\n\n  .stat-number {\n    font-size: 1.5rem;\n  }\n\n  .action-card {\n    padding: 1rem;\n    flex-direction: column;\n    text-align: center;\n    gap: 1rem;\n  }\n\n  .action-icon {\n    width: 3rem;\n    height: 3rem;\n  }\n\n  .document-card {\n    padding: 1rem;\n  }\n\n  .info-card, .help-card {\n    padding: 1rem;\n  }\n\n  .loading-container, .error-container {\n    padding: 2rem 1rem;\n  }\n}\n\n/* Animations */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.welcome-section,\n.quick-actions-section,\n.services-section,\n.info-section {\n  animation: fadeInUp 0.8s ease-out;\n}\n\n.quick-actions-section {\n  animation-delay: 0.2s;\n}\n\n.services-section {\n  animation-delay: 0.4s;\n}\n\n.info-section {\n  animation-delay: 0.6s;\n}\n\n/* Accessibility */\n@media (prefers-reduced-motion: reduce) {\n  * {\n    animation: none !important;\n    transition: none !important;\n  }\n\n  .action-card:hover,\n  .document-card:hover,\n  .info-card:hover,\n  .help-card:hover {\n    transform: none;\n  }\n}\n\n/* Focus styles */\n.action-card:focus,\n.document-card:focus,\n.help-btn:focus,\n.contact-btn:focus,\n.retry-btn:focus {\n  outline: 3px solid #fbbf24;\n  outline-offset: 2px;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAuB;;EAoB1BC,EAAE,EAAC,cAAc;EAACD,KAAK,EAAC,cAAc;EAACE,IAAI,EAAC;;;uBApBpDC,mBAAA,CAwEM,OAxENC,UAwEM,GAvEJC,mBAAA,iDAAoD,E,0BACpDC,mBAAA,CAAkE;IAA/DC,IAAI,EAAC,eAAe;IAACP,KAAK,EAAC;KAAY,sBAAoB,sBAE9DK,mBAAA,mCAAsC,EACtCG,YAAA,CAYEC,MAAA;IAXC,WAAS,EAAEA,MAAA,CAAAC,SAAS,CAACC,IAAI;IACzB,YAAU,EAAEF,MAAA,CAAAC,SAAS,CAACE,KAAK;IAC3B,aAAW,EAAEH,MAAA,CAAAC,SAAS,CAACG,MAAM;IAC7B,oBAAkB,EAAEJ,MAAA,CAAAK,OAAO,CAACC,gBAAgB;IAC5C,aAAW,EAAE,UAAU;IACvB,kBAAgB,EAAE,IAAI;IACtBC,oBAAoB,EAAEP,MAAA,CAAAQ,kBAAkB;IACxCC,YAAW,EAAET,MAAA,CAAAU,gBAAgB;IAC7BC,QAAM,EAAEX,MAAA,CAAAY,YAAY;IACpBC,OAAK,EAAEb,MAAA,CAAAc,WAAW;IAClBC,QAAM,EAAEf,MAAA,CAAAgB;6FAGXpB,mBAAA,kBAAqB,EACrBC,mBAAA,CAwCO,QAxCPoB,UAwCO,GAvCLrB,mBAAA,iBAAoB,EACpBG,YAAA,CAIEC,MAAA;IAHAkB,KAAK,EAAC,2BAA2B;IACjCC,QAAQ,EAAC,mFAAmF;IAC3FC,WAAW,EAAEpB,MAAA,CAAAoB;4CAGhBxB,mBAAA,0BAA6B,EAC7BG,YAAA,CAMEC,MAAA;IALC,gBAAc,EAAEA,MAAA,CAAAqB,SAAS,CAACC,aAAa;IACvC,kBAAgB,EAAEtB,MAAA,CAAAqB,SAAS,CAACE,eAAe;IAC3C,oBAAkB,EAAEvB,MAAA,CAAAqB,SAAS,CAACG,iBAAiB;IAC/CC,OAAO,EAAEzB,MAAA,CAAAqB,SAAS,CAACI,OAAO;IAC1BC,cAAa,EAAE1B,MAAA,CAAA2B;sHAGlB/B,mBAAA,mBAAsB,EACtBG,YAAA,CAKEC,MAAA;IAJC4B,YAAW,EAAE5B,MAAA,CAAA6B,gBAAgB;IAC7BH,cAAa,EAAE1B,MAAA,CAAA2B,kBAAkB;IACjCG,aAAY,EAAE9B,MAAA,CAAA+B,iBAAiB;IAC/BC,gBAAe,EAAEhC,MAAA,CAAAiC;gEAGpBrC,mBAAA,uBAA0B,EAC1BG,YAAA,CAOEC,MAAA;IANAkC,GAAG,EAAC,iBAAiB;IACpB,gBAAc,EAAElC,MAAA,CAAAmC,aAAa,CAACC,IAAI;IAClCX,OAAO,EAAEzB,MAAA,CAAAmC,aAAa,CAACV,OAAO;IAC9BY,KAAK,EAAErC,MAAA,CAAAmC,aAAa,CAACE,KAAK;IAC1BC,gBAAe,EAAEtC,MAAA,CAAAuC,kBAAkB;IACnCC,WAAU,EAAExC,MAAA,CAAAyC;kFAGf7C,mBAAA,yBAA4B,EAC5BG,YAAA,CAGEC,MAAA;IAFC0C,UAAS,EAAE1C,MAAA,CAAA2C,aAAa;IACxBX,gBAAe,EAAEhC,MAAA,CAAAiC;QAItBrC,mBAAA,YAAe,EAEPI,MAAA,CAAAK,OAAO,CAACuC,aAAa,I,cAD7BC,YAAA,CAGE7C,MAAA;;IADC8C,OAAK,EAAE9C,MAAA,CAAA+C;2CAIF/C,MAAA,CAAAK,OAAO,CAAC2C,gBAAgB,I,cADhCH,YAAA,CAGE7C,MAAA;;IADC8C,OAAK,EAAE9C,MAAA,CAAAiD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}