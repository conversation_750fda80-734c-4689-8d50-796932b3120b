{"ast": null, "code": "export default {\n  name: 'QuickActionsSection',\n  emits: ['start-new-request', 'view-requests', 'view-documents', 'get-help']\n};", "map": {"version": 3, "names": ["name", "emits"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\client\\sections\\QuickActionsSection.vue"], "sourcesContent": ["<template>\n  <section class=\"quick-actions-section\" aria-labelledby=\"quick-actions-title\">\n    <div class=\"container\">\n      <div class=\"section-header\">\n        <h2 id=\"quick-actions-title\" class=\"section-title\">Quick Actions</h2>\n        <p class=\"section-description\">Common tasks and frequently used services</p>\n      </div>\n\n      <div class=\"quick-actions-grid\">\n        <button \n          class=\"action-card primary\" \n          @click=\"$emit('start-new-request')\"\n          type=\"button\"\n          aria-describedby=\"new-request-desc\"\n        >\n          <div class=\"action-icon\">\n            <svg aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z\"/>\n            </svg>\n          </div>\n          <div class=\"action-content\">\n            <h3>New Document Request</h3>\n            <p id=\"new-request-desc\">Start a new request for official documents</p>\n          </div>\n          <div class=\"action-arrow\">\n            <svg aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z\"/>\n            </svg>\n          </div>\n        </button>\n\n        <button \n          class=\"action-card\" \n          @click=\"$emit('view-requests')\"\n          type=\"button\"\n          aria-describedby=\"view-requests-desc\"\n        >\n          <div class=\"action-icon\">\n            <svg aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M3,5H9V11H3V5M5,7V9H7V7H5M11,7H21V9H11V7M11,15H21V17H11V15M5,20L1.5,16.5L2.91,15.09L5,17.17L9.59,12.59L11,14L5,20Z\"/>\n            </svg>\n          </div>\n          <div class=\"action-content\">\n            <h3>My Requests</h3>\n            <p id=\"view-requests-desc\">Track and manage your document requests</p>\n          </div>\n          <div class=\"action-arrow\">\n            <svg aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z\"/>\n            </svg>\n          </div>\n        </button>\n\n        <button \n          class=\"action-card\" \n          @click=\"$emit('view-documents')\"\n          type=\"button\"\n          aria-describedby=\"view-documents-desc\"\n        >\n          <div class=\"action-icon\">\n            <svg aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M13,9H18.5L13,3.5V9M6,2H14L20,8V20A2,2 0 0,1 18,22H6C4.89,22 4,21.1 4,20V4C4,2.89 4.89,2 6,2M15,18V16H6V18H15M18,14V12H6V14H18Z\"/>\n            </svg>\n          </div>\n          <div class=\"action-content\">\n            <h3>My Documents</h3>\n            <p id=\"view-documents-desc\">Access your completed documents</p>\n          </div>\n          <div class=\"action-arrow\">\n            <svg aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z\"/>\n            </svg>\n          </div>\n        </button>\n\n        <button \n          class=\"action-card\" \n          @click=\"$emit('get-help')\"\n          type=\"button\"\n          aria-describedby=\"get-help-desc\"\n        >\n          <div class=\"action-icon\">\n            <svg aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M11,18H13V16H11V18M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,20C7.59,20 4,16.41 4,12C7.59,8 4,8 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,6A4,4 0 0,0 8,10H10A2,2 0 0,1 12,8A2,2 0 0,1 14,10C14,12 11,11.75 11,15H13C13,12.75 16,12.5 16,10A4,4 0 0,0 12,6Z\"/>\n            </svg>\n          </div>\n          <div class=\"action-content\">\n            <h3>Help & Support</h3>\n            <p id=\"get-help-desc\">Get assistance with your requests</p>\n          </div>\n          <div class=\"action-arrow\">\n            <svg aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z\"/>\n            </svg>\n          </div>\n        </button>\n      </div>\n    </div>\n  </section>\n</template>\n\n<script>\nexport default {\n  name: 'QuickActionsSection',\n  emits: [\n    'start-new-request',\n    'view-requests',\n    'view-documents',\n    'get-help'\n  ]\n};\n</script>\n\n<style scoped>\n/* Quick Actions Section */\n.quick-actions-section {\n  padding: var(--spacing-10) 0;\n  background: var(--color-bg-secondary);\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 var(--spacing-4);\n}\n\n.section-header {\n  text-align: center;\n  margin-bottom: var(--spacing-10);\n}\n\n.section-title {\n  font-size: var(--font-size-2xl);\n  font-weight: 700;\n  color: var(--color-text-primary);\n  margin: 0 0 var(--spacing-3);\n  line-height: var(--line-height-3);\n}\n\n.section-description {\n  font-size: var(--font-size-lg);\n  color: var(--color-text-secondary);\n  margin: 0;\n  line-height: var(--line-height-5);\n}\n\n.quick-actions-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: var(--spacing-6);\n}\n\n.action-card {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-4);\n  padding: var(--spacing-6);\n  background: var(--color-bg-primary);\n  border: 2px solid var(--color-border-light);\n  border-radius: var(--radius-xl);\n  text-decoration: none;\n  color: var(--color-text-primary);\n  transition: all var(--duration-base) var(--easing-standard);\n  cursor: pointer;\n  min-height: 80px;\n  width: 100%;\n  text-align: left;\n}\n\n.action-card:hover {\n  border-color: var(--color-primary);\n  transform: translateY(-4px);\n  box-shadow: var(--shadow-4);\n}\n\n.action-card:focus {\n  outline: var(--focus-ring-width) solid var(--color-accent);\n  outline-offset: var(--focus-ring-offset);\n  border-color: var(--color-primary);\n}\n\n.action-card.primary {\n  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);\n  color: var(--color-text-inverse);\n  border-color: var(--color-primary);\n}\n\n.action-card.primary:hover {\n  transform: translateY(-4px) scale(1.02);\n  box-shadow: var(--shadow-5);\n}\n\n.action-icon {\n  flex-shrink: 0;\n  width: 48px;\n  height: 48px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: var(--radius-lg);\n  background: rgba(255, 255, 255, 0.1);\n}\n\n.action-card:not(.primary) .action-icon {\n  background: var(--color-primary-lighter);\n  color: var(--color-primary);\n}\n\n.action-icon svg {\n  width: 24px;\n  height: 24px;\n}\n\n.action-content {\n  flex: 1;\n  min-width: 0;\n}\n\n.action-content h3 {\n  font-size: var(--font-size-lg);\n  font-weight: 600;\n  margin: 0 0 var(--spacing-1);\n  line-height: var(--line-height-3);\n}\n\n.action-content p {\n  font-size: var(--font-size-sm);\n  margin: 0;\n  opacity: 0.8;\n  line-height: var(--line-height-4);\n}\n\n.action-arrow {\n  flex-shrink: 0;\n  width: 24px;\n  height: 24px;\n  opacity: 0.6;\n  transition: all var(--duration-base) var(--easing-standard);\n}\n\n.action-card:hover .action-arrow {\n  opacity: 1;\n  transform: translateX(4px);\n}\n\n.action-arrow svg {\n  width: 100%;\n  height: 100%;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .quick-actions-grid {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-4);\n  }\n  \n  .action-card {\n    padding: var(--spacing-5);\n  }\n  \n  .action-icon {\n    width: 40px;\n    height: 40px;\n  }\n  \n  .action-icon svg {\n    width: 20px;\n    height: 20px;\n  }\n}\n\n@media (max-width: 480px) {\n  .action-card {\n    flex-direction: column;\n    text-align: center;\n    padding: var(--spacing-6);\n  }\n  \n  .action-arrow {\n    transform: rotate(90deg);\n  }\n  \n  .action-card:hover .action-arrow {\n    transform: rotate(90deg) translateX(4px);\n  }\n}\n</style>\n"], "mappings": "AAsGA,eAAe;EACbA,IAAI,EAAE,qBAAqB;EAC3BC,KAAK,EAAE,CACL,mBAAmB,EACnB,eAAe,EACf,gBAAgB,EAChB,UAAS;AAEb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}