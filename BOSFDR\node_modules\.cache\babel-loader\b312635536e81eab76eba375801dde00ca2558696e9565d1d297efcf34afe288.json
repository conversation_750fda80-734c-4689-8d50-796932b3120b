{"ast": null, "code": "import ClientNotifications from './ClientNotifications.vue';\nexport default {\n  name: 'ClientHeader',\n  components: {\n    ClientNotifications\n  },\n  props: {\n    userName: {\n      type: String,\n      default: 'User'\n    },\n    userEmail: {\n      type: String,\n      default: '<EMAIL>'\n    },\n    userAvatar: {\n      type: String,\n      default: null\n    },\n    showUserDropdown: {\n      type: Boolean,\n      default: false\n    },\n    sidebarCollapsed: {\n      type: Boolean,\n      default: false\n    },\n    activeMenu: {\n      type: String,\n      default: 'dashboard'\n    },\n    showBreadcrumbs: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data() {\n    return {\n      showSearch: false,\n      searchQuery: ''\n    };\n  },\n  emits: ['sidebar-toggle', 'user-dropdown-toggle', 'menu-action', 'logout', 'error'],\n  mounted() {\n    // Setup event listeners for outside clicks\n    document.addEventListener('click', this.handleOutsideClick);\n  },\n  beforeUnmount() {\n    // Clean up event listeners\n    document.removeEventListener('click', this.handleOutsideClick);\n  },\n  methods: {\n    // Get page title based on active menu\n    getPageTitle() {\n      const titles = {\n        'dashboard': 'Dashboard',\n        'services': 'Document Services',\n        'requests': 'My Requests',\n        'documents': 'My Documents',\n        'profile': 'My Profile',\n        'settings': 'Account Settings',\n        'history': 'Request History',\n        'notifications': 'Notifications',\n        'help': 'Help & Support'\n      };\n      return titles[this.activeMenu] || 'Dashboard';\n    },\n    // Toggle search functionality\n    toggleSearch() {\n      this.showSearch = !this.showSearch;\n      if (this.showSearch) {\n        this.$nextTick(() => {\n          const searchInput = this.$el.querySelector('.search-input');\n          if (searchInput) {\n            searchInput.focus();\n          }\n        });\n      }\n    },\n    // Perform search\n    performSearch() {\n      if (this.searchQuery.trim()) {\n        this.$emit('search', this.searchQuery.trim());\n        // Close search on mobile after search\n        if (window.innerWidth <= 768) {\n          this.showSearch = false;\n        }\n      }\n    },\n    // Handle sidebar toggle\n    handleSidebarToggle() {\n      this.$emit('sidebar-toggle');\n    },\n    // Handle user dropdown toggle\n    handleUserDropdownToggle() {\n      this.$emit('user-dropdown-toggle');\n    },\n    // Handle menu actions (profile, settings, etc.)\n    handleMenuAction(action) {\n      this.$emit('menu-action', action);\n    },\n    // Handle logout\n    handleLogout() {\n      this.$emit('logout');\n    },\n    // Handle outside clicks to close dropdowns\n    handleOutsideClick(event) {\n      // Check if click is outside user dropdown\n      if (!event.target.closest('.user-dropdown')) {\n        if (this.showUserDropdown) {\n          this.$emit('user-dropdown-toggle');\n        }\n      }\n    },\n    // Notification event handlers\n    handleNewNotification(notification) {\n      console.log('New notification received:', notification);\n      // Handle new notification - could show toast, update UI, etc.\n    },\n    async handleNotificationClick(notification) {\n      console.log('🔔 ClientHeader: Notification clicked:', notification);\n\n      // Ensure we have a valid notification object\n      if (!notification || typeof notification !== 'object') {\n        console.error('Invalid notification object received:', notification);\n        return;\n      }\n      try {\n        // The ClientNotifications component now handles navigation internally,\n        // but we can add additional logic here if needed\n\n        // Parse notification data\n        const notificationData = typeof notification.data === 'string' ? JSON.parse(notification.data) : notification.data || {};\n\n        // Log navigation for debugging\n        console.log('📊 ClientHeader: Notification data:', notificationData);\n\n        // Additional header-specific logic can go here\n        // For example, updating header state, showing badges, etc.\n\n        // The navigation is now handled by the ClientNotifications component\n        // This handler can focus on header-specific updates\n      } catch (error) {\n        console.error('❌ ClientHeader: Error handling notification click:', error);\n      }\n\n      // Always emit the event for other components that might need it\n      this.$emit('notification-click', notification);\n    },\n    handleNotificationError(error) {\n      console.error('Notification error:', error);\n      this.$emit('error', error);\n    }\n  }\n};", "map": {"version": 3, "names": ["ClientNotifications", "name", "components", "props", "userName", "type", "String", "default", "userEmail", "userAvatar", "showUserDropdown", "Boolean", "sidebarCollapsed", "activeMenu", "showBreadcrumbs", "data", "showSearch", "searchQuery", "emits", "mounted", "document", "addEventListener", "handleOutsideClick", "beforeUnmount", "removeEventListener", "methods", "getPageTitle", "titles", "toggleSearch", "$nextTick", "searchInput", "$el", "querySelector", "focus", "performSearch", "trim", "$emit", "window", "innerWidth", "handleSidebarToggle", "handleUserDropdownToggle", "handleMenuAction", "action", "handleLogout", "event", "target", "closest", "handleNewNotification", "notification", "console", "log", "handleNotificationClick", "error", "notificationData", "JSON", "parse", "handleNotificationError"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\client\\ClientHeader.vue"], "sourcesContent": ["<template>\n  <header class=\"client-header\" role=\"banner\">\n    <!-- Government Banner -->\n    <div class=\"gov-banner\">\n      <div class=\"gov-banner-content\">\n        <div class=\"gov-banner-flag\">\n          <img src=\"/assets/images/us_flag_small.png\" alt=\"U.S. flag\" class=\"flag-icon\" />\n        </div>\n        <div class=\"gov-banner-text\">\n          <span class=\"gov-banner-label\">An official website of the</span>\n          <strong class=\"gov-banner-agency\">Barangay Bula General Santos City</strong>\n        </div>\n        <div class=\"gov-banner-secure\">\n          <i class=\"fas fa-lock\"></i>\n          <span>Secure</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Main Header -->\n    <div class=\"main-header\">\n      <div class=\"header-container\">\n        <!-- Left Section: Logo and Navigation -->\n        <div class=\"header-left\">\n          <div class=\"logo-section\">\n            <img src=\"/assets/images/barangay-logo.png\" alt=\"Barangay Bula Logo\" class=\"logo\" />\n            <div class=\"site-identity\">\n              <h1 class=\"site-title\">Barangay Bula</h1>\n              <span class=\"site-subtitle\">Document Request System</span>\n            </div>\n          </div>\n\n          <!-- Mobile Menu Toggle -->\n          <button class=\"mobile-menu-toggle\" @click=\"handleSidebarToggle\" aria-label=\"Toggle navigation menu\">\n            <span class=\"hamburger-line\"></span>\n            <span class=\"hamburger-line\"></span>\n            <span class=\"hamburger-line\"></span>\n          </button>\n        </div>\n\n        <!-- Center Section: Navigation (Desktop) -->\n        <nav class=\"main-navigation\" role=\"navigation\" aria-label=\"Main navigation\">\n          <ul class=\"nav-list\">\n            <li class=\"nav-item\">\n              <a href=\"#\" class=\"nav-link\" :class=\"{ active: activeMenu === 'dashboard' }\" @click=\"handleMenuAction('dashboard')\">\n                <i class=\"fas fa-home\"></i>\n                <span>Dashboard</span>\n              </a>\n            </li>\n            <li class=\"nav-item\">\n              <a href=\"#\" class=\"nav-link\" :class=\"{ active: activeMenu === 'services' }\" @click=\"handleMenuAction('services')\">\n                <i class=\"fas fa-file-alt\"></i>\n                <span>Services</span>\n              </a>\n            </li>\n            <li class=\"nav-item\">\n              <a href=\"#\" class=\"nav-link\" :class=\"{ active: activeMenu === 'requests' }\" @click=\"handleMenuAction('requests')\">\n                <i class=\"fas fa-clock\"></i>\n                <span>My Requests</span>\n              </a>\n            </li>\n            <li class=\"nav-item\">\n              <a href=\"#\" class=\"nav-link\" :class=\"{ active: activeMenu === 'help' }\" @click=\"handleMenuAction('help')\">\n                <i class=\"fas fa-question-circle\"></i>\n                <span>Help</span>\n              </a>\n            </li>\n          </ul>\n        </nav>\n\n        <!-- Right Section: User Actions -->\n        <div class=\"header-actions\">\n          <!-- Search -->\n          <div class=\"search-container\">\n            <button class=\"search-toggle\" @click=\"toggleSearch\" aria-label=\"Search\">\n              <i class=\"fas fa-search\"></i>\n            </button>\n            <div class=\"search-box\" :class=\"{ active: showSearch }\">\n              <input\n                type=\"search\"\n                placeholder=\"Search documents, services...\"\n                class=\"search-input\"\n                v-model=\"searchQuery\"\n                @keyup.enter=\"performSearch\"\n              />\n              <button class=\"search-submit\" @click=\"performSearch\" aria-label=\"Submit search\">\n                <i class=\"fas fa-search\"></i>\n              </button>\n            </div>\n          </div>\n\n          <!-- Notifications -->\n          <ClientNotifications\n            @new-notification=\"handleNewNotification\"\n            @notification-click=\"handleNotificationClick\"\n            @error=\"handleNotificationError\"\n          />\n\n          <!-- User Profile -->\n          <div class=\"user-profile\" :class=\"{ active: showUserDropdown }\">\n            <button class=\"user-btn\" @click=\"handleUserDropdownToggle\" aria-label=\"User menu\">\n              <div class=\"user-avatar\">\n                <img v-if=\"userAvatar\" :src=\"userAvatar\" :alt=\"userName\" class=\"avatar-image\" />\n                <i v-else class=\"fas fa-user-circle avatar-icon\"></i>\n              </div>\n              <div class=\"user-info\">\n                <span class=\"user-name\">{{ userName }}</span>\n                <span class=\"user-role\">Client Portal</span>\n              </div>\n              <i class=\"fas fa-chevron-down dropdown-arrow\"></i>\n            </button>\n\n            <div v-if=\"showUserDropdown\" class=\"user-dropdown-menu\" role=\"menu\">\n              <div class=\"dropdown-header\">\n                <div class=\"user-details\">\n                  <strong>{{ userName }}</strong>\n                  <span class=\"user-email\">{{ userEmail }}</span>\n                </div>\n              </div>\n              <div class=\"dropdown-divider\"></div>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('profile')\" role=\"menuitem\">\n                <i class=\"fas fa-user\"></i>\n                <span>My Profile</span>\n              </a>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('settings')\" role=\"menuitem\">\n                <i class=\"fas fa-cog\"></i>\n                <span>Account Settings</span>\n              </a>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('documents')\" role=\"menuitem\">\n                <i class=\"fas fa-folder\"></i>\n                <span>My Documents</span>\n              </a>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('history')\" role=\"menuitem\">\n                <i class=\"fas fa-history\"></i>\n                <span>Request History</span>\n              </a>\n              <div class=\"dropdown-divider\"></div>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('help')\" role=\"menuitem\">\n                <i class=\"fas fa-question-circle\"></i>\n                <span>Help & Support</span>\n              </a>\n              <div class=\"dropdown-divider\"></div>\n              <a href=\"#\" class=\"dropdown-item logout-item\" @click=\"handleLogout\" role=\"menuitem\">\n                <i class=\"fas fa-sign-out-alt\"></i>\n                <span>Sign Out</span>\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Breadcrumb Navigation -->\n    <div class=\"breadcrumb-section\" v-if=\"showBreadcrumbs\">\n      <div class=\"header-container\">\n        <nav class=\"breadcrumb-nav\" aria-label=\"Breadcrumb\">\n          <ol class=\"breadcrumb-list\">\n            <li class=\"breadcrumb-item\">\n              <a href=\"#\" @click=\"handleMenuAction('dashboard')\">\n                <i class=\"fas fa-home\"></i>\n                Dashboard\n              </a>\n            </li>\n            <li class=\"breadcrumb-item active\" aria-current=\"page\">\n              {{ getPageTitle() }}\n            </li>\n          </ol>\n        </nav>\n      </div>\n    </div>\n  </header>\n</template>\n\n<script>\nimport ClientNotifications from './ClientNotifications.vue';\n\nexport default {\n  name: 'ClientHeader',\n  components: {\n    ClientNotifications\n  },\n  props: {\n    userName: {\n      type: String,\n      default: 'User'\n    },\n    userEmail: {\n      type: String,\n      default: '<EMAIL>'\n    },\n    userAvatar: {\n      type: String,\n      default: null\n    },\n    showUserDropdown: {\n      type: Boolean,\n      default: false\n    },\n    sidebarCollapsed: {\n      type: Boolean,\n      default: false\n    },\n    activeMenu: {\n      type: String,\n      default: 'dashboard'\n    },\n    showBreadcrumbs: {\n      type: Boolean,\n      default: true\n    }\n  },\n\n  data() {\n    return {\n      showSearch: false,\n      searchQuery: ''\n    };\n  },\n\n  emits: [\n    'sidebar-toggle',\n    'user-dropdown-toggle',\n    'menu-action',\n    'logout',\n    'error'\n  ],\n\n  mounted() {\n    // Setup event listeners for outside clicks\n    document.addEventListener('click', this.handleOutsideClick);\n  },\n\n  beforeUnmount() {\n    // Clean up event listeners\n    document.removeEventListener('click', this.handleOutsideClick);\n  },\n\n  methods: {\n    // Get page title based on active menu\n    getPageTitle() {\n      const titles = {\n        'dashboard': 'Dashboard',\n        'services': 'Document Services',\n        'requests': 'My Requests',\n        'documents': 'My Documents',\n        'profile': 'My Profile',\n        'settings': 'Account Settings',\n        'history': 'Request History',\n        'notifications': 'Notifications',\n        'help': 'Help & Support'\n      };\n      return titles[this.activeMenu] || 'Dashboard';\n    },\n\n    // Toggle search functionality\n    toggleSearch() {\n      this.showSearch = !this.showSearch;\n      if (this.showSearch) {\n        this.$nextTick(() => {\n          const searchInput = this.$el.querySelector('.search-input');\n          if (searchInput) {\n            searchInput.focus();\n          }\n        });\n      }\n    },\n\n    // Perform search\n    performSearch() {\n      if (this.searchQuery.trim()) {\n        this.$emit('search', this.searchQuery.trim());\n        // Close search on mobile after search\n        if (window.innerWidth <= 768) {\n          this.showSearch = false;\n        }\n      }\n    },\n\n    // Handle sidebar toggle\n    handleSidebarToggle() {\n      this.$emit('sidebar-toggle');\n    },\n\n    // Handle user dropdown toggle\n    handleUserDropdownToggle() {\n      this.$emit('user-dropdown-toggle');\n    },\n\n    // Handle menu actions (profile, settings, etc.)\n    handleMenuAction(action) {\n      this.$emit('menu-action', action);\n    },\n\n    // Handle logout\n    handleLogout() {\n      this.$emit('logout');\n    },\n\n    // Handle outside clicks to close dropdowns\n    handleOutsideClick(event) {\n      // Check if click is outside user dropdown\n      if (!event.target.closest('.user-dropdown')) {\n        if (this.showUserDropdown) {\n          this.$emit('user-dropdown-toggle');\n        }\n      }\n    },\n\n    // Notification event handlers\n    handleNewNotification(notification) {\n      console.log('New notification received:', notification);\n      // Handle new notification - could show toast, update UI, etc.\n    },\n\n    async handleNotificationClick(notification) {\n      console.log('🔔 ClientHeader: Notification clicked:', notification);\n\n      // Ensure we have a valid notification object\n      if (!notification || typeof notification !== 'object') {\n        console.error('Invalid notification object received:', notification);\n        return;\n      }\n\n      try {\n        // The ClientNotifications component now handles navigation internally,\n        // but we can add additional logic here if needed\n\n        // Parse notification data\n        const notificationData = typeof notification.data === 'string'\n          ? JSON.parse(notification.data)\n          : notification.data || {};\n\n        // Log navigation for debugging\n        console.log('📊 ClientHeader: Notification data:', notificationData);\n\n        // Additional header-specific logic can go here\n        // For example, updating header state, showing badges, etc.\n\n        // The navigation is now handled by the ClientNotifications component\n        // This handler can focus on header-specific updates\n\n      } catch (error) {\n        console.error('❌ ClientHeader: Error handling notification click:', error);\n      }\n\n      // Always emit the event for other components that might need it\n      this.$emit('notification-click', notification);\n    },\n\n    handleNotificationError(error) {\n      console.error('Notification error:', error);\n      this.$emit('error', error);\n    }\n  }\n};\n</script>\n\n<style scoped src=\"./css/clientHeader.css\"></style>\n"], "mappings": "AA8KA,OAAOA,mBAAkB,MAAO,2BAA2B;AAE3D,eAAe;EACbC,IAAI,EAAE,cAAc;EACpBC,UAAU,EAAE;IACVF;EACF,CAAC;EACDG,KAAK,EAAE;IACLC,QAAQ,EAAE;MACRC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDC,SAAS,EAAE;MACTH,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDE,UAAU,EAAE;MACVJ,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDG,gBAAgB,EAAE;MAChBL,IAAI,EAAEM,OAAO;MACbJ,OAAO,EAAE;IACX,CAAC;IACDK,gBAAgB,EAAE;MAChBP,IAAI,EAAEM,OAAO;MACbJ,OAAO,EAAE;IACX,CAAC;IACDM,UAAU,EAAE;MACVR,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDO,eAAe,EAAE;MACfT,IAAI,EAAEM,OAAO;MACbJ,OAAO,EAAE;IACX;EACF,CAAC;EAEDQ,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,UAAU,EAAE,KAAK;MACjBC,WAAW,EAAE;IACf,CAAC;EACH,CAAC;EAEDC,KAAK,EAAE,CACL,gBAAgB,EAChB,sBAAsB,EACtB,aAAa,EACb,QAAQ,EACR,OAAM,CACP;EAEDC,OAAOA,CAAA,EAAG;IACR;IACAC,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACC,kBAAkB,CAAC;EAC7D,CAAC;EAEDC,aAAaA,CAAA,EAAG;IACd;IACAH,QAAQ,CAACI,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACF,kBAAkB,CAAC;EAChE,CAAC;EAEDG,OAAO,EAAE;IACP;IACAC,YAAYA,CAAA,EAAG;MACb,MAAMC,MAAK,GAAI;QACb,WAAW,EAAE,WAAW;QACxB,UAAU,EAAE,mBAAmB;QAC/B,UAAU,EAAE,aAAa;QACzB,WAAW,EAAE,cAAc;QAC3B,SAAS,EAAE,YAAY;QACvB,UAAU,EAAE,kBAAkB;QAC9B,SAAS,EAAE,iBAAiB;QAC5B,eAAe,EAAE,eAAe;QAChC,MAAM,EAAE;MACV,CAAC;MACD,OAAOA,MAAM,CAAC,IAAI,CAACd,UAAU,KAAK,WAAW;IAC/C,CAAC;IAED;IACAe,YAAYA,CAAA,EAAG;MACb,IAAI,CAACZ,UAAS,GAAI,CAAC,IAAI,CAACA,UAAU;MAClC,IAAI,IAAI,CAACA,UAAU,EAAE;QACnB,IAAI,CAACa,SAAS,CAAC,MAAM;UACnB,MAAMC,WAAU,GAAI,IAAI,CAACC,GAAG,CAACC,aAAa,CAAC,eAAe,CAAC;UAC3D,IAAIF,WAAW,EAAE;YACfA,WAAW,CAACG,KAAK,CAAC,CAAC;UACrB;QACF,CAAC,CAAC;MACJ;IACF,CAAC;IAED;IACAC,aAAaA,CAAA,EAAG;MACd,IAAI,IAAI,CAACjB,WAAW,CAACkB,IAAI,CAAC,CAAC,EAAE;QAC3B,IAAI,CAACC,KAAK,CAAC,QAAQ,EAAE,IAAI,CAACnB,WAAW,CAACkB,IAAI,CAAC,CAAC,CAAC;QAC7C;QACA,IAAIE,MAAM,CAACC,UAAS,IAAK,GAAG,EAAE;UAC5B,IAAI,CAACtB,UAAS,GAAI,KAAK;QACzB;MACF;IACF,CAAC;IAED;IACAuB,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAACH,KAAK,CAAC,gBAAgB,CAAC;IAC9B,CAAC;IAED;IACAI,wBAAwBA,CAAA,EAAG;MACzB,IAAI,CAACJ,KAAK,CAAC,sBAAsB,CAAC;IACpC,CAAC;IAED;IACAK,gBAAgBA,CAACC,MAAM,EAAE;MACvB,IAAI,CAACN,KAAK,CAAC,aAAa,EAAEM,MAAM,CAAC;IACnC,CAAC;IAED;IACAC,YAAYA,CAAA,EAAG;MACb,IAAI,CAACP,KAAK,CAAC,QAAQ,CAAC;IACtB,CAAC;IAED;IACAd,kBAAkBA,CAACsB,KAAK,EAAE;MACxB;MACA,IAAI,CAACA,KAAK,CAACC,MAAM,CAACC,OAAO,CAAC,gBAAgB,CAAC,EAAE;QAC3C,IAAI,IAAI,CAACpC,gBAAgB,EAAE;UACzB,IAAI,CAAC0B,KAAK,CAAC,sBAAsB,CAAC;QACpC;MACF;IACF,CAAC;IAED;IACAW,qBAAqBA,CAACC,YAAY,EAAE;MAClCC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEF,YAAY,CAAC;MACvD;IACF,CAAC;IAED,MAAMG,uBAAuBA,CAACH,YAAY,EAAE;MAC1CC,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEF,YAAY,CAAC;;MAEnE;MACA,IAAI,CAACA,YAAW,IAAK,OAAOA,YAAW,KAAM,QAAQ,EAAE;QACrDC,OAAO,CAACG,KAAK,CAAC,uCAAuC,EAAEJ,YAAY,CAAC;QACpE;MACF;MAEA,IAAI;QACF;QACA;;QAEA;QACA,MAAMK,gBAAe,GAAI,OAAOL,YAAY,CAACjC,IAAG,KAAM,QAAO,GACzDuC,IAAI,CAACC,KAAK,CAACP,YAAY,CAACjC,IAAI,IAC5BiC,YAAY,CAACjC,IAAG,IAAK,CAAC,CAAC;;QAE3B;QACAkC,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEG,gBAAgB,CAAC;;QAEpE;QACA;;QAEA;QACA;MAEF,EAAE,OAAOD,KAAK,EAAE;QACdH,OAAO,CAACG,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;MAC5E;;MAEA;MACA,IAAI,CAAChB,KAAK,CAAC,oBAAoB,EAAEY,YAAY,CAAC;IAChD,CAAC;IAEDQ,uBAAuBA,CAACJ,KAAK,EAAE;MAC7BH,OAAO,CAACG,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,IAAI,CAAChB,KAAK,CAAC,OAAO,EAAEgB,KAAK,CAAC;IAC5B;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}