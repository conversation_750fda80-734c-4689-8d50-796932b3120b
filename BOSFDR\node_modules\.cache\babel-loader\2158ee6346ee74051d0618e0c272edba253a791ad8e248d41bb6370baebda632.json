{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nexport default {\n  name: 'WelcomePage',\n  data() {\n    return {\n      loading: false\n    };\n  },\n  methods: {\n    async goToLogin() {\n      this.loading = true;\n      try {\n        // Add a small delay for better UX\n        await new Promise(resolve => setTimeout(resolve, 500));\n        this.$router.push({\n          name: 'UnifiedLogin'\n        });\n      } catch (error) {\n        console.error('Navigation error:', error);\n        // Fallback navigation\n        window.location.href = '/login';\n      } finally {\n        this.loading = false;\n      }\n    },\n    goToRegister() {\n      this.$router.push({\n        name: 'ClientRegistration'\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "loading", "methods", "goToLogin", "Promise", "resolve", "setTimeout", "$router", "push", "error", "console", "window", "location", "href", "goToRegister"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\WelcomePage.vue"], "sourcesContent": ["<template>\n  <div class=\"welcome-page\">\n    <!-- Government Banner -->\n    <div class=\"gov-banner\">\n      <div class=\"banner-content\">\n        <div class=\"banner-flag\">\n          <i class=\"fas fa-flag\"></i>\n        </div>\n        <div class=\"banner-text\">\n          <span class=\"banner-title\">An official website of Barangay Bula</span>\n          <span class=\"banner-subtitle\">General Santos City, Philippines</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Hero Section -->\n    <section class=\"hero-section\">\n      <div class=\"hero-background\">\n        <div class=\"hero-overlay\"></div>\n      </div>\n      <div class=\"hero-content\">\n        <div class=\"hero-header\">\n          <img\n            src=\"@/assets/icon-of-bula.jpg\"\n            alt=\"Barangay Bula Official Seal\"\n            class=\"hero-logo\"\n          />\n          <div class=\"hero-text\">\n            <h1 class=\"hero-title\">Barangay Bula</h1>\n            <p class=\"hero-subtitle\">Digital Document Request Portal</p>\n          </div>\n        </div>\n\n        <div class=\"hero-main\">\n          <h2 class=\"hero-headline\">Request Official Documents Online</h2>\n          <p class=\"hero-description\">\n            Get barangay certificates, clearances, and permits quickly and securely.\n            Our digital platform serves over 34,000 residents with efficient government services.\n          </p>\n\n          <div class=\"hero-actions\">\n            <button\n              class=\"btn btn-primary\"\n              @click=\"goToLogin\"\n              :disabled=\"loading\"\n            >\n              <i class=\"fas fa-sign-in-alt\"></i>\n              <span v-if=\"!loading\">Sign In</span>\n              <span v-else>\n                <i class=\"fas fa-spinner fa-spin\"></i>\n                Loading...\n              </span>\n            </button>\n            <button\n              class=\"btn btn-outline\"\n              @click=\"goToRegister\"\n            >\n              <i class=\"fas fa-user-plus\"></i>\n              Create Account\n            </button>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- Services Section -->\n    <section class=\"services-section\">\n      <div class=\"container\">\n        <div class=\"section-header\">\n          <h3 class=\"section-title\">Available Services</h3>\n          <p class=\"section-description\">Request these documents online through our secure portal</p>\n        </div>\n\n        <div class=\"services-grid\">\n          <div class=\"service-card\">\n            <div class=\"service-icon\">\n              <i class=\"fas fa-certificate\"></i>\n            </div>\n            <h4 class=\"service-title\">Barangay Certificate</h4>\n            <p class=\"service-description\">Official certification of residency and good standing</p>\n          </div>\n\n          <div class=\"service-card\">\n            <div class=\"service-icon\">\n              <i class=\"fas fa-id-card\"></i>\n            </div>\n            <h4 class=\"service-title\">Barangay Clearance</h4>\n            <p class=\"service-description\">Required for employment and business applications</p>\n          </div>\n\n          <div class=\"service-card\">\n            <div class=\"service-icon\">\n              <i class=\"fas fa-file-alt\"></i>\n            </div>\n            <h4 class=\"service-title\">Indigency Certificate</h4>\n            <p class=\"service-description\">For financial assistance and scholarship applications</p>\n          </div>\n\n          <div class=\"service-card\">\n            <div class=\"service-icon\">\n              <i class=\"fas fa-users\"></i>\n            </div>\n            <h4 class=\"service-title\">Community Tax Certificate</h4>\n            <p class=\"service-description\">Annual tax certificate for residents</p>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- Benefits Section -->\n    <section class=\"benefits-section\">\n      <div class=\"container\">\n        <div class=\"section-header\">\n          <h3 class=\"section-title\">Why Use Our Digital Platform?</h3>\n          <p class=\"section-description\">Experience modern, efficient government services</p>\n        </div>\n\n        <div class=\"benefits-grid\">\n          <div class=\"benefit-item\">\n            <div class=\"benefit-icon\">\n              <i class=\"fas fa-clock\"></i>\n            </div>\n            <div class=\"benefit-content\">\n              <h4>Save Time</h4>\n              <p>Submit requests online without visiting our office</p>\n            </div>\n          </div>\n\n          <div class=\"benefit-item\">\n            <div class=\"benefit-icon\">\n              <i class=\"fas fa-shield-alt\"></i>\n            </div>\n            <div class=\"benefit-content\">\n              <h4>Secure & Safe</h4>\n              <p>Your data is protected with government-grade security</p>\n            </div>\n          </div>\n\n          <div class=\"benefit-item\">\n            <div class=\"benefit-icon\">\n              <i class=\"fas fa-mobile-alt\"></i>\n            </div>\n            <div class=\"benefit-content\">\n              <h4>24/7 Access</h4>\n              <p>Request documents anytime from any device</p>\n            </div>\n          </div>\n\n          <div class=\"benefit-item\">\n            <div class=\"benefit-icon\">\n              <i class=\"fas fa-search\"></i>\n            </div>\n            <div class=\"benefit-content\">\n              <h4>Track Status</h4>\n              <p>Monitor your request progress in real-time</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- About Section -->\n    <section class=\"about-section\">\n      <div class=\"container\">\n        <div class=\"about-content\">\n          <div class=\"about-text\">\n            <h3>About Barangay Bula</h3>\n            <p>\n              Established in 1959, Barangay Bula is a thriving coastal community in General Santos City,\n              serving over 34,000 residents. We are committed to providing transparent, efficient,\n              and accessible government services to all our constituents.\n            </p>\n            <p>\n              This digital platform represents our commitment to modernizing public service delivery,\n              making it easier for residents to access essential government documents and services.\n            </p>\n          </div>\n          <div class=\"about-stats\">\n            <div class=\"stat-item\">\n              <div class=\"stat-number\">34,000+</div>\n              <div class=\"stat-label\">Residents Served</div>\n            </div>\n            <div class=\"stat-item\">\n              <div class=\"stat-number\">1959</div>\n              <div class=\"stat-label\">Year Established</div>\n            </div>\n            <div class=\"stat-item\">\n              <div class=\"stat-number\">24/7</div>\n              <div class=\"stat-label\">Online Access</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- Footer -->\n    <footer class=\"footer-section\">\n      <div class=\"container\">\n        <div class=\"footer-content\">\n          <div class=\"footer-info\">\n            <div class=\"footer-logo\">\n              <img\n                src=\"@/assets/icon-of-bula.jpg\"\n                alt=\"Barangay Bula Official Seal\"\n                class=\"footer-logo-img\"\n              />\n              <div class=\"footer-text\">\n                <h4>Barangay Bula</h4>\n                <p>General Santos City</p>\n              </div>\n            </div>\n\n            <div class=\"footer-contact\">\n              <h5>Contact Information</h5>\n              <div class=\"contact-item\">\n                <i class=\"fas fa-map-marker-alt\"></i>\n                <span>Barangay Bula, General Santos City</span>\n              </div>\n              <div class=\"contact-item\">\n                <i class=\"fas fa-phone\"></i>\n                <span>(083) 552-XXXX</span>\n              </div>\n              <div class=\"contact-item\">\n                <i class=\"fas fa-envelope\"></i>\n                <span><EMAIL></span>\n              </div>\n            </div>\n\n            <div class=\"footer-hours\">\n              <h5>Office Hours</h5>\n              <p>Monday - Friday<br>8:00 AM - 5:00 PM</p>\n            </div>\n          </div>\n\n          <div class=\"footer-bottom\">\n            <p>&copy; 2024 Barangay Bula, General Santos City. All rights reserved.</p>\n            <p>Committed to transparent and efficient public service.</p>\n          </div>\n        </div>\n      </div>\n    </footer>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'WelcomePage',\n  data() {\n    return {\n      loading: false\n    };\n  },\n  methods: {\n    async goToLogin() {\n      this.loading = true;\n      try {\n        // Add a small delay for better UX\n        await new Promise(resolve => setTimeout(resolve, 500));\n        this.$router.push({ name: 'UnifiedLogin' });\n      } catch (error) {\n        console.error('Navigation error:', error);\n        // Fallback navigation\n        window.location.href = '/login';\n      } finally {\n        this.loading = false;\n      }\n    },\n    \n    goToRegister() {\n      this.$router.push({ name: 'ClientRegistration' });\n    }\n  }\n};\n</script>\n\n<style scoped>\n/* Reset and base styles */\n* {\n  box-sizing: border-box;\n}\n\n.welcome-page {\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n  line-height: 1.6;\n  color: #333;\n  background-color: #f8f9fa;\n}\n\n/* Container utility */\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 1rem;\n}\n\n/* Government Banner */\n.gov-banner {\n  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);\n  color: white;\n  padding: 0.75rem 0;\n  border-bottom: 3px solid #fbbf24;\n}\n\n.banner-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 1rem;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.banner-flag {\n  font-size: 1.25rem;\n  color: #fbbf24;\n}\n\n.banner-text {\n  display: flex;\n  flex-direction: column;\n  gap: 0.125rem;\n}\n\n.banner-title {\n  font-weight: 600;\n  font-size: 0.875rem;\n}\n\n.banner-subtitle {\n  font-size: 0.75rem;\n  opacity: 0.9;\n}\n\n/* Hero Section */\n.hero-section {\n  position: relative;\n  min-height: 70vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n  color: white;\n}\n\n.hero-background {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-image: url('@/assets/bula-welcome-page-background-pic.JPG');\n  background-size: cover;\n  background-position: center;\n  background-repeat: no-repeat;\n  z-index: -2;\n}\n\n.hero-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    135deg,\n    rgba(30, 58, 138, 0.85) 0%,\n    rgba(30, 64, 175, 0.9) 100%\n  );\n  z-index: -1;\n}\n\n.hero-content {\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 2rem 1rem;\n  z-index: 1;\n}\n\n.hero-header {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 1.5rem;\n  margin-bottom: 2rem;\n}\n\n.hero-logo {\n  width: 80px;\n  height: 80px;\n  border-radius: 50%;\n  border: 3px solid white;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\n}\n\n.hero-text {\n  text-align: left;\n}\n\n.hero-title {\n  font-size: clamp(1.75rem, 4vw, 2.5rem);\n  font-weight: 700;\n  margin: 0;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n}\n\n.hero-subtitle {\n  font-size: clamp(1rem, 2.5vw, 1.25rem);\n  margin: 0.25rem 0 0 0;\n  opacity: 0.95;\n  font-weight: 400;\n}\n\n.hero-main {\n  margin-top: 2rem;\n}\n\n.hero-headline {\n  font-size: clamp(2rem, 5vw, 3rem);\n  font-weight: 700;\n  margin-bottom: 1rem;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n}\n\n.hero-description {\n  font-size: clamp(1.1rem, 2.5vw, 1.25rem);\n  margin-bottom: 2rem;\n  opacity: 0.95;\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n.hero-actions {\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n  flex-wrap: wrap;\n}\n\n/* Button Styles */\n.btn {\n  display: inline-flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.875rem 1.75rem;\n  border-radius: 0.5rem;\n  font-weight: 600;\n  font-size: 1rem;\n  text-decoration: none;\n  border: 2px solid transparent;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  min-width: 160px;\n  justify-content: center;\n}\n\n.btn-primary {\n  background: #fbbf24;\n  color: #1e3a8a;\n  border-color: #fbbf24;\n}\n\n.btn-primary:hover:not(:disabled) {\n  background: #f59e0b;\n  border-color: #f59e0b;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(251, 191, 36, 0.4);\n}\n\n.btn-outline {\n  background: transparent;\n  color: white;\n  border-color: white;\n}\n\n.btn-outline:hover {\n  background: white;\n  color: #1e3a8a;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);\n}\n\n.btn:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n  transform: none;\n}\n\n/* Section Styles */\n.services-section,\n.benefits-section,\n.about-section {\n  padding: 4rem 0;\n}\n\n.services-section {\n  background: white;\n}\n\n.benefits-section {\n  background: #f8f9fa;\n}\n\n.about-section {\n  background: white;\n}\n\n.section-header {\n  text-align: center;\n  margin-bottom: 3rem;\n}\n\n.section-title {\n  font-size: clamp(2rem, 4vw, 2.5rem);\n  font-weight: 700;\n  color: #1e3a8a;\n  margin-bottom: 1rem;\n}\n\n.section-description {\n  font-size: 1.125rem;\n  color: #6b7280;\n  max-width: 600px;\n  margin: 0 auto;\n}\n\n/* Services Grid */\n.services-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: 2rem;\n}\n\n.service-card {\n  background: white;\n  border-radius: 1rem;\n  padding: 2rem;\n  text-align: center;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  border: 1px solid #e5e7eb;\n  transition: all 0.3s ease;\n}\n\n.service-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\n  border-color: #fbbf24;\n}\n\n.service-icon {\n  width: 4rem;\n  height: 4rem;\n  background: linear-gradient(135deg, #1e3a8a, #1e40af);\n  border-radius: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 0 auto 1.5rem;\n  color: white;\n  font-size: 1.5rem;\n}\n\n.service-title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1e3a8a;\n  margin-bottom: 0.75rem;\n}\n\n.service-description {\n  color: #6b7280;\n  line-height: 1.6;\n}\n\n/* Benefits Grid */\n.benefits-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 2rem;\n}\n\n.benefit-item {\n  display: flex;\n  align-items: flex-start;\n  gap: 1rem;\n  padding: 1.5rem;\n  background: white;\n  border-radius: 0.75rem;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n  border: 1px solid #e5e7eb;\n  transition: all 0.3s ease;\n}\n\n.benefit-item:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n}\n\n.benefit-icon {\n  width: 3rem;\n  height: 3rem;\n  background: linear-gradient(135deg, #fbbf24, #f59e0b);\n  border-radius: 0.75rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #1e3a8a;\n  font-size: 1.25rem;\n  flex-shrink: 0;\n}\n\n.benefit-content h4 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #1e3a8a;\n  margin-bottom: 0.5rem;\n}\n\n.benefit-content p {\n  color: #6b7280;\n  line-height: 1.5;\n  margin: 0;\n}\n\n/* Services Preview */\n.services-preview {\n  margin-bottom: 3rem;\n  text-align: center;\n}\n\n.services-title {\n  font-size: clamp(1.75rem, 4vw, 2.5rem);\n  font-weight: 600;\n  color: white;\n  margin-bottom: 2rem;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n}\n\n.services-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1.5rem;\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.service-item {\n  background: rgba(255, 255, 255, 0.9);\n  backdrop-filter: blur(10px);\n  border-radius: 0.75rem;\n  padding: 1.5rem;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 1rem;\n  transition: transform 0.3s ease, background 0.3s ease;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.service-item:hover {\n  transform: translateY(-3px);\n  background: rgba(255, 255, 255, 0.95);\n}\n\n.service-item i {\n  font-size: 2rem;\n  color: #007bbf;\n}\n\n.service-item span {\n  font-weight: 500;\n  color: #1a365d;\n  text-align: center;\n}\n\n/* Call to Action Section */\n.cta-section {\n  margin-bottom: 3rem;\n  text-align: center;\n}\n\n.cta-content {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: 1.5rem;\n  padding: 3rem 2rem;\n  max-width: 600px;\n  margin: 0 auto;\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.cta-title {\n  font-size: clamp(1.5rem, 3vw, 2rem);\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 1rem;\n}\n\n.cta-description {\n  color: #4a5568;\n  font-size: 1.1rem;\n  line-height: 1.6;\n  margin-bottom: 2rem;\n}\n\n.login-button {\n  background: linear-gradient(135deg, #007bbf, #005a86);\n  color: white;\n  border: none;\n  padding: 1rem 2.5rem;\n  border-radius: 0.75rem;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: inline-flex;\n  align-items: center;\n  gap: 0.75rem;\n  box-shadow: 0 4px 15px rgba(0, 123, 191, 0.3);\n  min-width: 200px;\n  justify-content: center;\n}\n\n.login-button:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(0, 123, 191, 0.4);\n  background: linear-gradient(135deg, #0056b3, #004a73);\n}\n\n.login-button:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n  transform: none;\n}\n\n.register-link {\n  margin-top: 1.5rem;\n}\n\n.register-link p {\n  color: #4a5568;\n  font-size: 1rem;\n}\n\n.register-btn {\n  color: #007bbf;\n  text-decoration: none;\n  font-weight: 500;\n  transition: color 0.3s ease;\n}\n\n.register-btn:hover {\n  color: #005a86;\n  text-decoration: underline;\n}\n\n/* Footer */\n.welcome-footer {\n  margin-top: auto;\n  background: rgba(26, 54, 93, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: 1rem 1rem 0 0;\n  padding: 2rem;\n  color: white;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.footer-content {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 2rem;\n  margin-bottom: 2rem;\n}\n\n.contact-info h4,\n.office-hours h4 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  margin-bottom: 1rem;\n  color: rgba(255, 255, 255, 0.95);\n}\n\n.contact-item {\n  display: flex;\n  align-items: flex-start;\n  gap: 0.75rem;\n  margin-bottom: 0.75rem;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.contact-item i {\n  color: #4fc3f7;\n  margin-top: 0.125rem;\n  flex-shrink: 0;\n}\n\n.office-hours p {\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 0.5rem;\n  line-height: 1.5;\n}\n\n.footer-bottom {\n  border-top: 1px solid rgba(255, 255, 255, 0.2);\n  padding-top: 1.5rem;\n  text-align: center;\n}\n\n.footer-bottom p {\n  color: rgba(255, 255, 255, 0.8);\n  margin-bottom: 0.5rem;\n}\n\n.powered-by {\n  font-size: 0.9rem;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .content-container {\n    padding: 1rem 0.75rem;\n  }\n\n  .welcome-header {\n    margin-bottom: 2rem;\n    padding: 1rem 0;\n  }\n\n  .logo {\n    width: 100px;\n    height: 100px;\n  }\n\n  .info-grid {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n\n  .info-card {\n    padding: 1.5rem;\n  }\n\n  .services-grid {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 1rem;\n  }\n\n  .service-item {\n    padding: 1rem;\n  }\n\n  .cta-content {\n    padding: 2rem 1.5rem;\n    margin: 0 1rem;\n  }\n\n  .login-button {\n    padding: 0.875rem 2rem;\n    font-size: 1rem;\n    min-width: 180px;\n  }\n\n  .welcome-footer {\n    padding: 1.5rem;\n    border-radius: 0;\n  }\n\n  .footer-content {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n    text-align: center;\n  }\n}\n\n@media (max-width: 480px) {\n  .content-container {\n    padding: 0.75rem 0.5rem;\n  }\n\n  .info-card {\n    padding: 1.25rem;\n  }\n\n  .services-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .cta-content {\n    margin: 0 0.5rem;\n    padding: 1.5rem 1rem;\n  }\n\n  .login-button {\n    width: 100%;\n    max-width: 280px;\n  }\n}\n\n/* Animation for smooth entrance */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.welcome-header,\n.info-section,\n.services-preview,\n.cta-section,\n.welcome-footer {\n  animation: fadeInUp 0.8s ease-out;\n}\n\n.info-section {\n  animation-delay: 0.2s;\n}\n\n.services-preview {\n  animation-delay: 0.4s;\n}\n\n.cta-section {\n  animation-delay: 0.6s;\n}\n\n.welcome-footer {\n  animation-delay: 0.8s;\n}\n\n/* Accessibility improvements */\n@media (prefers-reduced-motion: reduce) {\n  .welcome-header,\n  .info-section,\n  .services-preview,\n  .cta-section,\n  .welcome-footer {\n    animation: none;\n  }\n\n  .logo:hover,\n  .info-card:hover,\n  .service-item:hover,\n  .login-button:hover:not(:disabled) {\n    transform: none;\n  }\n}\n\n/* High contrast mode support */\n@media (prefers-contrast: high) {\n  .background-overlay {\n    background: rgba(0, 0, 0, 0.8);\n  }\n\n  .info-card,\n  .service-item,\n  .cta-content {\n    background: rgba(255, 255, 255, 1);\n    border: 2px solid #000;\n  }\n\n  .welcome-footer {\n    background: rgba(0, 0, 0, 0.95);\n    border: 2px solid #fff;\n  }\n}\n</style>\n"], "mappings": ";AAqPA,eAAe;EACbA,IAAI,EAAE,aAAa;EACnBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE;IACX,CAAC;EACH,CAAC;EACDC,OAAO,EAAE;IACP,MAAMC,SAASA,CAAA,EAAG;MAChB,IAAI,CAACF,OAAM,GAAI,IAAI;MACnB,IAAI;QACF;QACA,MAAM,IAAIG,OAAO,CAACC,OAAM,IAAKC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;QACtD,IAAI,CAACE,OAAO,CAACC,IAAI,CAAC;UAAET,IAAI,EAAE;QAAe,CAAC,CAAC;MAC7C,EAAE,OAAOU,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;QACzC;QACAE,MAAM,CAACC,QAAQ,CAACC,IAAG,GAAI,QAAQ;MACjC,UAAU;QACR,IAAI,CAACZ,OAAM,GAAI,KAAK;MACtB;IACF,CAAC;IAEDa,YAAYA,CAAA,EAAG;MACb,IAAI,CAACP,OAAO,CAACC,IAAI,CAAC;QAAET,IAAI,EAAE;MAAqB,CAAC,CAAC;IACnD;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}