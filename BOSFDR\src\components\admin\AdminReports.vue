<template>
  <div class="admin-reports">
    <AdminHeader
      :userName="adminData?.first_name || 'Admin'"
      :notificationCount="unreadNotifications"
      :showNotifications="showNotifications"
      :showUserDropdown="showUserDropdown"
      :sidebarCollapsed="sidebarCollapsed"
      :activeMenu="activeMenu"
      @sidebar-toggle="handleSidebarToggle"
      @notification-toggle="handleNotificationToggle"
      @user-dropdown-toggle="handleUserDropdownToggle"
      @menu-action="handleMenuAction"
      @logout="handleLogout"
      @view-all-notifications="handleViewAllNotifications"
    />

    <!-- Mobile Overlay -->
    <div
      class="mobile-overlay"
      :class="{ active: !sidebarCollapsed && isMobile }"
      @click="closeMobileSidebar"
    ></div>

    <div class="dashboard-container">
      <AdminSidebar
        :collapsed="sidebarCollapsed"
        :activeMenu="activeMenu"
        @menu-change="handleMenuChange"
        @logout="handleLogout"
        @toggle-sidebar="handleSidebarToggle"
      />

      <main class="main-content" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
        <div class="container-fluid py-4">
          <!-- Analytics Dashboard -->
          <div class="row mb-4">
            <div class="col-12">
              <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                  <h6 class="m-0 font-weight-bold text-primary">Analytics Dashboard</h6>
                  <div class="d-flex align-items-center">
                    <label class="me-2">Period:</label>
                    <select
                      v-model="selectedPeriod"
                      @change="onPeriodChange"
                      class="form-select form-select-sm"
                      style="width: auto;"
                    >
                      <option value="day">Last 30 Days</option>
                      <option value="week">Last 12 Weeks</option>
                      <option value="month">Last 12 Months</option>
                    </select>
                  </div>
                </div>
                <div class="card-body">
                  <div v-if="loading" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                  </div>

                  <div v-else-if="analyticsData" class="row">
                    <!-- Request Trends Chart -->
                    <div class="col-lg-8 mb-4">
                      <div class="card h-100">
                        <div class="card-header">
                          <h6 class="card-title mb-0">Request Trends</h6>
                        </div>
                        <div class="card-body">
                          <div style="height: 300px;">
                            <Bar
                              v-if="trendsChartData"
                              :data="trendsChartData"
                              :options="chartOptions"
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Document Types Distribution -->
                    <div class="col-lg-4 mb-4">
                      <div class="card h-100">
                        <div class="card-header">
                          <h6 class="card-title mb-0">Document Types</h6>
                        </div>
                        <div class="card-body">
                          <div style="height: 300px;">
                            <Doughnut
                              v-if="documentTypesChartData"
                              :data="documentTypesChartData"
                              :options="chartOptions"
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Status Distribution -->
                    <div class="col-lg-6 mb-4">
                      <div class="card h-100">
                        <div class="card-header">
                          <h6 class="card-title mb-0">Status Distribution</h6>
                        </div>
                        <div class="card-body">
                          <div style="height: 300px;">
                            <Doughnut
                              v-if="statusDistributionChartData"
                              :data="statusDistributionChartData"
                              :options="chartOptions"
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Revenue Trends -->
                    <div class="col-lg-6 mb-4">
                      <div class="card h-100">
                        <div class="card-header">
                          <h6 class="card-title mb-0">Revenue Trends</h6>
                        </div>
                        <div class="card-body">
                          <div style="height: 300px;">
                            <Line
                              v-if="revenueChartData"
                              :data="revenueChartData"
                              :options="chartOptions"
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Top Clients -->
                    <div class="col-12 mb-4">
                      <div class="card">
                        <div class="card-header">
                          <h6 class="card-title mb-0">Top Clients</h6>
                        </div>
                        <div class="card-body">
                          <div class="table-responsive">
                            <table class="table table-hover">
                              <thead>
                                <tr>
                                  <th>Client Name</th>
                                  <th>Total Requests</th>
                                  <th>Total Spending</th>
                                </tr>
                              </thead>
                              <tbody>
                                <tr v-for="client in analyticsData.topClients" :key="client.client_name">
                                  <td>{{ client.client_name }}</td>
                                  <td>{{ client.request_count }}</td>
                                  <td>₱{{ parseFloat(client.total_spending || 0).toFixed(2) }}</td>
                                </tr>
                              </tbody>
                            </table>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Report Generation -->
          <div class="row">
            <div class="col-12">
              <div class="card shadow">
                <div class="card-header py-3">
                  <h6 class="m-0 font-weight-bold text-primary">Report Generation</h6>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-6">
                      <div class="mb-3">
                        <label class="form-label">Report Type</label>
                        <select v-model="reportFilters.reportType" class="form-select">
                          <option value="daily">Daily Report</option>
                          <option value="weekly">Weekly Report</option>
                          <option value="monthly">Monthly Report</option>
                          <option value="custom">Custom Report</option>
                        </select>
                      </div>
                    </div>
                    <div class="col-md-3">
                      <div class="mb-3">
                        <label class="form-label">From Date</label>
                        <input
                          type="date"
                          v-model="reportFilters.date_from"
                          class="form-control"
                        >
                      </div>
                    </div>
                    <div class="col-md-3">
                      <div class="mb-3">
                        <label class="form-label">To Date</label>
                        <input
                          type="date"
                          v-model="reportFilters.date_to"
                          class="form-control"
                        >
                      </div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-6">
                      <div class="mb-3">
                        <label class="form-label">Output Format</label>
                        <select v-model="reportFilters.format" class="form-select">
                          <option value="json">View Online</option>
                          <option value="csv">Download CSV</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  <div class="d-flex gap-2">
                    <button
                      @click="generateReport"
                      :disabled="loading"
                      class="btn btn-primary"
                    >
                      <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
                      Generate Report
                    </button>
                    <button
                      @click="downloadReport"
                      :disabled="loading"
                      class="btn btn-success"
                    >
                      <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
                      Download CSV
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>



<script>
import AdminHeader from './AdminHeader.vue';
import AdminSidebar from './AdminSidebar.vue';
import adminDocumentService from '../../services/adminDocumentService';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement, LineElement, PointElement } from 'chart.js';
import { Bar, Doughnut, Line } from 'vue-chartjs';

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement, LineElement, PointElement);
import unifiedAuthService from '@/services/unifiedAuthService';

export default {
  name: 'AdminReports',
  components: {
    AdminHeader,
    AdminSidebar,
    Bar,
    Doughnut,
    Line
  },

  data() {
    return {
      // UI State
      sidebarCollapsed: false,
      showUserDropdown: false,
      isMobile: false,
      adminData: null,
      // Component Data
      loading: false,
      analyticsData: null,
      selectedPeriod: 'month',
      reportFilters: {
        reportType: 'daily',
        date_from: '',
        date_to: '',
        format: 'json'
      },
      chartOptions: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'top',
          },
          title: {
            display: true,
            text: 'Document Request Analytics'
          }
        }
      }
    };
  },

  computed: {
    activeMenu() {
      const path = this.$route.path;
      if (path.includes('/admin/users')) return 'users';
      if (path.includes('/admin/requests')) return 'requests';
      if (path.includes('/admin/reports')) return 'reports';
      if (path.includes('/admin/settings')) return 'settings';
      if (path.includes('/admin/activity-logs')) return 'activity';
      if (path.includes('/admin/profile')) return 'profile';
      return 'dashboard';
    },
    trendsChartData() {
      if (!this.analyticsData?.trends) return null;

      return {
        labels: this.analyticsData.trends.map(item => item.period),
        datasets: [
          {
            label: 'Total Requests',
            backgroundColor: '#007bff',
            data: this.analyticsData.trends.map(item => item.total_requests)
          },
          {
            label: 'Completed Requests',
            backgroundColor: '#28a745',
            data: this.analyticsData.trends.map(item => item.completed_requests)
          },
          {
            label: 'Rejected Requests',
            backgroundColor: '#dc3545',
            data: this.analyticsData.trends.map(item => item.rejected_requests)
          }
        ]
      };
    },
    documentTypesChartData() {
      if (!this.analyticsData?.documentTypes) return null;

      return {
        labels: this.analyticsData.documentTypes.map(item => item.type_name),
        datasets: [{
          data: this.analyticsData.documentTypes.map(item => item.request_count),
          backgroundColor: [
            '#007bff',
            '#28a745',
            '#ffc107',
            '#dc3545',
            '#6f42c1',
            '#fd7e14'
          ]
        }]
      };
    },
    statusDistributionChartData() {
      if (!this.analyticsData?.statusDistribution) return null;

      return {
        labels: this.analyticsData.statusDistribution.map(item => item.status_name),
        datasets: [{
          data: this.analyticsData.statusDistribution.map(item => item.percentage),
          backgroundColor: [
            '#ffc107', // Pending
            '#17a2b8', // Under Review
            '#007bff', // Processing
            '#28a745', // Completed
            '#dc3545', // Rejected
            '#6c757d', // Cancelled
            '#fd7e14', // Ready for Pickup
            '#20c997', // Additional Info Required
            '#e83e8c'  // Other
          ]
        }]
      };
    },
    revenueChartData() {
      if (!this.analyticsData?.trends) return null;

      return {
        labels: this.analyticsData.trends.map(item => item.period),
        datasets: [{
          label: 'Revenue (₱)',
          borderColor: '#28a745',
          backgroundColor: 'rgba(40, 167, 69, 0.1)',
          data: this.analyticsData.trends.map(item => parseFloat(item.total_revenue || 0))
        }]
      };
    }
  },

  async mounted() {
    // Check authentication
    if (!unifiedAuthService.isLoggedIn() || unifiedAuthService.getUserType() !== 'admin') {
      this.$router.push('/login');
      return;
    }

    // Initialize UI state
    this.initializeUI();

    // Load component data
    await this.loadAdminProfile();
    await this.loadAnalyticsData();
    this.setDefaultDateRange();
  },

  beforeUnmount() {
    if (this.handleResize) {
      window.removeEventListener('resize', this.handleResize);
    }
  },



  methods: {
    // Initialize UI state
    initializeUI() {
      this.isMobile = window.innerWidth <= 768;

      if (!this.isMobile) {
        const saved = localStorage.getItem('adminSidebarCollapsed');
        this.sidebarCollapsed = saved ? JSON.parse(saved) : false;
      } else {
        this.sidebarCollapsed = true;
      }

      this.handleResize = () => {
        const wasMobile = this.isMobile;
        this.isMobile = window.innerWidth <= 768;

        if (this.isMobile && !wasMobile) {
          this.sidebarCollapsed = true;
        } else if (!this.isMobile && wasMobile) {
          const saved = localStorage.getItem('adminSidebarCollapsed');
          this.sidebarCollapsed = saved ? JSON.parse(saved) : false;
        }
      };
      window.addEventListener('resize', this.handleResize);
    },

    handleSidebarToggle() {
      this.sidebarCollapsed = !this.sidebarCollapsed;
      localStorage.setItem('adminSidebarCollapsed', JSON.stringify(this.sidebarCollapsed));
    },

    handleMenuChange(menu) {
      const routes = {
        'dashboard': '/admin/dashboard',
        'users': '/admin/users',
        'requests': '/admin/requests',
        'reports': '/admin/reports',
        'settings': '/admin/settings',
        'activity': '/admin/activity-logs',
        'profile': '/admin/profile'
      };

      if (this.isMobile) {
        this.sidebarCollapsed = true;
      }

      if (routes[menu]) {
        this.$router.push(routes[menu]);
      }
    },

    handleUserDropdownToggle() {
      this.showUserDropdown = !this.showUserDropdown;
    },

    handleMenuAction(action) {
      if (action === 'profile') {
        this.$router.push('/admin/profile');
      } else if (action === 'settings') {
        this.$router.push('/admin/settings');
      }
      this.showUserDropdown = false;
    },

    closeMobileSidebar() {
      if (this.isMobile) {
        this.sidebarCollapsed = true;
      }
    },

    handleLogout() {
      unifiedAuthService.logout();
      this.$router.push('/login');
    },

    async loadAdminProfile() {
      try {
        const user = unifiedAuthService.getCurrentUser();
        if (user && user.profile) {
          this.adminData = user.profile;
        } else {
          this.adminData = {
            first_name: user?.username || 'Admin',
            role: user?.role || 'admin'
          };
        }
      } catch (error) {
        console.error('Failed to load admin profile:', error);
        const user = unifiedAuthService.getCurrentUser();
        this.adminData = {
          first_name: user?.username || 'Admin',
          role: user?.role || 'admin'
        };
      }
    },

    setDefaultDateRange() {
      const today = new Date();
      const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());

      this.reportFilters.date_to = today.toISOString().split('T')[0];
      this.reportFilters.date_from = lastMonth.toISOString().split('T')[0];
    },

    async loadAnalyticsData() {
      try {
        this.loading = true;
        const response = await adminDocumentService.getAnalyticsData(this.selectedPeriod);
        this.analyticsData = response.data;
      } catch (error) {
        console.error('Failed to load analytics data:', error);
        this.$toast.error('Failed to load analytics data');
      } finally {
        this.loading = false;
      }
    },

    async onPeriodChange() {
      await this.loadAnalyticsData();
    },

    async generateReport() {
      try {
        this.loading = true;
        const response = await adminDocumentService.generateReport(
          this.reportFilters.reportType,
          this.reportFilters
        );

        if (this.reportFilters.format === 'csv') {
          // CSV download is handled by the service
          this.$toast.success('Report downloaded successfully');
        } else {
          // Display JSON data (could open in modal or new tab)
          console.log('Report data:', response.data);
          this.$toast.success('Report generated successfully');
        }
      } catch (error) {
        console.error('Failed to generate report:', error);
        this.$toast.error('Failed to generate report');
      } finally {
        this.loading = false;
      }
    },

    async downloadReport() {
      try {
        this.loading = true;
        await adminDocumentService.downloadReport(
          this.reportFilters.reportType,
          this.reportFilters
        );
        this.$toast.success('Report downloaded successfully');
      } catch (error) {
        console.error('Failed to download report:', error);
        this.$toast.error('Failed to download report');
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>

<style scoped>
@import './css/adminDashboard.css';

/* Report card styles */
.report-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.report-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.3);
}

.report-card:active {
  transform: translateY(-1px);
}

/* Icon circle for reports */
.icon-circle {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

/* Table improvements */
.table-sm th,
.table-sm td {
  padding: 0.5rem;
  font-size: 0.875rem;
}

/* Form improvements */
.form-control-sm,
.form-select-sm {
  font-size: 0.875rem;
}

/* Badge improvements */
.badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .report-card .card-body {
    padding: 1rem;
  }

  .icon-circle {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1rem;
  }

  .btn-group {
    flex-direction: column;
  }

  .btn-group .btn {
    border-radius: 0.375rem !important;
    margin-bottom: 0.25rem;
  }
}
</style>
