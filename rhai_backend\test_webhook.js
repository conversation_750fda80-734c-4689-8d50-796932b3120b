const axios = require('axios');
const crypto = require('crypto');
require('dotenv').config();

async function testWebhook() {
  try {
    console.log('🧪 Testing PayMongo Webhook Endpoint');
    console.log('=====================================');
    
    const webhookUrl = 'http://localhost:7000/api/webhooks/paymongo';
    const secret = 'whsk_pTTtiARr2yocnmhHTcukNAwM'; // Use the new webhook secret
    
    console.log('Webhook URL:', webhookUrl);
    console.log('Secret configured:', !!secret);
    
    // Sample PayMongo webhook payload for link.payment.paid
    const testPayload = {
      "data": {
        "id": "evt_test_123456789",
        "type": "event",
        "attributes": {
          "type": "link.payment.paid",
          "livemode": false,
          "data": {
            "id": "link_test_123456789",
            "type": "link",
            "attributes": {
              "amount": 321060,
              "archived": false,
              "currency": "PHP",
              "description": "BOSFDR - Cedula Request #112",
              "livemode": false,
              "fee": 0,
              "remarks": null,
              "status": "paid",
              "tax_amount": null,
              "taxes": [],
              "checkout_url": "https://checkout.paymongo.com/test",
              "reference_number": "TEST123456",
              "created_at": Math.floor(Date.now() / 1000),
              "updated_at": Math.floor(Date.now() / 1000),
              "payments": [
                {
                  "id": "pay_test_123456789",
                  "type": "payment",
                  "attributes": {
                    "amount": 321060,
                    "currency": "PHP",
                    "description": "BOSFDR - Cedula Request #112",
                    "status": "paid",
                    "livemode": false,
                    "created_at": Math.floor(Date.now() / 1000)
                  }
                }
              ]
            }
          },
          "created_at": Math.floor(Date.now() / 1000),
          "livemode": false
        }
      }
    };
    
    const payloadString = JSON.stringify(testPayload);
    console.log('Test payload created, length:', payloadString.length);
    
    // Create signature if secret is available
    let headers = {
      'Content-Type': 'application/json'
    };
    
    if (secret) {
      const signature = crypto
        .createHmac('sha256', secret)
        .update(payloadString, 'utf8')
        .digest('hex');
      
      headers['paymongo-signature'] = `sha256=${signature}`;
      console.log('Signature created:', `sha256=${signature.substring(0, 10)}...`);
    }
    
    console.log('\n🚀 Sending test webhook...');

    // Send as raw JSON string (like PayMongo does)
    const response = await axios.post(webhookUrl, payloadString, { headers });
    
    console.log('✅ Webhook test successful!');
    console.log('Response status:', response.status);
    console.log('Response data:', response.data);
    
    // Check if payment was processed
    console.log('\n🔍 Checking payment status after webhook...');
    setTimeout(async () => {
      try {
        const { spawn } = require('child_process');
        const checkStatus = spawn('node', ['check_payment_status.js'], { 
          cwd: process.cwd(),
          stdio: 'inherit'
        });
      } catch (error) {
        console.log('Could not run status check automatically');
      }
    }, 2000);
    
  } catch (error) {
    console.error('❌ Webhook test failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
  }
}

testWebhook();
