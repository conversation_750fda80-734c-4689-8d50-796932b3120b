{"ast": null, "code": "import ClientNotifications from './ClientNotifications.vue';\nexport default {\n  name: 'Client<PERSON>eader',\n  components: {\n    ClientNotifications\n  },\n  props: {\n    userName: {\n      type: String,\n      default: 'User'\n    },\n    userEmail: {\n      type: String,\n      default: '<EMAIL>'\n    },\n    userAvatar: {\n      type: String,\n      default: null\n    },\n    showUserDropdown: {\n      type: Boolean,\n      default: false\n    },\n    sidebarCollapsed: {\n      type: Boolean,\n      default: false\n    },\n    activeMenu: {\n      type: String,\n      default: 'dashboard'\n    },\n    showBreadcrumbs: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data() {\n    return {\n      showSearch: false,\n      searchQuery: ''\n    };\n  },\n  emits: ['sidebar-toggle', 'user-dropdown-toggle', 'menu-action', 'logout', 'error', 'search', 'notification-click'],\n  mounted() {\n    // Setup event listeners for outside clicks\n    document.addEventListener('click', this.handleOutsideClick);\n  },\n  beforeUnmount() {\n    // Clean up event listeners\n    document.removeEventListener('click', this.handleOutsideClick);\n  },\n  methods: {\n    // Get page title based on active menu\n    getPageTitle() {\n      const titles = {\n        'dashboard': 'Dashboard',\n        'services': 'Document Services',\n        'requests': 'My Requests',\n        'documents': 'My Documents',\n        'profile': 'My Profile',\n        'settings': 'Account Settings',\n        'history': 'Request History',\n        'notifications': 'Notifications',\n        'help': 'Help & Support'\n      };\n      return titles[this.activeMenu] || 'Dashboard';\n    },\n    // Toggle search functionality\n    toggleSearch() {\n      this.showSearch = !this.showSearch;\n      if (this.showSearch) {\n        this.$nextTick(() => {\n          const searchInput = this.$el.querySelector('.search-input');\n          if (searchInput) {\n            searchInput.focus();\n          }\n        });\n      }\n    },\n    // Perform search\n    performSearch() {\n      if (this.searchQuery.trim()) {\n        this.$emit('search', this.searchQuery.trim());\n        // Close search on mobile after search\n        if (window.innerWidth <= 768) {\n          this.showSearch = false;\n        }\n      }\n    },\n    // Handle sidebar toggle\n    handleSidebarToggle() {\n      this.$emit('sidebar-toggle');\n    },\n    // Handle user dropdown toggle\n    handleUserDropdownToggle() {\n      this.$emit('user-dropdown-toggle');\n    },\n    // Handle menu actions (profile, settings, etc.)\n    handleMenuAction(action) {\n      this.$emit('menu-action', action);\n    },\n    // Handle logout\n    handleLogout() {\n      this.$emit('logout');\n    },\n    // Handle outside clicks to close dropdowns\n    handleOutsideClick(event) {\n      // Check if click is outside user dropdown\n      if (!event.target.closest('.user-profile')) {\n        if (this.showUserDropdown) {\n          this.$emit('user-dropdown-toggle');\n        }\n      }\n\n      // Check if click is outside search\n      if (!event.target.closest('.search-container')) {\n        this.showSearch = false;\n      }\n    },\n    // Notification event handlers\n    handleNewNotification(notification) {\n      console.log('New notification received:', notification);\n      // Handle new notification - could show toast, update UI, etc.\n    },\n    async handleNotificationClick(notification) {\n      console.log('🔔 ClientHeader: Notification clicked:', notification);\n\n      // Ensure we have a valid notification object\n      if (!notification || typeof notification !== 'object') {\n        console.error('Invalid notification object received:', notification);\n        return;\n      }\n      try {\n        // The ClientNotifications component now handles navigation internally,\n        // but we can add additional logic here if needed\n\n        // Parse notification data\n        const notificationData = typeof notification.data === 'string' ? JSON.parse(notification.data) : notification.data || {};\n\n        // Log navigation for debugging\n        console.log('📊 ClientHeader: Notification data:', notificationData);\n\n        // Additional header-specific logic can go here\n        // For example, updating header state, showing badges, etc.\n\n        // The navigation is now handled by the ClientNotifications component\n        // This handler can focus on header-specific updates\n      } catch (error) {\n        console.error('❌ ClientHeader: Error handling notification click:', error);\n      }\n\n      // Always emit the event for other components that might need it\n      this.$emit('notification-click', notification);\n    },\n    handleNotificationError(error) {\n      console.error('Notification error:', error);\n      this.$emit('error', error);\n    }\n  }\n};", "map": {"version": 3, "names": ["ClientNotifications", "name", "components", "props", "userName", "type", "String", "default", "userEmail", "userAvatar", "showUserDropdown", "Boolean", "sidebarCollapsed", "activeMenu", "showBreadcrumbs", "data", "showSearch", "searchQuery", "emits", "mounted", "document", "addEventListener", "handleOutsideClick", "beforeUnmount", "removeEventListener", "methods", "getPageTitle", "titles", "toggleSearch", "$nextTick", "searchInput", "$el", "querySelector", "focus", "performSearch", "trim", "$emit", "window", "innerWidth", "handleSidebarToggle", "handleUserDropdownToggle", "handleMenuAction", "action", "handleLogout", "event", "target", "closest", "handleNewNotification", "notification", "console", "log", "handleNotificationClick", "error", "notificationData", "JSON", "parse", "handleNotificationError"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\client\\ClientHeader.vue"], "sourcesContent": ["<template>\n  <!-- Skip Navigation Link for Accessibility -->\n  <a href=\"#main-content\" class=\"skip-link\">Skip to main content</a>\n\n  <header class=\"client-header\" role=\"banner\" aria-label=\"Site header\">\n    <!-- Government Banner - USWDS Compliant -->\n    <section\n      class=\"gov-banner\"\n      aria-label=\"Official website of Barangay Bula, General Santos City\"\n    >\n      <div class=\"usa-accordion\">\n        <header class=\"usa-banner__header\">\n          <div class=\"usa-banner__inner\">\n            <div class=\"grid-col-auto\">\n              <img\n                aria-hidden=\"true\"\n                class=\"usa-banner__header-flag\"\n                src=\"/assets/images/ph_flag_small.png\"\n                alt=\"\"\n              />\n            </div>\n            <div class=\"grid-col-fill tablet:grid-col-auto\" aria-hidden=\"true\">\n              <p class=\"usa-banner__header-text\">\n                An official website of Barangay Bula, General Santos City\n              </p>\n              <p class=\"usa-banner__header-action\">Here's how you know</p>\n            </div>\n            <button\n              type=\"button\"\n              class=\"usa-accordion__button usa-banner__button\"\n              :aria-expanded=\"showBannerDetails\"\n              aria-controls=\"gov-banner-content\"\n              @click=\"toggleBannerDetails\"\n            >\n              <span class=\"usa-banner__button-text\">Here's how you know</span>\n            </button>\n          </div>\n        </header>\n        <div\n          class=\"usa-banner__content usa-accordion__content\"\n          id=\"gov-banner-content\"\n          :hidden=\"!showBannerDetails\"\n        >\n          <div class=\"grid-row grid-gap-lg\">\n            <div class=\"usa-banner__guidance tablet:grid-col-6\">\n              <img\n                class=\"usa-banner__icon usa-media-block__img\"\n                src=\"/assets/images/icon-dot-gov.svg\"\n                role=\"img\"\n                alt=\"\"\n                aria-hidden=\"true\"\n              />\n              <div class=\"usa-media-block__body\">\n                <p>\n                  <strong>Official websites use .gov.ph</strong><br />\n                  A <strong>.gov.ph</strong> website belongs to an official government\n                  organization in the Philippines.\n                </p>\n              </div>\n            </div>\n            <div class=\"usa-banner__guidance tablet:grid-col-6\">\n              <img\n                class=\"usa-banner__icon usa-media-block__img\"\n                src=\"/assets/images/icon-https.svg\"\n                role=\"img\"\n                alt=\"\"\n                aria-hidden=\"true\"\n              />\n              <div class=\"usa-media-block__body\">\n                <p>\n                  <strong>Secure .gov.ph websites use HTTPS</strong><br />\n                  A <strong>lock</strong> (🔒) or <strong>https://</strong> means you've\n                  safely connected to the .gov.ph website. Share sensitive information\n                  only on official, secure websites.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- Main Header -->\n    <div class=\"main-header\">\n      <div class=\"header-container\">\n        <!-- Left Section: Logo and Navigation -->\n        <div class=\"header-left\">\n          <button\n            class=\"logo-section\"\n            @click=\"handleMenuAction('dashboard')\"\n            :aria-label=\"`Go to ${getPageTitle()} dashboard`\"\n            type=\"button\"\n          >\n            <img\n              src=\"@/assets/icon-of-bula.jpg\"\n              alt=\"Barangay Bula Logo\"\n              class=\"logo\"\n              width=\"40\"\n              height=\"40\"\n            />\n            <div class=\"site-identity\">\n              <h1 class=\"site-title\">Barangay Bula</h1>\n              <span class=\"site-subtitle\">Digital Services Portal</span>\n            </div>\n          </button>\n\n          <!-- Mobile Menu Toggle -->\n          <button\n            class=\"mobile-menu-toggle\"\n            @click=\"handleSidebarToggle\"\n            :aria-label=\"sidebarCollapsed ? 'Open navigation menu' : 'Close navigation menu'\"\n            :aria-expanded=\"!sidebarCollapsed\"\n            type=\"button\"\n          >\n            <span class=\"hamburger-line\" :class=\"{ active: !sidebarCollapsed }\"></span>\n            <span class=\"hamburger-line\" :class=\"{ active: !sidebarCollapsed }\"></span>\n            <span class=\"hamburger-line\" :class=\"{ active: !sidebarCollapsed }\"></span>\n          </button>\n        </div>\n\n        <!-- Center Section: Navigation (Desktop) -->\n        <nav class=\"main-navigation\" role=\"navigation\" aria-label=\"Main navigation\">\n          <ul class=\"nav-list\" role=\"menubar\">\n            <li class=\"nav-item\" role=\"none\">\n              <button\n                class=\"nav-link\"\n                :class=\"{ active: activeMenu === 'dashboard' }\"\n                @click=\"handleMenuAction('dashboard')\"\n                role=\"menuitem\"\n                :aria-current=\"activeMenu === 'dashboard' ? 'page' : undefined\"\n                type=\"button\"\n              >\n                <svg class=\"nav-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z\"/>\n                </svg>\n                <span>Dashboard</span>\n              </button>\n            </li>\n            <li class=\"nav-item\" role=\"none\">\n              <button\n                class=\"nav-link\"\n                :class=\"{ active: activeMenu === 'services' }\"\n                @click=\"handleMenuAction('services')\"\n                role=\"menuitem\"\n                :aria-current=\"activeMenu === 'services' ? 'page' : undefined\"\n                type=\"button\"\n              >\n                <svg class=\"nav-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"/>\n                </svg>\n                <span>Services</span>\n              </button>\n            </li>\n            <li class=\"nav-item\" role=\"none\">\n              <button\n                class=\"nav-link\"\n                :class=\"{ active: activeMenu === 'requests' }\"\n                @click=\"handleMenuAction('requests')\"\n                role=\"menuitem\"\n                :aria-current=\"activeMenu === 'requests' ? 'page' : undefined\"\n                type=\"button\"\n              >\n                <svg class=\"nav-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z\"/>\n                </svg>\n                <span>My Requests</span>\n              </button>\n            </li>\n            <li class=\"nav-item\" role=\"none\">\n              <button\n                class=\"nav-link\"\n                :class=\"{ active: activeMenu === 'help' }\"\n                @click=\"handleMenuAction('help')\"\n                role=\"menuitem\"\n                :aria-current=\"activeMenu === 'help' ? 'page' : undefined\"\n                type=\"button\"\n              >\n                <svg class=\"nav-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M11,18H13V16H11V18M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,20C7.59,20 4,16.41 4,12C7.59,8 4,8 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,6A4,4 0 0,0 8,10H10A2,2 0 0,1 12,8A2,2 0 0,1 14,10C14,12 11,11.75 11,15H13C13,12.75 16,12.5 16,10A4,4 0 0,0 12,6Z\"/>\n                </svg>\n                <span>Help</span>\n              </button>\n            </li>\n          </ul>\n        </nav>\n\n        <!-- Right Section: User Actions -->\n        <div class=\"header-actions\">\n          <!-- Search -->\n          <div class=\"search-container\">\n            <button class=\"search-toggle\" @click=\"toggleSearch\" aria-label=\"Search documents and services\" :aria-expanded=\"showSearch\">\n              <i class=\"fas fa-search\" aria-hidden=\"true\"></i>\n            </button>\n            <div class=\"search-box\" :class=\"{ active: showSearch }\" role=\"search\">\n              <label for=\"header-search\" class=\"sr-only\">Search documents and services</label>\n              <input\n                id=\"header-search\"\n                type=\"search\"\n                placeholder=\"Search documents, services...\"\n                class=\"search-input\"\n                v-model=\"searchQuery\"\n                @keyup.enter=\"performSearch\"\n                autocomplete=\"off\"\n              />\n              <button class=\"search-submit\" @click=\"performSearch\" aria-label=\"Submit search\">\n                <i class=\"fas fa-search\" aria-hidden=\"true\"></i>\n              </button>\n            </div>\n          </div>\n\n          <!-- Notifications -->\n          <ClientNotifications\n            @new-notification=\"handleNewNotification\"\n            @notification-click=\"handleNotificationClick\"\n            @error=\"handleNotificationError\"\n          />\n\n          <!-- User Profile -->\n          <div class=\"user-profile\" :class=\"{ active: showUserDropdown }\">\n            <button class=\"user-btn\" @click=\"handleUserDropdownToggle\" aria-label=\"User account menu\" :aria-expanded=\"showUserDropdown\">\n              <div class=\"user-avatar\">\n                <img v-if=\"userAvatar\" :src=\"userAvatar\" :alt=\"userName\" class=\"avatar-image\" />\n                <i v-else class=\"fas fa-user-circle avatar-icon\" aria-hidden=\"true\"></i>\n              </div>\n              <div class=\"user-info\">\n                <span class=\"user-name\">{{ userName }}</span>\n                <span class=\"user-role\">Client Portal</span>\n              </div>\n              <i class=\"fas fa-chevron-down dropdown-arrow\" aria-hidden=\"true\"></i>\n            </button>\n\n            <div v-if=\"showUserDropdown\" class=\"user-dropdown-menu\" role=\"menu\" aria-label=\"User account options\">\n              <div class=\"dropdown-header\">\n                <div class=\"user-details\">\n                  <strong>{{ userName }}</strong>\n                  <span class=\"user-email\">{{ userEmail }}</span>\n                </div>\n              </div>\n              <div class=\"dropdown-divider\"></div>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('profile')\" role=\"menuitem\">\n                <i class=\"fas fa-user\" aria-hidden=\"true\"></i>\n                <span>My Profile</span>\n              </a>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('settings')\" role=\"menuitem\">\n                <i class=\"fas fa-cog\" aria-hidden=\"true\"></i>\n                <span>Account Settings</span>\n              </a>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('documents')\" role=\"menuitem\">\n                <i class=\"fas fa-folder\" aria-hidden=\"true\"></i>\n                <span>My Documents</span>\n              </a>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('history')\" role=\"menuitem\">\n                <i class=\"fas fa-history\" aria-hidden=\"true\"></i>\n                <span>Request History</span>\n              </a>\n              <div class=\"dropdown-divider\"></div>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('help')\" role=\"menuitem\">\n                <i class=\"fas fa-question-circle\" aria-hidden=\"true\"></i>\n                <span>Help & Support</span>\n              </a>\n              <div class=\"dropdown-divider\"></div>\n              <a href=\"#\" class=\"dropdown-item logout-item\" @click=\"handleLogout\" role=\"menuitem\">\n                <i class=\"fas fa-sign-out-alt\" aria-hidden=\"true\"></i>\n                <span>Sign Out</span>\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Breadcrumb Navigation -->\n    <div class=\"breadcrumb-section\" v-if=\"showBreadcrumbs\">\n      <div class=\"header-container\">\n        <nav class=\"breadcrumb-nav\" aria-label=\"Breadcrumb navigation\">\n          <ol class=\"breadcrumb-list\">\n            <li class=\"breadcrumb-item\">\n              <a href=\"#\" @click=\"handleMenuAction('dashboard')\">\n                <i class=\"fas fa-home\" aria-hidden=\"true\"></i>\n                Dashboard\n              </a>\n            </li>\n            <li class=\"breadcrumb-item active\" aria-current=\"page\">\n              {{ getPageTitle() }}\n            </li>\n          </ol>\n        </nav>\n      </div>\n    </div>\n  </header>\n</template>\n\n<script>\nimport ClientNotifications from './ClientNotifications.vue';\n\nexport default {\n  name: 'ClientHeader',\n  components: {\n    ClientNotifications\n  },\n  props: {\n    userName: {\n      type: String,\n      default: 'User'\n    },\n    userEmail: {\n      type: String,\n      default: '<EMAIL>'\n    },\n    userAvatar: {\n      type: String,\n      default: null\n    },\n    showUserDropdown: {\n      type: Boolean,\n      default: false\n    },\n    sidebarCollapsed: {\n      type: Boolean,\n      default: false\n    },\n    activeMenu: {\n      type: String,\n      default: 'dashboard'\n    },\n    showBreadcrumbs: {\n      type: Boolean,\n      default: true\n    }\n  },\n\n  data() {\n    return {\n      showSearch: false,\n      searchQuery: ''\n    };\n  },\n\n  emits: [\n    'sidebar-toggle',\n    'user-dropdown-toggle',\n    'menu-action',\n    'logout',\n    'error',\n    'search',\n    'notification-click'\n  ],\n\n  mounted() {\n    // Setup event listeners for outside clicks\n    document.addEventListener('click', this.handleOutsideClick);\n  },\n\n  beforeUnmount() {\n    // Clean up event listeners\n    document.removeEventListener('click', this.handleOutsideClick);\n  },\n\n  methods: {\n    // Get page title based on active menu\n    getPageTitle() {\n      const titles = {\n        'dashboard': 'Dashboard',\n        'services': 'Document Services',\n        'requests': 'My Requests',\n        'documents': 'My Documents',\n        'profile': 'My Profile',\n        'settings': 'Account Settings',\n        'history': 'Request History',\n        'notifications': 'Notifications',\n        'help': 'Help & Support'\n      };\n      return titles[this.activeMenu] || 'Dashboard';\n    },\n\n    // Toggle search functionality\n    toggleSearch() {\n      this.showSearch = !this.showSearch;\n      if (this.showSearch) {\n        this.$nextTick(() => {\n          const searchInput = this.$el.querySelector('.search-input');\n          if (searchInput) {\n            searchInput.focus();\n          }\n        });\n      }\n    },\n\n    // Perform search\n    performSearch() {\n      if (this.searchQuery.trim()) {\n        this.$emit('search', this.searchQuery.trim());\n        // Close search on mobile after search\n        if (window.innerWidth <= 768) {\n          this.showSearch = false;\n        }\n      }\n    },\n\n    // Handle sidebar toggle\n    handleSidebarToggle() {\n      this.$emit('sidebar-toggle');\n    },\n\n    // Handle user dropdown toggle\n    handleUserDropdownToggle() {\n      this.$emit('user-dropdown-toggle');\n    },\n\n    // Handle menu actions (profile, settings, etc.)\n    handleMenuAction(action) {\n      this.$emit('menu-action', action);\n    },\n\n    // Handle logout\n    handleLogout() {\n      this.$emit('logout');\n    },\n\n    // Handle outside clicks to close dropdowns\n    handleOutsideClick(event) {\n      // Check if click is outside user dropdown\n      if (!event.target.closest('.user-profile')) {\n        if (this.showUserDropdown) {\n          this.$emit('user-dropdown-toggle');\n        }\n      }\n\n      // Check if click is outside search\n      if (!event.target.closest('.search-container')) {\n        this.showSearch = false;\n      }\n    },\n\n    // Notification event handlers\n    handleNewNotification(notification) {\n      console.log('New notification received:', notification);\n      // Handle new notification - could show toast, update UI, etc.\n    },\n\n    async handleNotificationClick(notification) {\n      console.log('🔔 ClientHeader: Notification clicked:', notification);\n\n      // Ensure we have a valid notification object\n      if (!notification || typeof notification !== 'object') {\n        console.error('Invalid notification object received:', notification);\n        return;\n      }\n\n      try {\n        // The ClientNotifications component now handles navigation internally,\n        // but we can add additional logic here if needed\n\n        // Parse notification data\n        const notificationData = typeof notification.data === 'string'\n          ? JSON.parse(notification.data)\n          : notification.data || {};\n\n        // Log navigation for debugging\n        console.log('📊 ClientHeader: Notification data:', notificationData);\n\n        // Additional header-specific logic can go here\n        // For example, updating header state, showing badges, etc.\n\n        // The navigation is now handled by the ClientNotifications component\n        // This handler can focus on header-specific updates\n\n      } catch (error) {\n        console.error('❌ ClientHeader: Error handling notification click:', error);\n      }\n\n      // Always emit the event for other components that might need it\n      this.$emit('notification-click', notification);\n    },\n\n    handleNotificationError(error) {\n      console.error('Notification error:', error);\n      this.$emit('error', error);\n    }\n  }\n};\n</script>\n\n<style scoped src=\"./css/clientHeader.css\"></style>\n"], "mappings": "AAqSA,OAAOA,mBAAkB,MAAO,2BAA2B;AAE3D,eAAe;EACbC,IAAI,EAAE,cAAc;EACpBC,UAAU,EAAE;IACVF;EACF,CAAC;EACDG,KAAK,EAAE;IACLC,QAAQ,EAAE;MACRC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDC,SAAS,EAAE;MACTH,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDE,UAAU,EAAE;MACVJ,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDG,gBAAgB,EAAE;MAChBL,IAAI,EAAEM,OAAO;MACbJ,OAAO,EAAE;IACX,CAAC;IACDK,gBAAgB,EAAE;MAChBP,IAAI,EAAEM,OAAO;MACbJ,OAAO,EAAE;IACX,CAAC;IACDM,UAAU,EAAE;MACVR,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDO,eAAe,EAAE;MACfT,IAAI,EAAEM,OAAO;MACbJ,OAAO,EAAE;IACX;EACF,CAAC;EAEDQ,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,UAAU,EAAE,KAAK;MACjBC,WAAW,EAAE;IACf,CAAC;EACH,CAAC;EAEDC,KAAK,EAAE,CACL,gBAAgB,EAChB,sBAAsB,EACtB,aAAa,EACb,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,oBAAmB,CACpB;EAEDC,OAAOA,CAAA,EAAG;IACR;IACAC,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACC,kBAAkB,CAAC;EAC7D,CAAC;EAEDC,aAAaA,CAAA,EAAG;IACd;IACAH,QAAQ,CAACI,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACF,kBAAkB,CAAC;EAChE,CAAC;EAEDG,OAAO,EAAE;IACP;IACAC,YAAYA,CAAA,EAAG;MACb,MAAMC,MAAK,GAAI;QACb,WAAW,EAAE,WAAW;QACxB,UAAU,EAAE,mBAAmB;QAC/B,UAAU,EAAE,aAAa;QACzB,WAAW,EAAE,cAAc;QAC3B,SAAS,EAAE,YAAY;QACvB,UAAU,EAAE,kBAAkB;QAC9B,SAAS,EAAE,iBAAiB;QAC5B,eAAe,EAAE,eAAe;QAChC,MAAM,EAAE;MACV,CAAC;MACD,OAAOA,MAAM,CAAC,IAAI,CAACd,UAAU,KAAK,WAAW;IAC/C,CAAC;IAED;IACAe,YAAYA,CAAA,EAAG;MACb,IAAI,CAACZ,UAAS,GAAI,CAAC,IAAI,CAACA,UAAU;MAClC,IAAI,IAAI,CAACA,UAAU,EAAE;QACnB,IAAI,CAACa,SAAS,CAAC,MAAM;UACnB,MAAMC,WAAU,GAAI,IAAI,CAACC,GAAG,CAACC,aAAa,CAAC,eAAe,CAAC;UAC3D,IAAIF,WAAW,EAAE;YACfA,WAAW,CAACG,KAAK,CAAC,CAAC;UACrB;QACF,CAAC,CAAC;MACJ;IACF,CAAC;IAED;IACAC,aAAaA,CAAA,EAAG;MACd,IAAI,IAAI,CAACjB,WAAW,CAACkB,IAAI,CAAC,CAAC,EAAE;QAC3B,IAAI,CAACC,KAAK,CAAC,QAAQ,EAAE,IAAI,CAACnB,WAAW,CAACkB,IAAI,CAAC,CAAC,CAAC;QAC7C;QACA,IAAIE,MAAM,CAACC,UAAS,IAAK,GAAG,EAAE;UAC5B,IAAI,CAACtB,UAAS,GAAI,KAAK;QACzB;MACF;IACF,CAAC;IAED;IACAuB,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAACH,KAAK,CAAC,gBAAgB,CAAC;IAC9B,CAAC;IAED;IACAI,wBAAwBA,CAAA,EAAG;MACzB,IAAI,CAACJ,KAAK,CAAC,sBAAsB,CAAC;IACpC,CAAC;IAED;IACAK,gBAAgBA,CAACC,MAAM,EAAE;MACvB,IAAI,CAACN,KAAK,CAAC,aAAa,EAAEM,MAAM,CAAC;IACnC,CAAC;IAED;IACAC,YAAYA,CAAA,EAAG;MACb,IAAI,CAACP,KAAK,CAAC,QAAQ,CAAC;IACtB,CAAC;IAED;IACAd,kBAAkBA,CAACsB,KAAK,EAAE;MACxB;MACA,IAAI,CAACA,KAAK,CAACC,MAAM,CAACC,OAAO,CAAC,eAAe,CAAC,EAAE;QAC1C,IAAI,IAAI,CAACpC,gBAAgB,EAAE;UACzB,IAAI,CAAC0B,KAAK,CAAC,sBAAsB,CAAC;QACpC;MACF;;MAEA;MACA,IAAI,CAACQ,KAAK,CAACC,MAAM,CAACC,OAAO,CAAC,mBAAmB,CAAC,EAAE;QAC9C,IAAI,CAAC9B,UAAS,GAAI,KAAK;MACzB;IACF,CAAC;IAED;IACA+B,qBAAqBA,CAACC,YAAY,EAAE;MAClCC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEF,YAAY,CAAC;MACvD;IACF,CAAC;IAED,MAAMG,uBAAuBA,CAACH,YAAY,EAAE;MAC1CC,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEF,YAAY,CAAC;;MAEnE;MACA,IAAI,CAACA,YAAW,IAAK,OAAOA,YAAW,KAAM,QAAQ,EAAE;QACrDC,OAAO,CAACG,KAAK,CAAC,uCAAuC,EAAEJ,YAAY,CAAC;QACpE;MACF;MAEA,IAAI;QACF;QACA;;QAEA;QACA,MAAMK,gBAAe,GAAI,OAAOL,YAAY,CAACjC,IAAG,KAAM,QAAO,GACzDuC,IAAI,CAACC,KAAK,CAACP,YAAY,CAACjC,IAAI,IAC5BiC,YAAY,CAACjC,IAAG,IAAK,CAAC,CAAC;;QAE3B;QACAkC,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEG,gBAAgB,CAAC;;QAEpE;QACA;;QAEA;QACA;MAEF,EAAE,OAAOD,KAAK,EAAE;QACdH,OAAO,CAACG,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;MAC5E;;MAEA;MACA,IAAI,CAAChB,KAAK,CAAC,oBAAoB,EAAEY,YAAY,CAAC;IAChD,CAAC;IAEDQ,uBAAuBA,CAACJ,KAAK,EAAE;MAC7BH,OAAO,CAACG,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,IAAI,CAAChB,KAAK,CAAC,OAAO,EAAEgB,KAAK,CAAC;IAC5B;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}