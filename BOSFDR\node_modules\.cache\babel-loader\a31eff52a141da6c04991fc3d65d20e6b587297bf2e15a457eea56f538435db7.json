{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode, createStaticVNode as _createStaticVNode } from \"vue\";\nimport _imports_0 from '@/assets/icon-of-bula.jpg';\nconst _hoisted_1 = {\n  class: \"welcome-page\"\n};\nconst _hoisted_2 = {\n  class: \"hero-section\"\n};\nconst _hoisted_3 = {\n  class: \"hero-content\"\n};\nconst _hoisted_4 = {\n  class: \"hero-main\"\n};\nconst _hoisted_5 = {\n  class: \"hero-actions\"\n};\nconst _hoisted_6 = [\"disabled\"];\nconst _hoisted_7 = {\n  key: 0\n};\nconst _hoisted_8 = {\n  key: 1\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" Government Banner \"), _cache[9] || (_cache[9] = _createStaticVNode(\"<div class=\\\"gov-banner\\\" data-v-958982de><div class=\\\"banner-content\\\" data-v-958982de><div class=\\\"banner-flag\\\" data-v-958982de><i class=\\\"fas fa-flag\\\" data-v-958982de></i></div><div class=\\\"banner-text\\\" data-v-958982de><span class=\\\"banner-title\\\" data-v-958982de>An official website of Barangay Bula</span><span class=\\\"banner-subtitle\\\" data-v-958982de>General Santos City, Philippines</span></div></div></div>\", 1)), _createCommentVNode(\" Hero Section \"), _createElementVNode(\"section\", _hoisted_2, [_cache[8] || (_cache[8] = _createElementVNode(\"div\", {\n    class: \"hero-background\"\n  }, [_createElementVNode(\"div\", {\n    class: \"hero-overlay\"\n  })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_3, [_cache[7] || (_cache[7] = _createStaticVNode(\"<div class=\\\"hero-header\\\" data-v-958982de><img src=\\\"\" + _imports_0 + \"\\\" alt=\\\"Barangay Bula Official Seal\\\" class=\\\"hero-logo\\\" data-v-958982de><div class=\\\"hero-text\\\" data-v-958982de><h1 class=\\\"hero-title\\\" data-v-958982de>Barangay Bula</h1><p class=\\\"hero-subtitle\\\" data-v-958982de>Digital Document Request Portal</p></div></div>\", 1)), _createElementVNode(\"div\", _hoisted_4, [_cache[5] || (_cache[5] = _createElementVNode(\"h2\", {\n    class: \"hero-headline\"\n  }, \"Request Official Documents Online\", -1 /* HOISTED */)), _cache[6] || (_cache[6] = _createElementVNode(\"p\", {\n    class: \"hero-description\"\n  }, \" Get barangay certificates, clearances, and permits quickly and securely. Our digital platform serves over 34,000 residents with efficient government services. \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"button\", {\n    class: \"btn btn-primary\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.goToLogin && $options.goToLogin(...args)),\n    disabled: $data.loading\n  }, [_cache[3] || (_cache[3] = _createElementVNode(\"i\", {\n    class: \"fas fa-sign-in-alt\"\n  }, null, -1 /* HOISTED */)), !$data.loading ? (_openBlock(), _createElementBlock(\"span\", _hoisted_7, \"Sign In\")) : (_openBlock(), _createElementBlock(\"span\", _hoisted_8, _cache[2] || (_cache[2] = [_createElementVNode(\"i\", {\n    class: \"fas fa-spinner fa-spin\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Loading... \")])))], 8 /* PROPS */, _hoisted_6), _createElementVNode(\"button\", {\n    class: \"btn btn-outline\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.goToRegister && $options.goToRegister(...args))\n  }, _cache[4] || (_cache[4] = [_createElementVNode(\"i\", {\n    class: \"fas fa-user-plus\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Create Account \")]))])])])]), _createCommentVNode(\" Services Section \"), _cache[10] || (_cache[10] = _createStaticVNode(\"<section class=\\\"services-section\\\" data-v-958982de><div class=\\\"container\\\" data-v-958982de><div class=\\\"section-header\\\" data-v-958982de><h3 class=\\\"section-title\\\" data-v-958982de>Available Services</h3><p class=\\\"section-description\\\" data-v-958982de>Request these documents online through our secure portal</p></div><div class=\\\"services-grid\\\" data-v-958982de><div class=\\\"service-card\\\" data-v-958982de><div class=\\\"service-icon\\\" data-v-958982de><i class=\\\"fas fa-certificate\\\" data-v-958982de></i></div><h4 class=\\\"service-title\\\" data-v-958982de>Barangay Certificate</h4><p class=\\\"service-description\\\" data-v-958982de>Official certification of residency and good standing</p></div><div class=\\\"service-card\\\" data-v-958982de><div class=\\\"service-icon\\\" data-v-958982de><i class=\\\"fas fa-id-card\\\" data-v-958982de></i></div><h4 class=\\\"service-title\\\" data-v-958982de>Barangay Clearance</h4><p class=\\\"service-description\\\" data-v-958982de>Required for employment and business applications</p></div><div class=\\\"service-card\\\" data-v-958982de><div class=\\\"service-icon\\\" data-v-958982de><i class=\\\"fas fa-file-alt\\\" data-v-958982de></i></div><h4 class=\\\"service-title\\\" data-v-958982de>Indigency Certificate</h4><p class=\\\"service-description\\\" data-v-958982de>For financial assistance and scholarship applications</p></div><div class=\\\"service-card\\\" data-v-958982de><div class=\\\"service-icon\\\" data-v-958982de><i class=\\\"fas fa-users\\\" data-v-958982de></i></div><h4 class=\\\"service-title\\\" data-v-958982de>Community Tax Certificate</h4><p class=\\\"service-description\\\" data-v-958982de>Annual tax certificate for residents</p></div></div></div></section>\", 1)), _createCommentVNode(\" Benefits Section \"), _cache[11] || (_cache[11] = _createStaticVNode(\"<section class=\\\"benefits-section\\\" data-v-958982de><div class=\\\"container\\\" data-v-958982de><div class=\\\"section-header\\\" data-v-958982de><h3 class=\\\"section-title\\\" data-v-958982de>Why Use Our Digital Platform?</h3><p class=\\\"section-description\\\" data-v-958982de>Experience modern, efficient government services</p></div><div class=\\\"benefits-grid\\\" data-v-958982de><div class=\\\"benefit-item\\\" data-v-958982de><div class=\\\"benefit-icon\\\" data-v-958982de><i class=\\\"fas fa-clock\\\" data-v-958982de></i></div><div class=\\\"benefit-content\\\" data-v-958982de><h4 data-v-958982de>Save Time</h4><p data-v-958982de>Submit requests online without visiting our office</p></div></div><div class=\\\"benefit-item\\\" data-v-958982de><div class=\\\"benefit-icon\\\" data-v-958982de><i class=\\\"fas fa-shield-alt\\\" data-v-958982de></i></div><div class=\\\"benefit-content\\\" data-v-958982de><h4 data-v-958982de>Secure &amp; Safe</h4><p data-v-958982de>Your data is protected with government-grade security</p></div></div><div class=\\\"benefit-item\\\" data-v-958982de><div class=\\\"benefit-icon\\\" data-v-958982de><i class=\\\"fas fa-mobile-alt\\\" data-v-958982de></i></div><div class=\\\"benefit-content\\\" data-v-958982de><h4 data-v-958982de>24/7 Access</h4><p data-v-958982de>Request documents anytime from any device</p></div></div><div class=\\\"benefit-item\\\" data-v-958982de><div class=\\\"benefit-icon\\\" data-v-958982de><i class=\\\"fas fa-search\\\" data-v-958982de></i></div><div class=\\\"benefit-content\\\" data-v-958982de><h4 data-v-958982de>Track Status</h4><p data-v-958982de>Monitor your request progress in real-time</p></div></div></div></div></section>\", 1)), _createCommentVNode(\" About Section \"), _cache[12] || (_cache[12] = _createStaticVNode(\"<section class=\\\"about-section\\\" data-v-958982de><div class=\\\"container\\\" data-v-958982de><div class=\\\"about-content\\\" data-v-958982de><div class=\\\"about-text\\\" data-v-958982de><h3 data-v-958982de>About Barangay Bula</h3><p data-v-958982de> Established in 1959, Barangay Bula is a thriving coastal community in General Santos City, serving over 34,000 residents. We are committed to providing transparent, efficient, and accessible government services to all our constituents. </p><p data-v-958982de> This digital platform represents our commitment to modernizing public service delivery, making it easier for residents to access essential government documents and services. </p></div><div class=\\\"about-stats\\\" data-v-958982de><div class=\\\"stat-item\\\" data-v-958982de><div class=\\\"stat-number\\\" data-v-958982de>34,000+</div><div class=\\\"stat-label\\\" data-v-958982de>Residents Served</div></div><div class=\\\"stat-item\\\" data-v-958982de><div class=\\\"stat-number\\\" data-v-958982de>1959</div><div class=\\\"stat-label\\\" data-v-958982de>Year Established</div></div><div class=\\\"stat-item\\\" data-v-958982de><div class=\\\"stat-number\\\" data-v-958982de>24/7</div><div class=\\\"stat-label\\\" data-v-958982de>Online Access</div></div></div></div></div></section>\", 1)), _createCommentVNode(\" Footer \"), _cache[13] || (_cache[13] = _createStaticVNode(\"<footer class=\\\"footer-section\\\" data-v-958982de><div class=\\\"container\\\" data-v-958982de><div class=\\\"footer-content\\\" data-v-958982de><div class=\\\"footer-info\\\" data-v-958982de><div class=\\\"footer-logo\\\" data-v-958982de><img src=\\\"\" + _imports_0 + \"\\\" alt=\\\"Barangay Bula Official Seal\\\" class=\\\"footer-logo-img\\\" data-v-958982de><div class=\\\"footer-text\\\" data-v-958982de><h4 data-v-958982de>Barangay Bula</h4><p data-v-958982de>General Santos City</p></div></div><div class=\\\"footer-contact\\\" data-v-958982de><h5 data-v-958982de>Contact Information</h5><div class=\\\"contact-item\\\" data-v-958982de><i class=\\\"fas fa-map-marker-alt\\\" data-v-958982de></i><span data-v-958982de>Barangay Bula, General Santos City</span></div><div class=\\\"contact-item\\\" data-v-958982de><i class=\\\"fas fa-phone\\\" data-v-958982de></i><span data-v-958982de>(083) 552-XXXX</span></div><div class=\\\"contact-item\\\" data-v-958982de><i class=\\\"fas fa-envelope\\\" data-v-958982de></i><span data-v-958982de><EMAIL></span></div></div><div class=\\\"footer-hours\\\" data-v-958982de><h5 data-v-958982de>Office Hours</h5><p data-v-958982de>Monday - Friday<br data-v-958982de>8:00 AM - 5:00 PM</p></div></div><div class=\\\"footer-bottom\\\" data-v-958982de><p data-v-958982de>© 2024 Barangay Bula, General Santos City. All rights reserved.</p><p data-v-958982de>Committed to transparent and efficient public service.</p></div></div></div></footer>\", 1))]);\n}", "map": {"version": 3, "names": ["_imports_0", "class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "onClick", "_cache", "args", "$options", "goToLogin", "disabled", "$data", "loading", "_hoisted_7", "_hoisted_8", "goToRegister"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\WelcomePage.vue"], "sourcesContent": ["<template>\n  <div class=\"welcome-page\">\n    <!-- Government Banner -->\n    <div class=\"gov-banner\">\n      <div class=\"banner-content\">\n        <div class=\"banner-flag\">\n          <i class=\"fas fa-flag\"></i>\n        </div>\n        <div class=\"banner-text\">\n          <span class=\"banner-title\">An official website of Barangay Bula</span>\n          <span class=\"banner-subtitle\">General Santos City, Philippines</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Hero Section -->\n    <section class=\"hero-section\">\n      <div class=\"hero-background\">\n        <div class=\"hero-overlay\"></div>\n      </div>\n      <div class=\"hero-content\">\n        <div class=\"hero-header\">\n          <img\n            src=\"@/assets/icon-of-bula.jpg\"\n            alt=\"Barangay Bula Official Seal\"\n            class=\"hero-logo\"\n          />\n          <div class=\"hero-text\">\n            <h1 class=\"hero-title\">Barangay Bula</h1>\n            <p class=\"hero-subtitle\">Digital Document Request Portal</p>\n          </div>\n        </div>\n\n        <div class=\"hero-main\">\n          <h2 class=\"hero-headline\">Request Official Documents Online</h2>\n          <p class=\"hero-description\">\n            Get barangay certificates, clearances, and permits quickly and securely.\n            Our digital platform serves over 34,000 residents with efficient government services.\n          </p>\n\n          <div class=\"hero-actions\">\n            <button\n              class=\"btn btn-primary\"\n              @click=\"goToLogin\"\n              :disabled=\"loading\"\n            >\n              <i class=\"fas fa-sign-in-alt\"></i>\n              <span v-if=\"!loading\">Sign In</span>\n              <span v-else>\n                <i class=\"fas fa-spinner fa-spin\"></i>\n                Loading...\n              </span>\n            </button>\n            <button\n              class=\"btn btn-outline\"\n              @click=\"goToRegister\"\n            >\n              <i class=\"fas fa-user-plus\"></i>\n              Create Account\n            </button>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- Services Section -->\n    <section class=\"services-section\">\n      <div class=\"container\">\n        <div class=\"section-header\">\n          <h3 class=\"section-title\">Available Services</h3>\n          <p class=\"section-description\">Request these documents online through our secure portal</p>\n        </div>\n\n        <div class=\"services-grid\">\n          <div class=\"service-card\">\n            <div class=\"service-icon\">\n              <i class=\"fas fa-certificate\"></i>\n            </div>\n            <h4 class=\"service-title\">Barangay Certificate</h4>\n            <p class=\"service-description\">Official certification of residency and good standing</p>\n          </div>\n\n          <div class=\"service-card\">\n            <div class=\"service-icon\">\n              <i class=\"fas fa-id-card\"></i>\n            </div>\n            <h4 class=\"service-title\">Barangay Clearance</h4>\n            <p class=\"service-description\">Required for employment and business applications</p>\n          </div>\n\n          <div class=\"service-card\">\n            <div class=\"service-icon\">\n              <i class=\"fas fa-file-alt\"></i>\n            </div>\n            <h4 class=\"service-title\">Indigency Certificate</h4>\n            <p class=\"service-description\">For financial assistance and scholarship applications</p>\n          </div>\n\n          <div class=\"service-card\">\n            <div class=\"service-icon\">\n              <i class=\"fas fa-users\"></i>\n            </div>\n            <h4 class=\"service-title\">Community Tax Certificate</h4>\n            <p class=\"service-description\">Annual tax certificate for residents</p>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- Benefits Section -->\n    <section class=\"benefits-section\">\n      <div class=\"container\">\n        <div class=\"section-header\">\n          <h3 class=\"section-title\">Why Use Our Digital Platform?</h3>\n          <p class=\"section-description\">Experience modern, efficient government services</p>\n        </div>\n\n        <div class=\"benefits-grid\">\n          <div class=\"benefit-item\">\n            <div class=\"benefit-icon\">\n              <i class=\"fas fa-clock\"></i>\n            </div>\n            <div class=\"benefit-content\">\n              <h4>Save Time</h4>\n              <p>Submit requests online without visiting our office</p>\n            </div>\n          </div>\n\n          <div class=\"benefit-item\">\n            <div class=\"benefit-icon\">\n              <i class=\"fas fa-shield-alt\"></i>\n            </div>\n            <div class=\"benefit-content\">\n              <h4>Secure & Safe</h4>\n              <p>Your data is protected with government-grade security</p>\n            </div>\n          </div>\n\n          <div class=\"benefit-item\">\n            <div class=\"benefit-icon\">\n              <i class=\"fas fa-mobile-alt\"></i>\n            </div>\n            <div class=\"benefit-content\">\n              <h4>24/7 Access</h4>\n              <p>Request documents anytime from any device</p>\n            </div>\n          </div>\n\n          <div class=\"benefit-item\">\n            <div class=\"benefit-icon\">\n              <i class=\"fas fa-search\"></i>\n            </div>\n            <div class=\"benefit-content\">\n              <h4>Track Status</h4>\n              <p>Monitor your request progress in real-time</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- About Section -->\n    <section class=\"about-section\">\n      <div class=\"container\">\n        <div class=\"about-content\">\n          <div class=\"about-text\">\n            <h3>About Barangay Bula</h3>\n            <p>\n              Established in 1959, Barangay Bula is a thriving coastal community in General Santos City,\n              serving over 34,000 residents. We are committed to providing transparent, efficient,\n              and accessible government services to all our constituents.\n            </p>\n            <p>\n              This digital platform represents our commitment to modernizing public service delivery,\n              making it easier for residents to access essential government documents and services.\n            </p>\n          </div>\n          <div class=\"about-stats\">\n            <div class=\"stat-item\">\n              <div class=\"stat-number\">34,000+</div>\n              <div class=\"stat-label\">Residents Served</div>\n            </div>\n            <div class=\"stat-item\">\n              <div class=\"stat-number\">1959</div>\n              <div class=\"stat-label\">Year Established</div>\n            </div>\n            <div class=\"stat-item\">\n              <div class=\"stat-number\">24/7</div>\n              <div class=\"stat-label\">Online Access</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- Footer -->\n    <footer class=\"footer-section\">\n      <div class=\"container\">\n        <div class=\"footer-content\">\n          <div class=\"footer-info\">\n            <div class=\"footer-logo\">\n              <img\n                src=\"@/assets/icon-of-bula.jpg\"\n                alt=\"Barangay Bula Official Seal\"\n                class=\"footer-logo-img\"\n              />\n              <div class=\"footer-text\">\n                <h4>Barangay Bula</h4>\n                <p>General Santos City</p>\n              </div>\n            </div>\n\n            <div class=\"footer-contact\">\n              <h5>Contact Information</h5>\n              <div class=\"contact-item\">\n                <i class=\"fas fa-map-marker-alt\"></i>\n                <span>Barangay Bula, General Santos City</span>\n              </div>\n              <div class=\"contact-item\">\n                <i class=\"fas fa-phone\"></i>\n                <span>(083) 552-XXXX</span>\n              </div>\n              <div class=\"contact-item\">\n                <i class=\"fas fa-envelope\"></i>\n                <span><EMAIL></span>\n              </div>\n            </div>\n\n            <div class=\"footer-hours\">\n              <h5>Office Hours</h5>\n              <p>Monday - Friday<br>8:00 AM - 5:00 PM</p>\n            </div>\n          </div>\n\n          <div class=\"footer-bottom\">\n            <p>&copy; 2024 Barangay Bula, General Santos City. All rights reserved.</p>\n            <p>Committed to transparent and efficient public service.</p>\n          </div>\n        </div>\n      </div>\n    </footer>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'WelcomePage',\n  data() {\n    return {\n      loading: false\n    };\n  },\n  methods: {\n    async goToLogin() {\n      this.loading = true;\n      try {\n        // Add a small delay for better UX\n        await new Promise(resolve => setTimeout(resolve, 500));\n        this.$router.push({ name: 'UnifiedLogin' });\n      } catch (error) {\n        console.error('Navigation error:', error);\n        // Fallback navigation\n        window.location.href = '/login';\n      } finally {\n        this.loading = false;\n      }\n    },\n    \n    goToRegister() {\n      this.$router.push({ name: 'ClientRegistration' });\n    }\n  }\n};\n</script>\n\n<style scoped>\n/* Reset and base styles */\n* {\n  box-sizing: border-box;\n}\n\n.welcome-page {\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n  line-height: 1.6;\n  color: #333;\n  background-color: #f8f9fa;\n}\n\n/* Container utility */\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 1rem;\n}\n\n/* Government Banner */\n.gov-banner {\n  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);\n  color: white;\n  padding: 0.75rem 0;\n  border-bottom: 3px solid #fbbf24;\n}\n\n.banner-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 1rem;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.banner-flag {\n  font-size: 1.25rem;\n  color: #fbbf24;\n}\n\n.banner-text {\n  display: flex;\n  flex-direction: column;\n  gap: 0.125rem;\n}\n\n.banner-title {\n  font-weight: 600;\n  font-size: 0.875rem;\n}\n\n.banner-subtitle {\n  font-size: 0.75rem;\n  opacity: 0.9;\n}\n\n/* Hero Section */\n.hero-section {\n  position: relative;\n  min-height: 70vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n  color: white;\n}\n\n.hero-background {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-image: url('@/assets/bula-welcome-page-background-pic.JPG');\n  background-size: cover;\n  background-position: center;\n  background-repeat: no-repeat;\n  z-index: -2;\n}\n\n.hero-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    135deg,\n    rgba(30, 58, 138, 0.85) 0%,\n    rgba(30, 64, 175, 0.9) 100%\n  );\n  z-index: -1;\n}\n\n.hero-content {\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 2rem 1rem;\n  z-index: 1;\n}\n\n.hero-header {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 1.5rem;\n  margin-bottom: 2rem;\n}\n\n.hero-logo {\n  width: 80px;\n  height: 80px;\n  border-radius: 50%;\n  border: 3px solid white;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\n}\n\n.hero-text {\n  text-align: left;\n}\n\n.hero-title {\n  font-size: clamp(1.75rem, 4vw, 2.5rem);\n  font-weight: 700;\n  margin: 0;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n}\n\n.hero-subtitle {\n  font-size: clamp(1rem, 2.5vw, 1.25rem);\n  margin: 0.25rem 0 0 0;\n  opacity: 0.95;\n  font-weight: 400;\n}\n\n.hero-main {\n  margin-top: 2rem;\n}\n\n.hero-headline {\n  font-size: clamp(2rem, 5vw, 3rem);\n  font-weight: 700;\n  margin-bottom: 1rem;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n}\n\n.hero-description {\n  font-size: clamp(1.1rem, 2.5vw, 1.25rem);\n  margin-bottom: 2rem;\n  opacity: 0.95;\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n.hero-actions {\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n  flex-wrap: wrap;\n}\n\n/* Header Section */\n.welcome-header {\n  text-align: center;\n  margin-bottom: 3rem;\n  padding: 2rem 0;\n}\n\n.logo-container {\n  margin-bottom: 2rem;\n}\n\n.logo {\n  width: 120px;\n  height: 120px;\n  border-radius: 50%;\n  border: 4px solid rgba(255, 255, 255, 0.9);\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n  transition: transform 0.3s ease;\n}\n\n.logo:hover {\n  transform: scale(1.05);\n}\n\n.title-section {\n  color: white;\n}\n\n.main-title {\n  font-size: clamp(2.5rem, 6vw, 4rem);\n  font-weight: 700;\n  margin-bottom: 1rem;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n  line-height: 1.2;\n}\n\n.subtitle {\n  font-size: clamp(1.25rem, 3vw, 2rem);\n  font-weight: 400;\n  margin-bottom: 1rem;\n  color: rgba(255, 255, 255, 0.95);\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\n}\n\n.tagline {\n  font-size: clamp(1rem, 2.5vw, 1.25rem);\n  color: rgba(255, 255, 255, 0.9);\n  font-weight: 300;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\n}\n\n/* Information Section */\n.info-section {\n  margin-bottom: 3rem;\n}\n\n.info-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 2rem;\n  margin-bottom: 2rem;\n}\n\n.info-card {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: 1rem;\n  padding: 2rem;\n  text-align: center;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.info-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\n}\n\n.info-icon {\n  width: 4rem;\n  height: 4rem;\n  background: linear-gradient(135deg, #007bbf, #005a86);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 0 auto 1.5rem;\n  color: white;\n  font-size: 1.5rem;\n}\n\n.info-card h3 {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 1rem;\n}\n\n.info-card p {\n  color: #4a5568;\n  line-height: 1.6;\n  font-size: 1rem;\n}\n\n/* Services Preview */\n.services-preview {\n  margin-bottom: 3rem;\n  text-align: center;\n}\n\n.services-title {\n  font-size: clamp(1.75rem, 4vw, 2.5rem);\n  font-weight: 600;\n  color: white;\n  margin-bottom: 2rem;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n}\n\n.services-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1.5rem;\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.service-item {\n  background: rgba(255, 255, 255, 0.9);\n  backdrop-filter: blur(10px);\n  border-radius: 0.75rem;\n  padding: 1.5rem;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 1rem;\n  transition: transform 0.3s ease, background 0.3s ease;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.service-item:hover {\n  transform: translateY(-3px);\n  background: rgba(255, 255, 255, 0.95);\n}\n\n.service-item i {\n  font-size: 2rem;\n  color: #007bbf;\n}\n\n.service-item span {\n  font-weight: 500;\n  color: #1a365d;\n  text-align: center;\n}\n\n/* Call to Action Section */\n.cta-section {\n  margin-bottom: 3rem;\n  text-align: center;\n}\n\n.cta-content {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: 1.5rem;\n  padding: 3rem 2rem;\n  max-width: 600px;\n  margin: 0 auto;\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.cta-title {\n  font-size: clamp(1.5rem, 3vw, 2rem);\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 1rem;\n}\n\n.cta-description {\n  color: #4a5568;\n  font-size: 1.1rem;\n  line-height: 1.6;\n  margin-bottom: 2rem;\n}\n\n.login-button {\n  background: linear-gradient(135deg, #007bbf, #005a86);\n  color: white;\n  border: none;\n  padding: 1rem 2.5rem;\n  border-radius: 0.75rem;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: inline-flex;\n  align-items: center;\n  gap: 0.75rem;\n  box-shadow: 0 4px 15px rgba(0, 123, 191, 0.3);\n  min-width: 200px;\n  justify-content: center;\n}\n\n.login-button:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(0, 123, 191, 0.4);\n  background: linear-gradient(135deg, #0056b3, #004a73);\n}\n\n.login-button:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n  transform: none;\n}\n\n.register-link {\n  margin-top: 1.5rem;\n}\n\n.register-link p {\n  color: #4a5568;\n  font-size: 1rem;\n}\n\n.register-btn {\n  color: #007bbf;\n  text-decoration: none;\n  font-weight: 500;\n  transition: color 0.3s ease;\n}\n\n.register-btn:hover {\n  color: #005a86;\n  text-decoration: underline;\n}\n\n/* Footer */\n.welcome-footer {\n  margin-top: auto;\n  background: rgba(26, 54, 93, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: 1rem 1rem 0 0;\n  padding: 2rem;\n  color: white;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.footer-content {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 2rem;\n  margin-bottom: 2rem;\n}\n\n.contact-info h4,\n.office-hours h4 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  margin-bottom: 1rem;\n  color: rgba(255, 255, 255, 0.95);\n}\n\n.contact-item {\n  display: flex;\n  align-items: flex-start;\n  gap: 0.75rem;\n  margin-bottom: 0.75rem;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.contact-item i {\n  color: #4fc3f7;\n  margin-top: 0.125rem;\n  flex-shrink: 0;\n}\n\n.office-hours p {\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 0.5rem;\n  line-height: 1.5;\n}\n\n.footer-bottom {\n  border-top: 1px solid rgba(255, 255, 255, 0.2);\n  padding-top: 1.5rem;\n  text-align: center;\n}\n\n.footer-bottom p {\n  color: rgba(255, 255, 255, 0.8);\n  margin-bottom: 0.5rem;\n}\n\n.powered-by {\n  font-size: 0.9rem;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .content-container {\n    padding: 1rem 0.75rem;\n  }\n\n  .welcome-header {\n    margin-bottom: 2rem;\n    padding: 1rem 0;\n  }\n\n  .logo {\n    width: 100px;\n    height: 100px;\n  }\n\n  .info-grid {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n\n  .info-card {\n    padding: 1.5rem;\n  }\n\n  .services-grid {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 1rem;\n  }\n\n  .service-item {\n    padding: 1rem;\n  }\n\n  .cta-content {\n    padding: 2rem 1.5rem;\n    margin: 0 1rem;\n  }\n\n  .login-button {\n    padding: 0.875rem 2rem;\n    font-size: 1rem;\n    min-width: 180px;\n  }\n\n  .welcome-footer {\n    padding: 1.5rem;\n    border-radius: 0;\n  }\n\n  .footer-content {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n    text-align: center;\n  }\n}\n\n@media (max-width: 480px) {\n  .content-container {\n    padding: 0.75rem 0.5rem;\n  }\n\n  .info-card {\n    padding: 1.25rem;\n  }\n\n  .services-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .cta-content {\n    margin: 0 0.5rem;\n    padding: 1.5rem 1rem;\n  }\n\n  .login-button {\n    width: 100%;\n    max-width: 280px;\n  }\n}\n\n/* Animation for smooth entrance */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.welcome-header,\n.info-section,\n.services-preview,\n.cta-section,\n.welcome-footer {\n  animation: fadeInUp 0.8s ease-out;\n}\n\n.info-section {\n  animation-delay: 0.2s;\n}\n\n.services-preview {\n  animation-delay: 0.4s;\n}\n\n.cta-section {\n  animation-delay: 0.6s;\n}\n\n.welcome-footer {\n  animation-delay: 0.8s;\n}\n\n/* Accessibility improvements */\n@media (prefers-reduced-motion: reduce) {\n  .welcome-header,\n  .info-section,\n  .services-preview,\n  .cta-section,\n  .welcome-footer {\n    animation: none;\n  }\n\n  .logo:hover,\n  .info-card:hover,\n  .service-item:hover,\n  .login-button:hover:not(:disabled) {\n    transform: none;\n  }\n}\n\n/* High contrast mode support */\n@media (prefers-contrast: high) {\n  .background-overlay {\n    background: rgba(0, 0, 0, 0.8);\n  }\n\n  .info-card,\n  .service-item,\n  .cta-content {\n    background: rgba(255, 255, 255, 1);\n    border: 2px solid #000;\n  }\n\n  .welcome-footer {\n    background: rgba(0, 0, 0, 0.95);\n    border: 2px solid #fff;\n  }\n}\n</style>\n"], "mappings": ";OAuBYA,UAA+B;;EAtBpCC,KAAK,EAAC;AAAc;;EAedA,KAAK,EAAC;AAAc;;EAItBA,KAAK,EAAC;AAAc;;EAalBA,KAAK,EAAC;AAAW;;EAOfA,KAAK,EAAC;AAAc;;;;;;;;;uBAvCjCC,mBAAA,CAgPM,OAhPNC,UAgPM,GA/OJC,mBAAA,uBAA0B,E,wdAa1BA,mBAAA,kBAAqB,EACrBC,mBAAA,CA+CU,WA/CVC,UA+CU,G,0BA9CRD,mBAAA,CAEM;IAFDJ,KAAK,EAAC;EAAiB,IAC1BI,mBAAA,CAAgC;IAA3BJ,KAAK,EAAC;EAAc,G,sBAE3BI,mBAAA,CA0CM,OA1CNE,UA0CM,G,uYA7BJF,mBAAA,CA4BM,OA5BNG,UA4BM,G,0BA3BJH,mBAAA,CAAgE;IAA5DJ,KAAK,EAAC;EAAe,GAAC,mCAAiC,sB,0BAC3DI,mBAAA,CAGI;IAHDJ,KAAK,EAAC;EAAkB,GAAC,kKAG5B,sBAEAI,mBAAA,CAoBM,OApBNI,UAoBM,GAnBJJ,mBAAA,CAWS;IAVPJ,KAAK,EAAC,iBAAiB;IACtBS,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,SAAA,IAAAD,QAAA,CAAAC,SAAA,IAAAF,IAAA,CAAS;IAChBG,QAAQ,EAAEC,KAAA,CAAAC;gCAEXZ,mBAAA,CAAkC;IAA/BJ,KAAK,EAAC;EAAoB,6B,CAChBe,KAAA,CAAAC,OAAO,I,cAApBf,mBAAA,CAAoC,QAAAgB,UAAA,EAAd,SAAO,M,cAC7BhB,mBAAA,CAGO,QAAAiB,UAAA,EAAAR,MAAA,QAAAA,MAAA,OAFLN,mBAAA,CAAsC;IAAnCJ,KAAK,EAAC;EAAwB,4B,iBAAK,cAExC,E,kCAEFI,mBAAA,CAMS;IALPJ,KAAK,EAAC,iBAAiB;IACtBS,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAO,YAAA,IAAAP,QAAA,CAAAO,YAAA,IAAAR,IAAA,CAAY;gCAEpBP,mBAAA,CAAgC;IAA7BJ,KAAK,EAAC;EAAkB,4B,iBAAK,kBAElC,E,YAMRG,mBAAA,sBAAyB,E,0rDA4CzBA,mBAAA,sBAAyB,E,ipDAoDzBA,mBAAA,mBAAsB,E,wxCAkCtBA,mBAAA,YAAe,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}