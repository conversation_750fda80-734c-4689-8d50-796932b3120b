{"ast": null, "code": "import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue';\nimport { useRouter } from 'vue-router';\nimport ClientNotifications from './ClientNotifications.vue';\n\n// Props\n\nexport default {\n  __name: 'ClientHeader',\n  props: {\n    userName: {\n      type: String,\n      default: 'User'\n    },\n    userEmail: {\n      type: String,\n      default: '<EMAIL>'\n    },\n    userAvatar: {\n      type: String,\n      default: null\n    },\n    showUserDropdown: {\n      type: Boolean,\n      default: false\n    },\n    activeMenu: {\n      type: String,\n      default: 'dashboard'\n    },\n    showBreadcrumbs: {\n      type: Boolean,\n      default: true\n    }\n  },\n  emits: ['user-dropdown-toggle', 'menu-action', 'logout', 'error', 'search', 'notification-click'],\n  setup(__props, {\n    expose: __expose,\n    emit: __emit\n  }) {\n    __expose();\n    const props = __props;\n\n    // Emits\n    const emit = __emit;\n\n    // Router\n    const router = useRouter();\n\n    // Reactive state\n    const showSearch = ref(false);\n    const showMobileMenu = ref(false);\n    const searchQuery = ref('');\n    const searchInput = ref(null);\n    const userProfileRef = ref(null);\n\n    // Computed\n    const pageTitle = computed(() => {\n      const titles = {\n        'dashboard': 'Dashboard',\n        'services': 'Document Services',\n        'requests': 'My Requests',\n        'documents': 'My Documents',\n        'profile': 'My Profile',\n        'settings': 'Account Settings',\n        'history': 'Request History',\n        'notifications': 'Notifications',\n        'help': 'Help & Support'\n      };\n      return titles[props.activeMenu] || 'Dashboard';\n    });\n\n    // Methods\n    const getPageTitle = () => pageTitle.value;\n    const toggleSearch = async () => {\n      showSearch.value = !showSearch.value;\n      if (showSearch.value) {\n        await nextTick();\n        searchInput.value?.focus();\n      }\n    };\n    const closeSearch = () => {\n      showSearch.value = false;\n      searchQuery.value = '';\n    };\n    const performSearch = () => {\n      if (searchQuery.value.trim()) {\n        emit('search', searchQuery.value.trim());\n        // Close search on mobile after search\n        if (window.innerWidth <= 768) {\n          closeSearch();\n        }\n      }\n    };\n    const toggleUserDropdown = () => {\n      emit('user-dropdown-toggle');\n    };\n    const toggleMobileMenu = () => {\n      showMobileMenu.value = !showMobileMenu.value;\n      // Prevent body scroll when mobile menu is open\n      document.body.style.overflow = showMobileMenu.value ? 'hidden' : '';\n    };\n    const handleMenuAction = action => {\n      emit('menu-action', action);\n      // Close mobile menu if open\n      if (showMobileMenu.value) {\n        toggleMobileMenu();\n      }\n    };\n    const handleMobileMenuAction = action => {\n      handleMenuAction(action);\n    };\n    const handleLogout = () => {\n      emit('logout');\n    };\n    const handleOutsideClick = event => {\n      // Check if click is outside user dropdown\n      if (userProfileRef.value && !userProfileRef.value.contains(event.target)) {\n        if (props.showUserDropdown) {\n          toggleUserDropdown();\n        }\n      }\n\n      // Check if click is outside search\n      if (!event.target.closest('.search-container')) {\n        showSearch.value = false;\n      }\n\n      // Check if click is outside mobile menu\n      if (!event.target.closest('.mobile-nav') && !event.target.closest('.mobile-menu-toggle')) {\n        if (showMobileMenu.value) {\n          toggleMobileMenu();\n        }\n      }\n    };\n    const handleKeydown = event => {\n      // Close dropdowns on Escape key\n      if (event.key === 'Escape') {\n        if (showSearch.value) {\n          closeSearch();\n        }\n        if (props.showUserDropdown) {\n          toggleUserDropdown();\n        }\n        if (showMobileMenu.value) {\n          toggleMobileMenu();\n        }\n      }\n    };\n\n    // Notification event handlers\n    const handleNewNotification = notification => {\n      console.log('New notification received:', notification);\n      // Handle new notification - could show toast, update UI, etc.\n    };\n    const handleNotificationClick = async notification => {\n      console.log('🔔 ClientHeader: Notification clicked:', notification);\n\n      // Ensure we have a valid notification object\n      if (!notification || typeof notification !== 'object') {\n        console.error('Invalid notification object received:', notification);\n        return;\n      }\n      try {\n        // Parse notification data\n        const notificationData = typeof notification.data === 'string' ? JSON.parse(notification.data) : notification.data || {};\n\n        // Log navigation for debugging\n        console.log('📊 ClientHeader: Notification data:', notificationData);\n\n        // Additional header-specific logic can go here\n        // For example, updating header state, showing badges, etc.\n      } catch (error) {\n        console.error('❌ ClientHeader: Error handling notification click:', error);\n        emit('error', error);\n      }\n\n      // Always emit the event for other components that might need it\n      emit('notification-click', notification);\n    };\n    const handleNotificationError = error => {\n      console.error('Notification error:', error);\n      emit('error', error);\n    };\n\n    // Lifecycle hooks\n    onMounted(() => {\n      document.addEventListener('click', handleOutsideClick);\n      document.addEventListener('keydown', handleKeydown);\n    });\n    onUnmounted(() => {\n      document.removeEventListener('click', handleOutsideClick);\n      document.removeEventListener('keydown', handleKeydown);\n      // Reset body overflow\n      document.body.style.overflow = '';\n    });\n    const __returned__ = {\n      props,\n      emit,\n      router,\n      showSearch,\n      showMobileMenu,\n      searchQuery,\n      searchInput,\n      userProfileRef,\n      pageTitle,\n      getPageTitle,\n      toggleSearch,\n      closeSearch,\n      performSearch,\n      toggleUserDropdown,\n      toggleMobileMenu,\n      handleMenuAction,\n      handleMobileMenuAction,\n      handleLogout,\n      handleOutsideClick,\n      handleKeydown,\n      handleNewNotification,\n      handleNotificationClick,\n      handleNotificationError,\n      ref,\n      computed,\n      nextTick,\n      onMounted,\n      onUnmounted,\n      get useRouter() {\n        return useRouter;\n      },\n      ClientNotifications\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "nextTick", "onMounted", "onUnmounted", "useRouter", "ClientNotifications", "props", "__props", "emit", "__emit", "router", "showSearch", "showMobileMenu", "searchQuery", "searchInput", "userProfileRef", "pageTitle", "titles", "activeMenu", "getPageTitle", "value", "toggleSearch", "focus", "closeSearch", "performSearch", "trim", "window", "innerWidth", "toggleUserDropdown", "toggleMobileMenu", "document", "body", "style", "overflow", "handleMenuAction", "action", "handleMobileMenuAction", "handleLogout", "handleOutsideClick", "event", "contains", "target", "showUserDropdown", "closest", "handleKeydown", "key", "handleNewNotification", "notification", "console", "log", "handleNotificationClick", "error", "notificationData", "data", "JSON", "parse", "handleNotificationError", "addEventListener", "removeEventListener"], "sources": ["D:/cap2_rhai_front_and_back/BOSFDR/src/components/client/ClientHeader.vue"], "sourcesContent": ["<template>\n  <header class=\"client-header\" role=\"banner\">\n    <!-- Skip to main content link -->\n    <a href=\"#main-content\" class=\"skip-link\">Skip to main content</a>\n\n    <!-- Government Banner -->\n    <div class=\"gov-banner\" role=\"region\" aria-label=\"Government website banner\">\n      <div class=\"gov-banner-content\">\n        <div class=\"gov-banner-flag\">\n          <img\n            src=\"/assets/images/ph_flag_small.png\"\n            alt=\"Philippine flag\"\n            class=\"flag-icon\"\n            width=\"24\"\n            height=\"16\"\n          />\n        </div>\n        <div class=\"gov-banner-text\">\n          <span class=\"gov-banner-label\">An official website of the</span>\n          <strong class=\"gov-banner-agency\">Barangay Bula, General Santos City</strong>\n        </div>\n        <div class=\"gov-banner-secure\">\n          <i class=\"fas fa-shield-alt\" aria-hidden=\"true\"></i>\n          <span>Secure</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Main Header -->\n    <div class=\"main-header\">\n      <div class=\"header-container\">\n        <!-- Logo and Site Identity -->\n        <div class=\"header-brand\">\n          <button\n            class=\"logo-button\"\n            @click=\"handleMenuAction('dashboard')\"\n            aria-label=\"Go to dashboard\"\n            type=\"button\"\n          >\n            <img\n              src=\"@/assets/icon-of-bula.jpg\"\n              alt=\"Barangay Bula Logo\"\n              class=\"logo\"\n              width=\"48\"\n              height=\"48\"\n            />\n            <div class=\"site-identity\">\n              <h1 class=\"site-title\">Barangay Bula</h1>\n              <span class=\"site-subtitle\">Digital Services Portal</span>\n            </div>\n          </button>\n        </div>\n\n        <!-- Primary Navigation -->\n        <nav class=\"primary-nav\" role=\"navigation\" aria-label=\"Primary navigation\">\n          <ul class=\"nav-list\" role=\"menubar\">\n            <li class=\"nav-item\" role=\"none\">\n              <button\n                class=\"nav-link\"\n                :class=\"{ active: activeMenu === 'dashboard' }\"\n                @click=\"handleMenuAction('dashboard')\"\n                role=\"menuitem\"\n                :aria-current=\"activeMenu === 'dashboard' ? 'page' : undefined\"\n              >\n                <i class=\"fas fa-home\" aria-hidden=\"true\"></i>\n                <span>Dashboard</span>\n              </button>\n            </li>\n            <li class=\"nav-item\" role=\"none\">\n              <button\n                class=\"nav-link\"\n                :class=\"{ active: activeMenu === 'services' }\"\n                @click=\"handleMenuAction('services')\"\n                role=\"menuitem\"\n                :aria-current=\"activeMenu === 'services' ? 'page' : undefined\"\n              >\n                <i class=\"fas fa-file-alt\" aria-hidden=\"true\"></i>\n                <span>Services</span>\n              </button>\n            </li>\n            <li class=\"nav-item\" role=\"none\">\n              <button\n                class=\"nav-link\"\n                :class=\"{ active: activeMenu === 'requests' }\"\n                @click=\"handleMenuAction('requests')\"\n                role=\"menuitem\"\n                :aria-current=\"activeMenu === 'requests' ? 'page' : undefined\"\n              >\n                <i class=\"fas fa-clock\" aria-hidden=\"true\"></i>\n                <span>My Requests</span>\n              </button>\n            </li>\n            <li class=\"nav-item\" role=\"none\">\n              <button\n                class=\"nav-link\"\n                :class=\"{ active: activeMenu === 'help' }\"\n                @click=\"handleMenuAction('help')\"\n                role=\"menuitem\"\n                :aria-current=\"activeMenu === 'help' ? 'page' : undefined\"\n              >\n                <i class=\"fas fa-question-circle\" aria-hidden=\"true\"></i>\n                <span>Help</span>\n              </button>\n            </li>\n          </ul>\n        </nav>\n\n        <!-- Header Actions -->\n        <div class=\"header-actions\">\n          <!-- Search -->\n          <div class=\"search-container\">\n            <button\n              class=\"search-toggle\"\n              @click=\"toggleSearch\"\n              :aria-label=\"showSearch ? 'Close search' : 'Open search'\"\n              :aria-expanded=\"showSearch\"\n              type=\"button\"\n            >\n              <i class=\"fas fa-search\" aria-hidden=\"true\"></i>\n              <span class=\"sr-only\">Search</span>\n            </button>\n\n            <Transition name=\"search-slide\">\n              <div v-if=\"showSearch\" class=\"search-box\" role=\"search\">\n                <label for=\"header-search\" class=\"sr-only\">Search documents and services</label>\n                <input\n                  id=\"header-search\"\n                  ref=\"searchInput\"\n                  type=\"search\"\n                  placeholder=\"Search documents, services...\"\n                  class=\"search-input\"\n                  v-model=\"searchQuery\"\n                  @keyup.enter=\"performSearch\"\n                  @keyup.escape=\"closeSearch\"\n                  autocomplete=\"off\"\n                  aria-describedby=\"search-help\"\n                />\n                <span id=\"search-help\" class=\"sr-only\">\n                  Press Enter to search, Escape to close\n                </span>\n                <button\n                  class=\"search-submit\"\n                  @click=\"performSearch\"\n                  aria-label=\"Submit search\"\n                  type=\"button\"\n                >\n                  <i class=\"fas fa-search\" aria-hidden=\"true\"></i>\n                </button>\n              </div>\n            </Transition>\n          </div>\n\n          <!-- Notifications -->\n          <ClientNotifications\n            @new-notification=\"handleNewNotification\"\n            @notification-click=\"handleNotificationClick\"\n            @error=\"handleNotificationError\"\n          />\n\n          <!-- User Profile Menu -->\n          <div class=\"user-profile\" ref=\"userProfileRef\">\n            <button\n              class=\"user-button\"\n              @click=\"toggleUserDropdown\"\n              :aria-label=\"`User menu for ${userName}`\"\n              :aria-expanded=\"showUserDropdown\"\n              type=\"button\"\n            >\n              <div class=\"user-avatar\">\n                <img\n                  v-if=\"userAvatar\"\n                  :src=\"userAvatar\"\n                  :alt=\"`${userName} avatar`\"\n                  class=\"avatar-image\"\n                  width=\"32\"\n                  height=\"32\"\n                />\n                <i v-else class=\"fas fa-user-circle avatar-icon\" aria-hidden=\"true\"></i>\n              </div>\n              <div class=\"user-info\">\n                <span class=\"user-name\">{{ userName }}</span>\n                <span class=\"user-role\">Client Portal</span>\n              </div>\n              <i\n                class=\"fas fa-chevron-down dropdown-arrow\"\n                :class=\"{ rotated: showUserDropdown }\"\n                aria-hidden=\"true\"\n              ></i>\n            </button>\n\n            <Transition name=\"dropdown-fade\">\n              <div\n                v-if=\"showUserDropdown\"\n                class=\"user-dropdown\"\n                role=\"menu\"\n                aria-label=\"User account options\"\n              >\n                <div class=\"dropdown-header\">\n                  <div class=\"user-details\">\n                    <strong class=\"user-display-name\">{{ userName }}</strong>\n                    <span class=\"user-email\">{{ userEmail }}</span>\n                  </div>\n                </div>\n\n                <div class=\"dropdown-divider\" role=\"separator\"></div>\n\n                <button\n                  class=\"dropdown-item\"\n                  @click=\"handleMenuAction('profile')\"\n                  role=\"menuitem\"\n                  type=\"button\"\n                >\n                  <i class=\"fas fa-user\" aria-hidden=\"true\"></i>\n                  <span>My Profile</span>\n                </button>\n\n                <button\n                  class=\"dropdown-item\"\n                  @click=\"handleMenuAction('settings')\"\n                  role=\"menuitem\"\n                  type=\"button\"\n                >\n                  <i class=\"fas fa-cog\" aria-hidden=\"true\"></i>\n                  <span>Account Settings</span>\n                </button>\n\n                <button\n                  class=\"dropdown-item\"\n                  @click=\"handleMenuAction('documents')\"\n                  role=\"menuitem\"\n                  type=\"button\"\n                >\n                  <i class=\"fas fa-folder\" aria-hidden=\"true\"></i>\n                  <span>My Documents</span>\n                </button>\n\n                <button\n                  class=\"dropdown-item\"\n                  @click=\"handleMenuAction('history')\"\n                  role=\"menuitem\"\n                  type=\"button\"\n                >\n                  <i class=\"fas fa-history\" aria-hidden=\"true\"></i>\n                  <span>Request History</span>\n                </button>\n\n                <div class=\"dropdown-divider\" role=\"separator\"></div>\n\n                <button\n                  class=\"dropdown-item\"\n                  @click=\"handleMenuAction('help')\"\n                  role=\"menuitem\"\n                  type=\"button\"\n                >\n                  <i class=\"fas fa-question-circle\" aria-hidden=\"true\"></i>\n                  <span>Help & Support</span>\n                </button>\n\n                <div class=\"dropdown-divider\" role=\"separator\"></div>\n\n                <button\n                  class=\"dropdown-item logout-item\"\n                  @click=\"handleLogout\"\n                  role=\"menuitem\"\n                  type=\"button\"\n                >\n                  <i class=\"fas fa-sign-out-alt\" aria-hidden=\"true\"></i>\n                  <span>Sign Out</span>\n                </button>\n              </div>\n            </Transition>\n          </div>\n\n          <!-- Mobile Menu Toggle -->\n          <button\n            class=\"mobile-menu-toggle\"\n            @click=\"toggleMobileMenu\"\n            :aria-label=\"showMobileMenu ? 'Close navigation menu' : 'Open navigation menu'\"\n            :aria-expanded=\"showMobileMenu\"\n            type=\"button\"\n          >\n            <span class=\"hamburger-line\" :class=\"{ active: showMobileMenu }\"></span>\n            <span class=\"hamburger-line\" :class=\"{ active: showMobileMenu }\"></span>\n            <span class=\"hamburger-line\" :class=\"{ active: showMobileMenu }\"></span>\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Mobile Navigation Menu -->\n    <Transition name=\"mobile-menu-slide\">\n      <div v-if=\"showMobileMenu\" class=\"mobile-nav\" role=\"navigation\" aria-label=\"Mobile navigation\">\n        <div class=\"mobile-nav-content\">\n          <nav class=\"mobile-nav-menu\">\n            <ul class=\"mobile-nav-list\" role=\"menubar\">\n              <li class=\"mobile-nav-item\" role=\"none\">\n                <button\n                  class=\"mobile-nav-link\"\n                  :class=\"{ active: activeMenu === 'dashboard' }\"\n                  @click=\"handleMobileMenuAction('dashboard')\"\n                  role=\"menuitem\"\n                  type=\"button\"\n                >\n                  <i class=\"fas fa-home\" aria-hidden=\"true\"></i>\n                  <span>Dashboard</span>\n                </button>\n              </li>\n              <li class=\"mobile-nav-item\" role=\"none\">\n                <button\n                  class=\"mobile-nav-link\"\n                  :class=\"{ active: activeMenu === 'services' }\"\n                  @click=\"handleMobileMenuAction('services')\"\n                  role=\"menuitem\"\n                  type=\"button\"\n                >\n                  <i class=\"fas fa-file-alt\" aria-hidden=\"true\"></i>\n                  <span>Services</span>\n                </button>\n              </li>\n              <li class=\"mobile-nav-item\" role=\"none\">\n                <button\n                  class=\"mobile-nav-link\"\n                  :class=\"{ active: activeMenu === 'requests' }\"\n                  @click=\"handleMobileMenuAction('requests')\"\n                  role=\"menuitem\"\n                  type=\"button\"\n                >\n                  <i class=\"fas fa-clock\" aria-hidden=\"true\"></i>\n                  <span>My Requests</span>\n                </button>\n              </li>\n              <li class=\"mobile-nav-item\" role=\"none\">\n                <button\n                  class=\"mobile-nav-link\"\n                  :class=\"{ active: activeMenu === 'help' }\"\n                  @click=\"handleMobileMenuAction('help')\"\n                  role=\"menuitem\"\n                  type=\"button\"\n                >\n                  <i class=\"fas fa-question-circle\" aria-hidden=\"true\"></i>\n                  <span>Help</span>\n                </button>\n              </li>\n            </ul>\n          </nav>\n        </div>\n      </div>\n    </Transition>\n\n    <!-- Breadcrumb Navigation -->\n    <div v-if=\"showBreadcrumbs\" class=\"breadcrumb-section\" role=\"navigation\" aria-label=\"Breadcrumb\">\n      <div class=\"header-container\">\n        <nav class=\"breadcrumb-nav\">\n          <ol class=\"breadcrumb-list\">\n            <li class=\"breadcrumb-item\">\n              <button\n                class=\"breadcrumb-link\"\n                @click=\"handleMenuAction('dashboard')\"\n                type=\"button\"\n              >\n                <i class=\"fas fa-home\" aria-hidden=\"true\"></i>\n                <span>Dashboard</span>\n              </button>\n            </li>\n            <li class=\"breadcrumb-item active\" aria-current=\"page\">\n              <span>{{ getPageTitle() }}</span>\n            </li>\n          </ol>\n        </nav>\n      </div>\n    </div>\n  </header>\n</template>\n\n<script setup>\nimport { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport ClientNotifications from './ClientNotifications.vue'\n\n// Props\nconst props = defineProps({\n  userName: {\n    type: String,\n    default: 'User'\n  },\n  userEmail: {\n    type: String,\n    default: '<EMAIL>'\n  },\n  userAvatar: {\n    type: String,\n    default: null\n  },\n  showUserDropdown: {\n    type: Boolean,\n    default: false\n  },\n  activeMenu: {\n    type: String,\n    default: 'dashboard'\n  },\n  showBreadcrumbs: {\n    type: Boolean,\n    default: true\n  }\n})\n\n// Emits\nconst emit = defineEmits([\n  'user-dropdown-toggle',\n  'menu-action',\n  'logout',\n  'error',\n  'search',\n  'notification-click'\n])\n\n// Router\nconst router = useRouter()\n\n// Reactive state\nconst showSearch = ref(false)\nconst showMobileMenu = ref(false)\nconst searchQuery = ref('')\nconst searchInput = ref(null)\nconst userProfileRef = ref(null)\n\n// Computed\nconst pageTitle = computed(() => {\n  const titles = {\n    'dashboard': 'Dashboard',\n    'services': 'Document Services',\n    'requests': 'My Requests',\n    'documents': 'My Documents',\n    'profile': 'My Profile',\n    'settings': 'Account Settings',\n    'history': 'Request History',\n    'notifications': 'Notifications',\n    'help': 'Help & Support'\n  }\n  return titles[props.activeMenu] || 'Dashboard'\n})\n\n// Methods\nconst getPageTitle = () => pageTitle.value\n\nconst toggleSearch = async () => {\n  showSearch.value = !showSearch.value\n  if (showSearch.value) {\n    await nextTick()\n    searchInput.value?.focus()\n  }\n}\n\nconst closeSearch = () => {\n  showSearch.value = false\n  searchQuery.value = ''\n}\n\nconst performSearch = () => {\n  if (searchQuery.value.trim()) {\n    emit('search', searchQuery.value.trim())\n    // Close search on mobile after search\n    if (window.innerWidth <= 768) {\n      closeSearch()\n    }\n  }\n}\n\nconst toggleUserDropdown = () => {\n  emit('user-dropdown-toggle')\n}\n\nconst toggleMobileMenu = () => {\n  showMobileMenu.value = !showMobileMenu.value\n  // Prevent body scroll when mobile menu is open\n  document.body.style.overflow = showMobileMenu.value ? 'hidden' : ''\n}\n\nconst handleMenuAction = (action) => {\n  emit('menu-action', action)\n  // Close mobile menu if open\n  if (showMobileMenu.value) {\n    toggleMobileMenu()\n  }\n}\n\nconst handleMobileMenuAction = (action) => {\n  handleMenuAction(action)\n}\n\nconst handleLogout = () => {\n  emit('logout')\n}\n\nconst handleOutsideClick = (event) => {\n  // Check if click is outside user dropdown\n  if (userProfileRef.value && !userProfileRef.value.contains(event.target)) {\n    if (props.showUserDropdown) {\n      toggleUserDropdown()\n    }\n  }\n\n  // Check if click is outside search\n  if (!event.target.closest('.search-container')) {\n    showSearch.value = false\n  }\n\n  // Check if click is outside mobile menu\n  if (!event.target.closest('.mobile-nav') && !event.target.closest('.mobile-menu-toggle')) {\n    if (showMobileMenu.value) {\n      toggleMobileMenu()\n    }\n  }\n}\n\nconst handleKeydown = (event) => {\n  // Close dropdowns on Escape key\n  if (event.key === 'Escape') {\n    if (showSearch.value) {\n      closeSearch()\n    }\n    if (props.showUserDropdown) {\n      toggleUserDropdown()\n    }\n    if (showMobileMenu.value) {\n      toggleMobileMenu()\n    }\n  }\n}\n\n// Notification event handlers\nconst handleNewNotification = (notification) => {\n  console.log('New notification received:', notification)\n  // Handle new notification - could show toast, update UI, etc.\n}\n\nconst handleNotificationClick = async (notification) => {\n  console.log('🔔 ClientHeader: Notification clicked:', notification)\n\n  // Ensure we have a valid notification object\n  if (!notification || typeof notification !== 'object') {\n    console.error('Invalid notification object received:', notification)\n    return\n  }\n\n  try {\n    // Parse notification data\n    const notificationData = typeof notification.data === 'string'\n      ? JSON.parse(notification.data)\n      : notification.data || {}\n\n    // Log navigation for debugging\n    console.log('📊 ClientHeader: Notification data:', notificationData)\n\n    // Additional header-specific logic can go here\n    // For example, updating header state, showing badges, etc.\n\n  } catch (error) {\n    console.error('❌ ClientHeader: Error handling notification click:', error)\n    emit('error', error)\n  }\n\n  // Always emit the event for other components that might need it\n  emit('notification-click', notification)\n}\n\nconst handleNotificationError = (error) => {\n  console.error('Notification error:', error)\n  emit('error', error)\n}\n\n// Lifecycle hooks\nonMounted(() => {\n  document.addEventListener('click', handleOutsideClick)\n  document.addEventListener('keydown', handleKeydown)\n})\n\nonUnmounted(() => {\n  document.removeEventListener('click', handleOutsideClick)\n  document.removeEventListener('keydown', handleKeydown)\n  // Reset body overflow\n  document.body.style.overflow = ''\n})\n</script>\n\n<style scoped src=\"./css/clientHeader.css\"></style>\n"], "mappings": "AAuXA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,KAAI;AACpE,SAASC,SAAS,QAAQ,YAAW;AACrC,OAAOC,mBAAmB,MAAM,2BAA0B;;AAE1D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IACA,MAAMC,KAAK,GAAGC,OAAA;;IA2Bd;IACA,MAAMC,IAAI,GAAGC,MAAA;;IASb;IACA,MAAMC,MAAM,GAAGN,SAAS,CAAC;;IAEzB;IACA,MAAMO,UAAU,GAAGZ,GAAG,CAAC,KAAK;IAC5B,MAAMa,cAAc,GAAGb,GAAG,CAAC,KAAK;IAChC,MAAMc,WAAW,GAAGd,GAAG,CAAC,EAAE;IAC1B,MAAMe,WAAW,GAAGf,GAAG,CAAC,IAAI;IAC5B,MAAMgB,cAAc,GAAGhB,GAAG,CAAC,IAAI;;IAE/B;IACA,MAAMiB,SAAS,GAAGhB,QAAQ,CAAC,MAAM;MAC/B,MAAMiB,MAAM,GAAG;QACb,WAAW,EAAE,WAAW;QACxB,UAAU,EAAE,mBAAmB;QAC/B,UAAU,EAAE,aAAa;QACzB,WAAW,EAAE,cAAc;QAC3B,SAAS,EAAE,YAAY;QACvB,UAAU,EAAE,kBAAkB;QAC9B,SAAS,EAAE,iBAAiB;QAC5B,eAAe,EAAE,eAAe;QAChC,MAAM,EAAE;MACV;MACA,OAAOA,MAAM,CAACX,KAAK,CAACY,UAAU,CAAC,IAAI,WAAU;IAC/C,CAAC;;IAED;IACA,MAAMC,YAAY,GAAGA,CAAA,KAAMH,SAAS,CAACI,KAAI;IAEzC,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/BV,UAAU,CAACS,KAAK,GAAG,CAACT,UAAU,CAACS,KAAI;MACnC,IAAIT,UAAU,CAACS,KAAK,EAAE;QACpB,MAAMnB,QAAQ,CAAC;QACfa,WAAW,CAACM,KAAK,EAAEE,KAAK,CAAC;MAC3B;IACF;IAEA,MAAMC,WAAW,GAAGA,CAAA,KAAM;MACxBZ,UAAU,CAACS,KAAK,GAAG,KAAI;MACvBP,WAAW,CAACO,KAAK,GAAG,EAAC;IACvB;IAEA,MAAMI,aAAa,GAAGA,CAAA,KAAM;MAC1B,IAAIX,WAAW,CAACO,KAAK,CAACK,IAAI,CAAC,CAAC,EAAE;QAC5BjB,IAAI,CAAC,QAAQ,EAAEK,WAAW,CAACO,KAAK,CAACK,IAAI,CAAC,CAAC;QACvC;QACA,IAAIC,MAAM,CAACC,UAAU,IAAI,GAAG,EAAE;UAC5BJ,WAAW,CAAC;QACd;MACF;IACF;IAEA,MAAMK,kBAAkB,GAAGA,CAAA,KAAM;MAC/BpB,IAAI,CAAC,sBAAsB;IAC7B;IAEA,MAAMqB,gBAAgB,GAAGA,CAAA,KAAM;MAC7BjB,cAAc,CAACQ,KAAK,GAAG,CAACR,cAAc,CAACQ,KAAI;MAC3C;MACAU,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAGrB,cAAc,CAACQ,KAAK,GAAG,QAAQ,GAAG,EAAC;IACpE;IAEA,MAAMc,gBAAgB,GAAIC,MAAM,IAAK;MACnC3B,IAAI,CAAC,aAAa,EAAE2B,MAAM;MAC1B;MACA,IAAIvB,cAAc,CAACQ,KAAK,EAAE;QACxBS,gBAAgB,CAAC;MACnB;IACF;IAEA,MAAMO,sBAAsB,GAAID,MAAM,IAAK;MACzCD,gBAAgB,CAACC,MAAM;IACzB;IAEA,MAAME,YAAY,GAAGA,CAAA,KAAM;MACzB7B,IAAI,CAAC,QAAQ;IACf;IAEA,MAAM8B,kBAAkB,GAAIC,KAAK,IAAK;MACpC;MACA,IAAIxB,cAAc,CAACK,KAAK,IAAI,CAACL,cAAc,CAACK,KAAK,CAACoB,QAAQ,CAACD,KAAK,CAACE,MAAM,CAAC,EAAE;QACxE,IAAInC,KAAK,CAACoC,gBAAgB,EAAE;UAC1Bd,kBAAkB,CAAC;QACrB;MACF;;MAEA;MACA,IAAI,CAACW,KAAK,CAACE,MAAM,CAACE,OAAO,CAAC,mBAAmB,CAAC,EAAE;QAC9ChC,UAAU,CAACS,KAAK,GAAG,KAAI;MACzB;;MAEA;MACA,IAAI,CAACmB,KAAK,CAACE,MAAM,CAACE,OAAO,CAAC,aAAa,CAAC,IAAI,CAACJ,KAAK,CAACE,MAAM,CAACE,OAAO,CAAC,qBAAqB,CAAC,EAAE;QACxF,IAAI/B,cAAc,CAACQ,KAAK,EAAE;UACxBS,gBAAgB,CAAC;QACnB;MACF;IACF;IAEA,MAAMe,aAAa,GAAIL,KAAK,IAAK;MAC/B;MACA,IAAIA,KAAK,CAACM,GAAG,KAAK,QAAQ,EAAE;QAC1B,IAAIlC,UAAU,CAACS,KAAK,EAAE;UACpBG,WAAW,CAAC;QACd;QACA,IAAIjB,KAAK,CAACoC,gBAAgB,EAAE;UAC1Bd,kBAAkB,CAAC;QACrB;QACA,IAAIhB,cAAc,CAACQ,KAAK,EAAE;UACxBS,gBAAgB,CAAC;QACnB;MACF;IACF;;IAEA;IACA,MAAMiB,qBAAqB,GAAIC,YAAY,IAAK;MAC9CC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEF,YAAY;MACtD;IACF;IAEA,MAAMG,uBAAuB,GAAG,MAAOH,YAAY,IAAK;MACtDC,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEF,YAAY;;MAElE;MACA,IAAI,CAACA,YAAY,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;QACrDC,OAAO,CAACG,KAAK,CAAC,uCAAuC,EAAEJ,YAAY;QACnE;MACF;MAEA,IAAI;QACF;QACA,MAAMK,gBAAgB,GAAG,OAAOL,YAAY,CAACM,IAAI,KAAK,QAAO,GACzDC,IAAI,CAACC,KAAK,CAACR,YAAY,CAACM,IAAI,IAC5BN,YAAY,CAACM,IAAI,IAAI,CAAC;;QAE1B;QACAL,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEG,gBAAgB;;QAEnE;QACA;MAEF,CAAC,CAAC,OAAOD,KAAK,EAAE;QACdH,OAAO,CAACG,KAAK,CAAC,oDAAoD,EAAEA,KAAK;QACzE3C,IAAI,CAAC,OAAO,EAAE2C,KAAK;MACrB;;MAEA;MACA3C,IAAI,CAAC,oBAAoB,EAAEuC,YAAY;IACzC;IAEA,MAAMS,uBAAuB,GAAIL,KAAK,IAAK;MACzCH,OAAO,CAACG,KAAK,CAAC,qBAAqB,EAAEA,KAAK;MAC1C3C,IAAI,CAAC,OAAO,EAAE2C,KAAK;IACrB;;IAEA;IACAjD,SAAS,CAAC,MAAM;MACd4B,QAAQ,CAAC2B,gBAAgB,CAAC,OAAO,EAAEnB,kBAAkB;MACrDR,QAAQ,CAAC2B,gBAAgB,CAAC,SAAS,EAAEb,aAAa;IACpD,CAAC;IAEDzC,WAAW,CAAC,MAAM;MAChB2B,QAAQ,CAAC4B,mBAAmB,CAAC,OAAO,EAAEpB,kBAAkB;MACxDR,QAAQ,CAAC4B,mBAAmB,CAAC,SAAS,EAAEd,aAAa;MACrD;MACAd,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,EAAC;IAClC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}