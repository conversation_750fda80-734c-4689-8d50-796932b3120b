{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode, withModifiers as _withModifiers, createStaticVNode as _createStaticVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"welcome-page\"\n};\nconst _hoisted_2 = {\n  class: \"content-container\"\n};\nconst _hoisted_3 = {\n  class: \"welcome-header\"\n};\nconst _hoisted_4 = {\n  class: \"logo-container\"\n};\nconst _hoisted_5 = [\"src\"];\nconst _hoisted_6 = {\n  class: \"cta-section\"\n};\nconst _hoisted_7 = {\n  class: \"cta-content\"\n};\nconst _hoisted_8 = [\"disabled\"];\nconst _hoisted_9 = {\n  key: 0\n};\nconst _hoisted_10 = {\n  key: 1\n};\nconst _hoisted_11 = {\n  class: \"register-link\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" Background Image \"), _cache[11] || (_cache[11] = _createElementVNode(\"div\", {\n    class: \"background-container\"\n  }, [_createElementVNode(\"div\", {\n    class: \"background-image\"\n  }), _createElementVNode(\"div\", {\n    class: \"background-overlay\"\n  })], -1 /* HOISTED */)), _createCommentVNode(\" Main Content \"), _createElementVNode(\"div\", _hoisted_2, [_createCommentVNode(\" Header Section \"), _createElementVNode(\"header\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"img\", {\n    src: $data.logoImage,\n    alt: \"Barangay Bula Official Seal\",\n    class: \"logo\"\n  }, null, 8 /* PROPS */, _hoisted_5)]), _cache[2] || (_cache[2] = _createElementVNode(\"div\", {\n    class: \"title-section\"\n  }, [_createElementVNode(\"h1\", {\n    class: \"main-title\"\n  }, \" Welcome to Barangay Bula \"), _createElementVNode(\"h2\", {\n    class: \"subtitle\"\n  }, \" Online Document Request System \"), _createElementVNode(\"p\", {\n    class: \"tagline\"\n  }, \" General Santos City • Serving our Community with Excellence \")], -1 /* HOISTED */))]), _createCommentVNode(\" Information Section \"), _cache[8] || (_cache[8] = _createStaticVNode(\"<section class=\\\"info-section\\\" data-v-958982de><div class=\\\"info-grid\\\" data-v-958982de><div class=\\\"info-card\\\" data-v-958982de><div class=\\\"info-icon\\\" data-v-958982de><i class=\\\"fas fa-history\\\" data-v-958982de></i></div><h3 data-v-958982de>Our Heritage</h3><p data-v-958982de> Established in 1959, Barangay Bula derives its name from the bubbling waters rich with marine life that early settlers discovered. We are a thriving coastal community 3.5 kilometers from General Santos City proper. </p></div><div class=\\\"info-card\\\" data-v-958982de><div class=\\\"info-icon\\\" data-v-958982de><i class=\\\"fas fa-users\\\" data-v-958982de></i></div><h3 data-v-958982de>Our Community</h3><p data-v-958982de> Home to over 34,000 residents across 25 puroks, we are a diverse community of fishing families, traders, and industry workers who have built one of General Santos City&#39;s most productive barangays. </p></div><div class=\\\"info-card\\\" data-v-958982de><div class=\\\"info-icon\\\" data-v-958982de><i class=\\\"fas fa-file-alt\\\" data-v-958982de></i></div><h3 data-v-958982de>Digital Services</h3><p data-v-958982de> Request official documents online with ease. Our digital platform provides convenient access to barangay clearances, cedulas, and other essential documents from the comfort of your home. </p></div></div></section>\", 1)), _createCommentVNode(\" Services Preview \"), _cache[9] || (_cache[9] = _createStaticVNode(\"<section class=\\\"services-preview\\\" data-v-958982de><h3 class=\\\"services-title\\\" data-v-958982de>Available Services</h3><div class=\\\"services-grid\\\" data-v-958982de><div class=\\\"service-item\\\" data-v-958982de><i class=\\\"fas fa-certificate\\\" data-v-958982de></i><span data-v-958982de>Barangay Clearance</span></div><div class=\\\"service-item\\\" data-v-958982de><i class=\\\"fas fa-id-card\\\" data-v-958982de></i><span data-v-958982de>Cedula</span></div><div class=\\\"service-item\\\" data-v-958982de><i class=\\\"fas fa-file-contract\\\" data-v-958982de></i><span data-v-958982de>Business Permits</span></div><div class=\\\"service-item\\\" data-v-958982de><i class=\\\"fas fa-home\\\" data-v-958982de></i><span data-v-958982de>Residency Certificates</span></div></div></section>\", 1)), _createCommentVNode(\" Call to Action \"), _createElementVNode(\"section\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_cache[6] || (_cache[6] = _createElementVNode(\"h3\", {\n    class: \"cta-title\"\n  }, \"Ready to Get Started?\", -1 /* HOISTED */)), _cache[7] || (_cache[7] = _createElementVNode(\"p\", {\n    class: \"cta-description\"\n  }, \" Access our secure portal to request documents, track applications, and manage your barangay services online. \", -1 /* HOISTED */)), _createElementVNode(\"button\", {\n    class: \"login-button\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.goToLogin && $options.goToLogin(...args)),\n    disabled: $data.loading\n  }, [_cache[4] || (_cache[4] = _createElementVNode(\"i\", {\n    class: \"fas fa-sign-in-alt\"\n  }, null, -1 /* HOISTED */)), !$data.loading ? (_openBlock(), _createElementBlock(\"span\", _hoisted_9, \"Access Portal\")) : (_openBlock(), _createElementBlock(\"span\", _hoisted_10, _cache[3] || (_cache[3] = [_createElementVNode(\"i\", {\n    class: \"fas fa-spinner fa-spin\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Loading... \")])))], 8 /* PROPS */, _hoisted_8), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"p\", null, [_cache[5] || (_cache[5] = _createTextVNode(\" Don't have an account? \")), _createElementVNode(\"a\", {\n    href: \"#\",\n    onClick: _cache[1] || (_cache[1] = _withModifiers((...args) => $options.goToRegister && $options.goToRegister(...args), [\"prevent\"])),\n    class: \"register-btn\"\n  }, \" Register here \")])])])]), _createCommentVNode(\" Footer \"), _cache[10] || (_cache[10] = _createStaticVNode(\"<footer class=\\\"welcome-footer\\\" data-v-958982de><div class=\\\"footer-content\\\" data-v-958982de><div class=\\\"contact-info\\\" data-v-958982de><h4 data-v-958982de>Contact Information</h4><div class=\\\"contact-item\\\" data-v-958982de><i class=\\\"fas fa-map-marker-alt\\\" data-v-958982de></i><span data-v-958982de>Barangay Bula, General Santos City, South Cotabato</span></div><div class=\\\"contact-item\\\" data-v-958982de><i class=\\\"fas fa-phone\\\" data-v-958982de></i><span data-v-958982de>Contact your Barangay Office</span></div></div><div class=\\\"office-hours\\\" data-v-958982de><h4 data-v-958982de>Office Hours</h4><p data-v-958982de>Monday - Friday: 8:00 AM - 5:00 PM</p><p data-v-958982de>Saturday: 8:00 AM - 12:00 PM</p><p data-v-958982de>Sunday: Closed</p></div></div><div class=\\\"footer-bottom\\\" data-v-958982de><p data-v-958982de>© 2024 Barangay Bula. All rights reserved.</p><p class=\\\"powered-by\\\" data-v-958982de>Powered by Barangay Online System for Document Requests</p></div></footer>\", 1))])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "src", "$data", "logoImage", "alt", "_hoisted_6", "_hoisted_7", "onClick", "_cache", "args", "$options", "goToLogin", "disabled", "loading", "_hoisted_9", "_hoisted_10", "_hoisted_11", "href", "_withModifiers", "goToRegister"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\WelcomePage.vue"], "sourcesContent": ["<template>\n  <div class=\"welcome-page\">\n    <!-- Background Image -->\n    <div class=\"background-container\">\n      <div class=\"background-image\"></div>\n      <div class=\"background-overlay\"></div>\n    </div>\n\n    <!-- Main Content -->\n    <div class=\"content-container\">\n      <!-- Header Section -->\n      <header class=\"welcome-header\">\n        <div class=\"logo-container\">\n          <img\n            :src=\"logoImage\"\n            alt=\"Barangay Bula Official Seal\"\n            class=\"logo\"\n          />\n        </div>\n        \n        <div class=\"title-section\">\n          <h1 class=\"main-title\">\n            Welcome to Barangay Bula\n          </h1>\n          <h2 class=\"subtitle\">\n            Online Document Request System\n          </h2>\n          <p class=\"tagline\">\n            General Santos City • Serving our Community with Excellence\n          </p>\n        </div>\n      </header>\n\n      <!-- Information Section -->\n      <section class=\"info-section\">\n        <div class=\"info-grid\">\n          <div class=\"info-card\">\n            <div class=\"info-icon\">\n              <i class=\"fas fa-history\"></i>\n            </div>\n            <h3>Our Heritage</h3>\n            <p>\n              Established in 1959, Barangay Bula derives its name from the bubbling waters \n              rich with marine life that early settlers discovered. We are a thriving coastal \n              community 3.5 kilometers from General Santos City proper.\n            </p>\n          </div>\n\n          <div class=\"info-card\">\n            <div class=\"info-icon\">\n              <i class=\"fas fa-users\"></i>\n            </div>\n            <h3>Our Community</h3>\n            <p>\n              Home to over 34,000 residents across 25 puroks, we are a diverse community \n              of fishing families, traders, and industry workers who have built one of \n              General Santos City's most productive barangays.\n            </p>\n          </div>\n\n          <div class=\"info-card\">\n            <div class=\"info-icon\">\n              <i class=\"fas fa-file-alt\"></i>\n            </div>\n            <h3>Digital Services</h3>\n            <p>\n              Request official documents online with ease. Our digital platform provides \n              convenient access to barangay clearances, cedulas, and other essential \n              documents from the comfort of your home.\n            </p>\n          </div>\n        </div>\n      </section>\n\n      <!-- Services Preview -->\n      <section class=\"services-preview\">\n        <h3 class=\"services-title\">Available Services</h3>\n        <div class=\"services-grid\">\n          <div class=\"service-item\">\n            <i class=\"fas fa-certificate\"></i>\n            <span>Barangay Clearance</span>\n          </div>\n          <div class=\"service-item\">\n            <i class=\"fas fa-id-card\"></i>\n            <span>Cedula</span>\n          </div>\n          <div class=\"service-item\">\n            <i class=\"fas fa-file-contract\"></i>\n            <span>Business Permits</span>\n          </div>\n          <div class=\"service-item\">\n            <i class=\"fas fa-home\"></i>\n            <span>Residency Certificates</span>\n          </div>\n        </div>\n      </section>\n\n      <!-- Call to Action -->\n      <section class=\"cta-section\">\n        <div class=\"cta-content\">\n          <h3 class=\"cta-title\">Ready to Get Started?</h3>\n          <p class=\"cta-description\">\n            Access our secure portal to request documents, track applications, \n            and manage your barangay services online.\n          </p>\n          <button \n            class=\"login-button\"\n            @click=\"goToLogin\"\n            :disabled=\"loading\"\n          >\n            <i class=\"fas fa-sign-in-alt\"></i>\n            <span v-if=\"!loading\">Access Portal</span>\n            <span v-else>\n              <i class=\"fas fa-spinner fa-spin\"></i>\n              Loading...\n            </span>\n          </button>\n          \n          <div class=\"register-link\">\n            <p>\n              Don't have an account? \n              <a href=\"#\" @click.prevent=\"goToRegister\" class=\"register-btn\">\n                Register here\n              </a>\n            </p>\n          </div>\n        </div>\n      </section>\n\n      <!-- Footer -->\n      <footer class=\"welcome-footer\">\n        <div class=\"footer-content\">\n          <div class=\"contact-info\">\n            <h4>Contact Information</h4>\n            <div class=\"contact-item\">\n              <i class=\"fas fa-map-marker-alt\"></i>\n              <span>Barangay Bula, General Santos City, South Cotabato</span>\n            </div>\n            <div class=\"contact-item\">\n              <i class=\"fas fa-phone\"></i>\n              <span>Contact your Barangay Office</span>\n            </div>\n          </div>\n          \n          <div class=\"office-hours\">\n            <h4>Office Hours</h4>\n            <p>Monday - Friday: 8:00 AM - 5:00 PM</p>\n            <p>Saturday: 8:00 AM - 12:00 PM</p>\n            <p>Sunday: Closed</p>\n          </div>\n        </div>\n        \n        <div class=\"footer-bottom\">\n          <p>&copy; 2024 Barangay Bula. All rights reserved.</p>\n          <p class=\"powered-by\">Powered by Barangay Online System for Document Requests</p>\n        </div>\n      </footer>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'WelcomePage',\n  data() {\n    return {\n      loading: false,\n      backgroundImage: require('@/assets/bula-welcome-page-background-pic.JPG'),\n      logoImage: require('@/assets/icon-of-bula.jpg')\n    };\n  },\n  methods: {\n    async goToLogin() {\n      this.loading = true;\n      try {\n        // Add a small delay for better UX\n        await new Promise(resolve => setTimeout(resolve, 500));\n        this.$router.push({ name: 'UnifiedLogin' });\n      } catch (error) {\n        console.error('Navigation error:', error);\n        // Fallback navigation\n        window.location.href = '/login';\n      } finally {\n        this.loading = false;\n      }\n    },\n    \n    goToRegister() {\n      this.$router.push({ name: 'ClientRegistration' });\n    }\n  }\n};\n</script>\n\n<style scoped>\n.welcome-page {\n  position: relative;\n  min-height: 100vh;\n  overflow-x: hidden;\n}\n\n/* Background Setup */\n.background-container {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: -2;\n}\n\n.background-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  object-position: center;\n}\n\n.background-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    135deg,\n    rgba(0, 123, 191, 0.85) 0%,\n    rgba(0, 86, 134, 0.9) 50%,\n    rgba(26, 54, 93, 0.95) 100%\n  );\n  z-index: -1;\n}\n\n/* Content Container */\n.content-container {\n  position: relative;\n  z-index: 1;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  padding: 2rem 1rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n/* Header Section */\n.welcome-header {\n  text-align: center;\n  margin-bottom: 3rem;\n  padding: 2rem 0;\n}\n\n.logo-container {\n  margin-bottom: 2rem;\n}\n\n.logo {\n  width: 120px;\n  height: 120px;\n  border-radius: 50%;\n  border: 4px solid rgba(255, 255, 255, 0.9);\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n  transition: transform 0.3s ease;\n}\n\n.logo:hover {\n  transform: scale(1.05);\n}\n\n.title-section {\n  color: white;\n}\n\n.main-title {\n  font-size: clamp(2.5rem, 6vw, 4rem);\n  font-weight: 700;\n  margin-bottom: 1rem;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n  line-height: 1.2;\n}\n\n.subtitle {\n  font-size: clamp(1.25rem, 3vw, 2rem);\n  font-weight: 400;\n  margin-bottom: 1rem;\n  color: rgba(255, 255, 255, 0.95);\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\n}\n\n.tagline {\n  font-size: clamp(1rem, 2.5vw, 1.25rem);\n  color: rgba(255, 255, 255, 0.9);\n  font-weight: 300;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\n}\n\n/* Information Section */\n.info-section {\n  margin-bottom: 3rem;\n}\n\n.info-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 2rem;\n  margin-bottom: 2rem;\n}\n\n.info-card {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: 1rem;\n  padding: 2rem;\n  text-align: center;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.info-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\n}\n\n.info-icon {\n  width: 4rem;\n  height: 4rem;\n  background: linear-gradient(135deg, #007bbf, #005a86);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 0 auto 1.5rem;\n  color: white;\n  font-size: 1.5rem;\n}\n\n.info-card h3 {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 1rem;\n}\n\n.info-card p {\n  color: #4a5568;\n  line-height: 1.6;\n  font-size: 1rem;\n}\n\n/* Services Preview */\n.services-preview {\n  margin-bottom: 3rem;\n  text-align: center;\n}\n\n.services-title {\n  font-size: clamp(1.75rem, 4vw, 2.5rem);\n  font-weight: 600;\n  color: white;\n  margin-bottom: 2rem;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n}\n\n.services-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1.5rem;\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.service-item {\n  background: rgba(255, 255, 255, 0.9);\n  backdrop-filter: blur(10px);\n  border-radius: 0.75rem;\n  padding: 1.5rem;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 1rem;\n  transition: transform 0.3s ease, background 0.3s ease;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.service-item:hover {\n  transform: translateY(-3px);\n  background: rgba(255, 255, 255, 0.95);\n}\n\n.service-item i {\n  font-size: 2rem;\n  color: #007bbf;\n}\n\n.service-item span {\n  font-weight: 500;\n  color: #1a365d;\n  text-align: center;\n}\n\n/* Call to Action Section */\n.cta-section {\n  margin-bottom: 3rem;\n  text-align: center;\n}\n\n.cta-content {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: 1.5rem;\n  padding: 3rem 2rem;\n  max-width: 600px;\n  margin: 0 auto;\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.cta-title {\n  font-size: clamp(1.5rem, 3vw, 2rem);\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 1rem;\n}\n\n.cta-description {\n  color: #4a5568;\n  font-size: 1.1rem;\n  line-height: 1.6;\n  margin-bottom: 2rem;\n}\n\n.login-button {\n  background: linear-gradient(135deg, #007bbf, #005a86);\n  color: white;\n  border: none;\n  padding: 1rem 2.5rem;\n  border-radius: 0.75rem;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: inline-flex;\n  align-items: center;\n  gap: 0.75rem;\n  box-shadow: 0 4px 15px rgba(0, 123, 191, 0.3);\n  min-width: 200px;\n  justify-content: center;\n}\n\n.login-button:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(0, 123, 191, 0.4);\n  background: linear-gradient(135deg, #0056b3, #004a73);\n}\n\n.login-button:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n  transform: none;\n}\n\n.register-link {\n  margin-top: 1.5rem;\n}\n\n.register-link p {\n  color: #4a5568;\n  font-size: 1rem;\n}\n\n.register-btn {\n  color: #007bbf;\n  text-decoration: none;\n  font-weight: 500;\n  transition: color 0.3s ease;\n}\n\n.register-btn:hover {\n  color: #005a86;\n  text-decoration: underline;\n}\n\n/* Footer */\n.welcome-footer {\n  margin-top: auto;\n  background: rgba(26, 54, 93, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: 1rem 1rem 0 0;\n  padding: 2rem;\n  color: white;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.footer-content {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 2rem;\n  margin-bottom: 2rem;\n}\n\n.contact-info h4,\n.office-hours h4 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  margin-bottom: 1rem;\n  color: rgba(255, 255, 255, 0.95);\n}\n\n.contact-item {\n  display: flex;\n  align-items: flex-start;\n  gap: 0.75rem;\n  margin-bottom: 0.75rem;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.contact-item i {\n  color: #4fc3f7;\n  margin-top: 0.125rem;\n  flex-shrink: 0;\n}\n\n.office-hours p {\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 0.5rem;\n  line-height: 1.5;\n}\n\n.footer-bottom {\n  border-top: 1px solid rgba(255, 255, 255, 0.2);\n  padding-top: 1.5rem;\n  text-align: center;\n}\n\n.footer-bottom p {\n  color: rgba(255, 255, 255, 0.8);\n  margin-bottom: 0.5rem;\n}\n\n.powered-by {\n  font-size: 0.9rem;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .content-container {\n    padding: 1rem 0.75rem;\n  }\n\n  .welcome-header {\n    margin-bottom: 2rem;\n    padding: 1rem 0;\n  }\n\n  .logo {\n    width: 100px;\n    height: 100px;\n  }\n\n  .info-grid {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n\n  .info-card {\n    padding: 1.5rem;\n  }\n\n  .services-grid {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 1rem;\n  }\n\n  .service-item {\n    padding: 1rem;\n  }\n\n  .cta-content {\n    padding: 2rem 1.5rem;\n    margin: 0 1rem;\n  }\n\n  .login-button {\n    padding: 0.875rem 2rem;\n    font-size: 1rem;\n    min-width: 180px;\n  }\n\n  .welcome-footer {\n    padding: 1.5rem;\n    border-radius: 0;\n  }\n\n  .footer-content {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n    text-align: center;\n  }\n}\n\n@media (max-width: 480px) {\n  .content-container {\n    padding: 0.75rem 0.5rem;\n  }\n\n  .info-card {\n    padding: 1.25rem;\n  }\n\n  .services-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .cta-content {\n    margin: 0 0.5rem;\n    padding: 1.5rem 1rem;\n  }\n\n  .login-button {\n    width: 100%;\n    max-width: 280px;\n  }\n}\n\n/* Animation for smooth entrance */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.welcome-header,\n.info-section,\n.services-preview,\n.cta-section,\n.welcome-footer {\n  animation: fadeInUp 0.8s ease-out;\n}\n\n.info-section {\n  animation-delay: 0.2s;\n}\n\n.services-preview {\n  animation-delay: 0.4s;\n}\n\n.cta-section {\n  animation-delay: 0.6s;\n}\n\n.welcome-footer {\n  animation-delay: 0.8s;\n}\n\n/* Accessibility improvements */\n@media (prefers-reduced-motion: reduce) {\n  .welcome-header,\n  .info-section,\n  .services-preview,\n  .cta-section,\n  .welcome-footer {\n    animation: none;\n  }\n\n  .logo:hover,\n  .info-card:hover,\n  .service-item:hover,\n  .login-button:hover:not(:disabled) {\n    transform: none;\n  }\n}\n\n/* High contrast mode support */\n@media (prefers-contrast: high) {\n  .background-overlay {\n    background: rgba(0, 0, 0, 0.8);\n  }\n\n  .info-card,\n  .service-item,\n  .cta-content {\n    background: rgba(255, 255, 255, 1);\n    border: 2px solid #000;\n  }\n\n  .welcome-footer {\n    background: rgba(0, 0, 0, 0.95);\n    border: 2px solid #fff;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAc;;EAQlBA,KAAK,EAAC;AAAmB;;EAEpBA,KAAK,EAAC;AAAgB;;EACvBA,KAAK,EAAC;AAAgB;;;EAsFpBA,KAAK,EAAC;AAAa;;EACrBA,KAAK,EAAC;AAAa;;;;;;;;;EAmBjBA,KAAK,EAAC;AAAe;;uBArHlCC,mBAAA,CA6JM,OA7JNC,UA6JM,GA5JJC,mBAAA,sBAAyB,E,4BACzBC,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAsB,IAC/BI,mBAAA,CAAoC;IAA/BJ,KAAK,EAAC;EAAkB,IAC7BI,mBAAA,CAAsC;IAAjCJ,KAAK,EAAC;EAAoB,G,sBAGjCG,mBAAA,kBAAqB,EACrBC,mBAAA,CAoJM,OApJNC,UAoJM,GAnJJF,mBAAA,oBAAuB,EACvBC,mBAAA,CAoBS,UApBTE,UAoBS,GAnBPF,mBAAA,CAMM,OANNG,UAMM,GALJH,mBAAA,CAIE;IAHCI,GAAG,EAAEC,KAAA,CAAAC,SAAS;IACfC,GAAG,EAAC,6BAA6B;IACjCX,KAAK,EAAC;mEAIVI,mBAAA,CAUM;IAVDJ,KAAK,EAAC;EAAe,IACxBI,mBAAA,CAEK;IAFDJ,KAAK,EAAC;EAAY,GAAC,4BAEvB,GACAI,mBAAA,CAEK;IAFDJ,KAAK,EAAC;EAAU,GAAC,kCAErB,GACAI,mBAAA,CAEI;IAFDJ,KAAK,EAAC;EAAS,GAAC,+DAEnB,E,wBAIJG,mBAAA,yBAA4B,E,+1CAyC5BA,mBAAA,sBAAyB,E,4yBAuBzBA,mBAAA,oBAAuB,EACvBC,mBAAA,CA6BU,WA7BVQ,UA6BU,GA5BRR,mBAAA,CA2BM,OA3BNS,UA2BM,G,0BA1BJT,mBAAA,CAAgD;IAA5CJ,KAAK,EAAC;EAAW,GAAC,uBAAqB,sB,0BAC3CI,mBAAA,CAGI;IAHDJ,KAAK,EAAC;EAAiB,GAAC,gHAG3B,sBACAI,mBAAA,CAWS;IAVPJ,KAAK,EAAC,cAAc;IACnBc,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,SAAA,IAAAD,QAAA,CAAAC,SAAA,IAAAF,IAAA,CAAS;IAChBG,QAAQ,EAAEV,KAAA,CAAAW;gCAEXhB,mBAAA,CAAkC;IAA/BJ,KAAK,EAAC;EAAoB,6B,CAChBS,KAAA,CAAAW,OAAO,I,cAApBnB,mBAAA,CAA0C,QAAAoB,UAAA,EAApB,eAAa,M,cACnCpB,mBAAA,CAGO,QAAAqB,WAAA,EAAAP,MAAA,QAAAA,MAAA,OAFLX,mBAAA,CAAsC;IAAnCJ,KAAK,EAAC;EAAwB,4B,iBAAK,cAExC,E,kCAGFI,mBAAA,CAOM,OAPNmB,WAOM,GANJnB,mBAAA,CAKI,Y,2CALD,0BAED,IAAAA,mBAAA,CAEI;IAFDoB,IAAI,EAAC,GAAG;IAAEV,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAU,cAAA,KAAAT,IAAA,KAAUC,QAAA,CAAAS,YAAA,IAAAT,QAAA,CAAAS,YAAA,IAAAV,IAAA,CAAY;IAAEhB,KAAK,EAAC;KAAe,iBAE/D,E,SAMRG,mBAAA,YAAe,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}