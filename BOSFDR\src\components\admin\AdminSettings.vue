<template>
  <div class="admin-settings">
    <AdminHeader
      :userName="adminData?.first_name || 'Admin'"
      :showUserDropdown="showUserDropdown"
      :sidebarCollapsed="sidebarCollapsed"
      :activeMenu="activeMenu"
      @sidebar-toggle="handleSidebarToggle"
      @user-dropdown-toggle="handleUserDropdownToggle"
      @menu-action="handleMenuAction"
      @logout="handleLogout"
    />

    <!-- Mobile Overlay -->
    <div
      class="mobile-overlay"
      :class="{ active: !sidebarCollapsed && isMobile }"
      @click="closeMobileSidebar"
    ></div>

    <div class="dashboard-container">
      <AdminSidebar
        :collapsed="sidebarCollapsed"
        :activeMenu="activeMenu"
        @menu-change="handleMenuChange"
        @logout="handleLogout"
        @toggle-sidebar="handleSidebarToggle"
      />

      <main class="main-content" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
        <div class="container-fluid py-4">

          <!-- Settings Navigation -->
          <div class="row mb-4">
            <div class="col-12">
              <div class="card shadow">
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-3">
                      <div class="nav flex-column nav-pills" role="tablist">
                        <button
                          class="nav-link"
                          :class="{ active: activeTab === 'general' }"
                          @click="activeTab = 'general'"
                          type="button"
                        >
                          <i class="fas fa-cog me-2"></i>
                          General Settings
                        </button>
                        <button
                          class="nav-link"
                          :class="{ active: activeTab === 'security' }"
                          @click="activeTab = 'security'"
                          type="button"
                        >
                          <i class="fas fa-shield-alt me-2"></i>
                          Security
                        </button>
                        <button
                          class="nav-link"
                          :class="{ active: activeTab === 'notifications' }"
                          @click="activeTab = 'notifications'"
                          type="button"
                        >
                          <i class="fas fa-bell me-2"></i>
                          Notifications
                        </button>
                        <button
                          class="nav-link"
                          :class="{ active: activeTab === 'system' }"
                          @click="activeTab = 'system'"
                          type="button"
                        >
                          <i class="fas fa-server me-2"></i>
                          System
                        </button>
                        <button
                          class="nav-link"
                          :class="{ active: activeTab === 'backup' }"
                          @click="activeTab = 'backup'"
                          type="button"
                        >
                          <i class="fas fa-database me-2"></i>
                          Backup & Restore
                        </button>
                      </div>
                    </div>
                    <div class="col-md-9">
                      <!-- General Settings Tab -->
                      <div v-if="activeTab === 'general'" class="tab-content">
                        <h5 class="mb-3">General Settings</h5>
                        <form @submit.prevent="saveGeneralSettings">
                          <div class="row">
                            <div class="col-md-6 mb-3">
                              <label class="form-label">System Name</label>
                              <input type="text" class="form-control" v-model="settings.general.systemName">
                            </div>
                            <div class="col-md-6 mb-3">
                              <label class="form-label">Barangay Name</label>
                              <input type="text" class="form-control" v-model="settings.general.barangayName">
                            </div>
                          </div>
                          <div class="row">
                            <div class="col-md-6 mb-3">
                              <label class="form-label">Contact Email</label>
                              <input type="email" class="form-control" v-model="settings.general.contactEmail">
                            </div>
                            <div class="col-md-6 mb-3">
                              <label class="form-label">Contact Phone</label>
                              <input type="tel" class="form-control" v-model="settings.general.contactPhone">
                            </div>
                          </div>
                          <div class="mb-3">
                            <label class="form-label">Address</label>
                            <textarea class="form-control" rows="3" v-model="settings.general.address"></textarea>
                          </div>
                          <div class="row">
                            <div class="col-md-6 mb-3">
                              <label class="form-label">Timezone</label>
                              <select class="form-select" v-model="settings.general.timezone">
                                <option value="Asia/Manila">Asia/Manila (GMT+8)</option>
                                <option value="UTC">UTC (GMT+0)</option>
                              </select>
                            </div>
                            <div class="col-md-6 mb-3">
                              <label class="form-label">Language</label>
                              <select class="form-select" v-model="settings.general.language">
                                <option value="en">English</option>
                                <option value="fil">Filipino</option>
                              </select>
                            </div>
                          </div>
                          <div class="text-end">
                            <button type="submit" class="btn btn-success">
                              <i class="fas fa-save me-1"></i>
                              Save Changes
                            </button>
                          </div>
                        </form>
                      </div>

                      <!-- Security Settings Tab -->
                      <div v-if="activeTab === 'security'" class="tab-content">
                        <h5 class="mb-3">Security Settings</h5>
                        <form @submit.prevent="saveSecuritySettings">
                          <div class="row">
                            <div class="col-md-6 mb-3">
                              <label class="form-label">Password Policy</label>
                              <select class="form-select" v-model="settings.security.passwordPolicy">
                                <option value="basic">Basic (8 characters)</option>
                                <option value="medium">Medium (8 chars + mixed case)</option>
                                <option value="strong">Strong (8 chars + mixed case + numbers + symbols)</option>
                              </select>
                            </div>
                            <div class="col-md-6 mb-3">
                              <label class="form-label">Session Timeout (minutes)</label>
                              <input type="number" class="form-control" v-model="settings.security.sessionTimeout" min="15" max="480">
                            </div>
                          </div>
                          <div class="row">
                            <div class="col-md-6 mb-3">
                              <div class="form-check">
                                <input class="form-check-input" type="checkbox" v-model="settings.security.twoFactorAuth">
                                <label class="form-check-label">
                                  Enable Two-Factor Authentication
                                </label>
                              </div>
                            </div>
                            <div class="col-md-6 mb-3">
                              <div class="form-check">
                                <input class="form-check-input" type="checkbox" v-model="settings.security.loginNotifications">
                                <label class="form-check-label">
                                  Email Login Notifications
                                </label>
                              </div>
                            </div>
                          </div>
                          <div class="text-end">
                            <button type="submit" class="btn btn-success">
                              <i class="fas fa-save me-1"></i>
                              Save Changes
                            </button>
                          </div>
                        </form>
                      </div>

                      <!-- Notifications Settings Tab -->
                      <div v-if="activeTab === 'notifications'" class="tab-content">
                        <h5 class="mb-3">Notification Settings</h5>
                        <form @submit.prevent="saveNotificationSettings">
                          <div class="mb-4">
                            <h6>Email Notifications</h6>
                            <div class="form-check">
                              <input class="form-check-input" type="checkbox" v-model="settings.notifications.newUserRegistration">
                              <label class="form-check-label">New User Registration</label>
                            </div>
                            <div class="form-check">
                              <input class="form-check-input" type="checkbox" v-model="settings.notifications.documentRequests">
                              <label class="form-check-label">Document Requests</label>
                            </div>
                            <div class="form-check">
                              <input class="form-check-input" type="checkbox" v-model="settings.notifications.systemAlerts">
                              <label class="form-check-label">System Alerts</label>
                            </div>
                          </div>
                          <div class="mb-4">
                            <h6>SMS Notifications</h6>
                            <div class="form-check">
                              <input class="form-check-input" type="checkbox" v-model="settings.notifications.smsEnabled">
                              <label class="form-check-label">Enable SMS Notifications</label>
                            </div>
                            <div class="row mt-3">
                              <div class="col-md-6">
                                <label class="form-label">SMS Provider</label>
                                <select class="form-select" v-model="settings.notifications.smsProvider">
                                  <option value="semaphore">Semaphore</option>
                                  <option value="globe">Globe Labs</option>
                                  <option value="smart">Smart Communications</option>
                                </select>
                              </div>
                              <div class="col-md-6">
                                <label class="form-label">SMS API Key</label>
                                <input type="password" class="form-control" v-model="settings.notifications.smsApiKey">
                              </div>
                            </div>
                          </div>
                          <div class="text-end">
                            <button type="submit" class="btn btn-success">
                              <i class="fas fa-save me-1"></i>
                              Save Changes
                            </button>
                          </div>
                        </form>
                      </div>

                      <!-- System Settings Tab -->
                      <div v-if="activeTab === 'system'" class="tab-content">
                        <h5 class="mb-3">System Settings</h5>
                        <div class="row">
                          <div class="col-md-6 mb-4">
                            <div class="card border-0 bg-light">
                              <div class="card-body">
                                <h6 class="card-title">System Information</h6>
                                <table class="table table-sm table-borderless">
                                  <tbody>
                                    <tr>
                                      <td><strong>Version:</strong></td>
                                      <td>{{ systemInfo.version }}</td>
                                    </tr>
                                    <tr>
                                      <td><strong>Database:</strong></td>
                                      <td>{{ systemInfo.database }}</td>
                                    </tr>
                                    <tr>
                                      <td><strong>Server:</strong></td>
                                      <td>{{ systemInfo.server }}</td>
                                    </tr>
                                    <tr>
                                      <td><strong>Uptime:</strong></td>
                                      <td>{{ systemInfo.uptime }}</td>
                                    </tr>
                                  </tbody>
                                </table>
                              </div>
                            </div>
                          </div>
                          <div class="col-md-6 mb-4">
                            <div class="card border-0 bg-light">
                              <div class="card-body">
                                <h6 class="card-title">System Actions</h6>
                                <div class="d-grid gap-2">
                                  <button class="btn btn-warning btn-sm" @click="clearCache">
                                    <i class="fas fa-broom me-1"></i>
                                    Clear System Cache
                                  </button>
                                  <button class="btn btn-info btn-sm" @click="optimizeDatabase">
                                    <i class="fas fa-database me-1"></i>
                                    Optimize Database
                                  </button>
                                  <button class="btn btn-success btn-sm" @click="checkUpdates">
                                    <i class="fas fa-sync-alt me-1"></i>
                                    Check for Updates
                                  </button>
                                  <button class="btn btn-danger btn-sm" @click="restartSystem">
                                    <i class="fas fa-power-off me-1"></i>
                                    Restart System
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Backup & Restore Tab -->
                      <div v-if="activeTab === 'backup'" class="tab-content">
                        <h5 class="mb-3">Backup & Restore</h5>
                        <div class="row">
                          <div class="col-md-6 mb-4">
                            <div class="card border-0 bg-light">
                              <div class="card-body">
                                <h6 class="card-title">Create Backup</h6>
                                <p class="text-muted small">Create a full system backup including database and files.</p>
                                <div class="mb-3">
                                  <label class="form-label">Backup Type</label>
                                  <select class="form-select" v-model="backupType">
                                    <option value="full">Full Backup</option>
                                    <option value="database">Database Only</option>
                                    <option value="files">Files Only</option>
                                  </select>
                                </div>
                                <button class="btn btn-primary" @click="createBackup" :disabled="backupInProgress">
                                  <i class="fas fa-download me-1" :class="{ 'fa-spin': backupInProgress }"></i>
                                  {{ backupInProgress ? 'Creating Backup...' : 'Create Backup' }}
                                </button>
                              </div>
                            </div>
                          </div>
                          <div class="col-md-6 mb-4">
                            <div class="card border-0 bg-light">
                              <div class="card-body">
                                <h6 class="card-title">Restore Backup</h6>
                                <p class="text-muted small">Restore system from a previous backup.</p>
                                <div class="mb-3">
                                  <label class="form-label">Select Backup File</label>
                                  <input type="file" class="form-control" accept=".zip,.sql" @change="selectBackupFile">
                                </div>
                                <button class="btn btn-warning" @click="restoreBackup" :disabled="!selectedBackupFile || restoreInProgress">
                                  <i class="fas fa-upload me-1" :class="{ 'fa-spin': restoreInProgress }"></i>
                                  {{ restoreInProgress ? 'Restoring...' : 'Restore Backup' }}
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- Recent Backups -->
                        <div class="card border-0 bg-light">
                          <div class="card-body">
                            <h6 class="card-title">Recent Backups</h6>
                            <div class="table-responsive">
                              <table class="table table-sm">
                                <thead>
                                  <tr>
                                    <th>Date</th>
                                    <th>Type</th>
                                    <th>Size</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr v-for="backup in recentBackups" :key="backup.id">
                                    <td>{{ formatDate(backup.created_at) }}</td>
                                    <td>
                                      <span class="badge bg-info">{{ backup.type }}</span>
                                    </td>
                                    <td>{{ backup.size }}</td>
                                    <td>
                                      <span class="badge" :class="backup.status === 'completed' ? 'bg-success' : 'bg-warning'">
                                        {{ backup.status }}
                                      </span>
                                    </td>
                                    <td>
                                      <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" @click="downloadBackup(backup)">
                                          <i class="fas fa-download"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" @click="deleteBackup(backup)">
                                          <i class="fas fa-trash"></i>
                                        </button>
                                      </div>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script>
import AdminHeader from './AdminHeader.vue';
import AdminSidebar from './AdminSidebar.vue';
import unifiedAuthService from '@/services/unifiedAuthService';

export default {
  name: 'AdminSettings',
  components: {
    AdminHeader,
    AdminSidebar
  },

  data() {
    return {
      // UI State
      sidebarCollapsed: false,
      showUserDropdown: false,
      isMobile: false,
      adminData: null,
      // Component Data
      activeTab: 'general',
      backupInProgress: false,
      restoreInProgress: false,
      backupType: 'full',
      selectedBackupFile: null,

      settings: {
        general: {
          systemName: 'Barangay Bula Management System',
          barangayName: 'Barangay Bula',
          contactEmail: '<EMAIL>',
          contactPhone: '+63 ************',
          address: 'Barangay Bula, Camarines Sur, Philippines',
          timezone: 'Asia/Manila',
          language: 'en'
        },
        security: {
          passwordPolicy: 'medium',
          sessionTimeout: 120,
          twoFactorAuth: false,
          loginNotifications: true
        },
        notifications: {
          newUserRegistration: true,
          documentRequests: true,
          systemAlerts: true,
          smsEnabled: false,
          smsProvider: 'semaphore',
          smsApiKey: ''
        }
      },

      systemInfo: {
        version: '1.0.0',
        database: 'MySQL 8.0',
        server: 'Apache 2.4',
        uptime: '15 days, 3 hours'
      },

      recentBackups: [
        {
          id: 1,
          created_at: '2024-01-31T10:30:00Z',
          type: 'Full',
          size: '45.2 MB',
          status: 'completed'
        },
        {
          id: 2,
          created_at: '2024-01-30T10:30:00Z',
          type: 'Database',
          size: '12.8 MB',
          status: 'completed'
        },
        {
          id: 3,
          created_at: '2024-01-29T10:30:00Z',
          type: 'Full',
          size: '44.1 MB',
          status: 'completed'
        }
      ]
    };
  },

  async mounted() {
    // Check authentication
    if (!unifiedAuthService.isLoggedIn() || unifiedAuthService.getUserType() !== 'admin') {
      this.$router.push('/login');
      return;
    }

    // Initialize UI state
    this.initializeUI();

    // Load component data
    await this.loadAdminProfile();
    await this.loadSettings();
  },

  beforeUnmount() {
    if (this.handleResize) {
      window.removeEventListener('resize', this.handleResize);
    }
  },

  computed: {
    activeMenu() {
      const path = this.$route.path;
      if (path.includes('/admin/users')) return 'users';
      if (path.includes('/admin/requests')) return 'requests';
      if (path.includes('/admin/reports')) return 'reports';
      if (path.includes('/admin/settings')) return 'settings';
      if (path.includes('/admin/activity-logs')) return 'activity';
      if (path.includes('/admin/profile')) return 'profile';
      return 'dashboard';
    }
  },

  methods: {
    // Initialize UI state
    initializeUI() {
      this.isMobile = window.innerWidth <= 768;

      if (!this.isMobile) {
        const saved = localStorage.getItem('adminSidebarCollapsed');
        this.sidebarCollapsed = saved ? JSON.parse(saved) : false;
      } else {
        this.sidebarCollapsed = true;
      }

      this.handleResize = () => {
        const wasMobile = this.isMobile;
        this.isMobile = window.innerWidth <= 768;

        if (this.isMobile && !wasMobile) {
          this.sidebarCollapsed = true;
        } else if (!this.isMobile && wasMobile) {
          const saved = localStorage.getItem('adminSidebarCollapsed');
          this.sidebarCollapsed = saved ? JSON.parse(saved) : false;
        }
      };
      window.addEventListener('resize', this.handleResize);
    },

    handleSidebarToggle() {
      this.sidebarCollapsed = !this.sidebarCollapsed;
      localStorage.setItem('adminSidebarCollapsed', JSON.stringify(this.sidebarCollapsed));
    },

    handleMenuChange(menu) {
      const routes = {
        'dashboard': '/admin/dashboard',
        'users': '/admin/users',
        'requests': '/admin/requests',
        'reports': '/admin/reports',
        'settings': '/admin/settings',
        'activity': '/admin/activity-logs',
        'profile': '/admin/profile'
      };

      if (this.isMobile) {
        this.sidebarCollapsed = true;
      }

      if (routes[menu]) {
        this.$router.push(routes[menu]);
      }
    },

    handleUserDropdownToggle() {
      this.showUserDropdown = !this.showUserDropdown;
    },

    handleMenuAction(action) {
      if (action === 'profile') {
        this.$router.push('/admin/profile');
      } else if (action === 'settings') {
        this.$router.push('/admin/settings');
      }
      this.showUserDropdown = false;
    },

    closeMobileSidebar() {
      if (this.isMobile) {
        this.sidebarCollapsed = true;
      }
    },

    handleLogout() {
      unifiedAuthService.logout();
      this.$router.push('/login');
    },

    async loadAdminProfile() {
      try {
        const user = unifiedAuthService.getCurrentUser();
        if (user && user.profile) {
          this.adminData = user.profile;
        } else {
          this.adminData = {
            first_name: user?.username || 'Admin',
            role: user?.role || 'admin'
          };
        }
      } catch (error) {
        console.error('Failed to load admin profile:', error);
        const user = unifiedAuthService.getCurrentUser();
        this.adminData = {
          first_name: user?.username || 'Admin',
          role: user?.role || 'admin'
        };
      }
    },

    async loadSettings() {
      try {
        // Simulate API call to load settings
        await new Promise(resolve => setTimeout(resolve, 500));
        console.log('Settings loaded');
      } catch (error) {
        console.error('Failed to load settings:', error);
      }
    },

    async saveGeneralSettings() {
      try {
        console.log('Saving general settings:', this.settings.general);
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        alert('General settings saved successfully!');
      } catch (error) {
        console.error('Failed to save general settings:', error);
        alert('Failed to save settings. Please try again.');
      }
    },

    async saveSecuritySettings() {
      try {
        console.log('Saving security settings:', this.settings.security);
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        alert('Security settings saved successfully!');
      } catch (error) {
        console.error('Failed to save security settings:', error);
        alert('Failed to save settings. Please try again.');
      }
    },

    async saveNotificationSettings() {
      try {
        console.log('Saving notification settings:', this.settings.notifications);
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        alert('Notification settings saved successfully!');
      } catch (error) {
        console.error('Failed to save notification settings:', error);
        alert('Failed to save settings. Please try again.');
      }
    },

    // System actions
    async clearCache() {
      if (confirm('Are you sure you want to clear the system cache?')) {
        try {
          console.log('Clearing cache...');
          await new Promise(resolve => setTimeout(resolve, 2000));
          alert('System cache cleared successfully!');
        } catch (error) {
          console.error('Failed to clear cache:', error);
          alert('Failed to clear cache. Please try again.');
        }
      }
    },

    async optimizeDatabase() {
      if (confirm('Are you sure you want to optimize the database? This may take a few minutes.')) {
        try {
          console.log('Optimizing database...');
          await new Promise(resolve => setTimeout(resolve, 3000));
          alert('Database optimized successfully!');
        } catch (error) {
          console.error('Failed to optimize database:', error);
          alert('Failed to optimize database. Please try again.');
        }
      }
    },

    async checkUpdates() {
      try {
        console.log('Checking for updates...');
        await new Promise(resolve => setTimeout(resolve, 2000));
        alert('System is up to date!');
      } catch (error) {
        console.error('Failed to check updates:', error);
        alert('Failed to check for updates. Please try again.');
      }
    },

    async restartSystem() {
      if (confirm('Are you sure you want to restart the system? This will temporarily make the system unavailable.')) {
        try {
          console.log('Restarting system...');
          alert('System restart initiated. Please wait a few minutes before accessing the system again.');
        } catch (error) {
          console.error('Failed to restart system:', error);
          alert('Failed to restart system. Please try again.');
        }
      }
    },

    // Backup and restore methods
    async createBackup() {
      this.backupInProgress = true;
      try {
        console.log(`Creating ${this.backupType} backup...`);
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Add new backup to the list
        const newBackup = {
          id: Date.now(),
          created_at: new Date().toISOString(),
          type: this.backupType.charAt(0).toUpperCase() + this.backupType.slice(1),
          size: '42.5 MB',
          status: 'completed'
        };
        this.recentBackups.unshift(newBackup);

        alert('Backup created successfully!');
      } catch (error) {
        console.error('Failed to create backup:', error);
        alert('Failed to create backup. Please try again.');
      } finally {
        this.backupInProgress = false;
      }
    },

    selectBackupFile(event) {
      this.selectedBackupFile = event.target.files[0];
    },

    async restoreBackup() {
      if (!this.selectedBackupFile) {
        alert('Please select a backup file first.');
        return;
      }

      if (confirm('Are you sure you want to restore from this backup? This will overwrite current data.')) {
        this.restoreInProgress = true;
        try {
          console.log('Restoring backup:', this.selectedBackupFile.name);
          await new Promise(resolve => setTimeout(resolve, 8000));
          alert('Backup restored successfully!');
          this.selectedBackupFile = null;
        } catch (error) {
          console.error('Failed to restore backup:', error);
          alert('Failed to restore backup. Please try again.');
        } finally {
          this.restoreInProgress = false;
        }
      }
    },

    downloadBackup(backup) {
      console.log('Downloading backup:', backup);
      alert(`Download functionality for backup "${backup.type}" will be implemented soon.`);
    },

    deleteBackup(backup) {
      if (confirm(`Are you sure you want to delete the backup from ${this.formatDate(backup.created_at)}?`)) {
        const index = this.recentBackups.findIndex(b => b.id === backup.id);
        if (index > -1) {
          this.recentBackups.splice(index, 1);
          alert('Backup deleted successfully.');
        }
      }
    },

    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    },

    // Navigation handlers are now provided by the mixin

    goBack() {
      this.$router.push('/admin/dashboard');
    }
  }
};
</script>

<style scoped>
@import './css/adminDashboard.css';

/* Navigation pills */
.nav-pills .nav-link {
  color: #6c757d;
  border-radius: 0.5rem;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
}

.nav-pills .nav-link:hover {
  background-color: #f8f9fa;
  color: #495057;
}

.nav-pills .nav-link.active {
  background-color: #007bff;
  color: white;
}

/* Tab content */
.tab-content {
  min-height: 400px;
}

/* Form improvements */
.form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

.form-control,
.form-select {
  border-radius: 0.5rem;
  border: 1px solid #d1d3e2;
  transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Card improvements */
.card {
  border-radius: 1rem;
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

/* Button improvements */
.btn {
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-1px);
}

/* Table improvements */
.table-sm th,
.table-sm td {
  padding: 0.5rem;
  font-size: 0.875rem;
}

.table-borderless td {
  border: none;
}

/* Badge improvements */
.badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  border-radius: 0.5rem;
}

/* System info styling */
.card.bg-light {
  background-color: #f8f9fa !important;
  border: 1px solid #e9ecef;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .nav-pills {
    flex-direction: row;
    overflow-x: auto;
    white-space: nowrap;
    margin-bottom: 1rem;
  }

  .nav-pills .nav-link {
    margin-right: 0.5rem;
    margin-bottom: 0;
    white-space: nowrap;
  }

  .tab-content {
    min-height: auto;
  }

  .row .col-md-6 {
    margin-bottom: 1rem;
  }
}

@media (max-width: 576px) {
  .d-grid.gap-2 .btn {
    font-size: 0.875rem;
    padding: 0.5rem;
  }

  .table-responsive {
    font-size: 0.8rem;
  }

  .btn-group-sm .btn {
    padding: 0.25rem 0.375rem;
    font-size: 0.7rem;
  }
}
</style>
