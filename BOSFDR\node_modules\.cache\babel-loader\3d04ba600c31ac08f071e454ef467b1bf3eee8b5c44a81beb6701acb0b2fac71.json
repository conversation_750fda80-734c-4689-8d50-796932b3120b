{"ast": null, "code": "import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue';\nimport documentRequestService from '@/services/documentRequestService';\nimport ClientHeader from './ClientHeader.vue';\nimport HeroSection from './sections/HeroSection.vue';\nimport QuickActionsSection from './sections/QuickActionsSection.vue';\nimport DocumentServicesSection from './sections/DocumentServicesSection.vue';\nimport InformationSection from './sections/InformationSection.vue';\nimport unifiedAuthService from '@/services/unifiedAuthService';\nexport default {\n  name: 'NewDocumentRequest',\n  components: {\n    ClientHeader,\n    HeroSection,\n    QuickActionsSection,\n    DocumentServicesSection,\n    InformationSection\n  },\n  setup() {\n    // Reactive state\n    const documentTypes = ref([]);\n    const loading = ref(true);\n    const error = ref(null);\n\n    // Header state\n    const showUserDropdown = ref(false);\n\n    // User data\n    const userName = ref('User');\n    const userEmail = ref('<EMAIL>');\n    const userAvatar = ref(null);\n    const firstName = ref('User');\n    const totalRequests = ref(0);\n    const pendingRequests = ref(0);\n\n    // Template refs\n    const servicesSection = ref(null);\n    // Methods\n    const loadUserData = async () => {\n      try {\n        const currentUser = unifiedAuthService.getCurrentUser();\n        if (currentUser) {\n          userName.value = currentUser.username || 'User';\n          userEmail.value = currentUser.email || '<EMAIL>';\n          firstName.value = currentUser.first_name || currentUser.username || 'User';\n          userAvatar.value = currentUser.avatar || null;\n        }\n      } catch (error) {\n        console.error('Error loading user data:', error);\n      }\n    };\n    const loadUserStats = async () => {\n      try {\n        // TODO: Implement API call to get user statistics\n        // For now, using placeholder data\n        totalRequests.value = 5;\n        pendingRequests.value = 2;\n      } catch (error) {\n        console.error('Error loading user stats:', error);\n      }\n    };\n    const loadDocumentTypes = async () => {\n      try {\n        loading.value = true;\n        error.value = null;\n        const response = await documentRequestService.getDocumentTypes();\n        documentTypes.value = response.data || [];\n      } catch (err) {\n        console.error('Error loading document types:', err);\n        error.value = err.response?.data?.message || 'Failed to load available services';\n      } finally {\n        loading.value = false;\n      }\n    };\n    const selectDocumentType = documentType => {\n      if (!documentType.is_active) return;\n      const routeName = getRouteForDocumentType(documentType.type_name);\n      if (routeName) {\n        console.log('Navigate to:', routeName, 'with ID:', documentType.id);\n      }\n    };\n    const getRouteForDocumentType = typeName => {\n      const routes = {\n        'Barangay Clearance': 'BarangayClearanceRequest',\n        'Cedula': 'CedulaRequest'\n      };\n      return routes[typeName];\n    };\n\n    // Header event handlers\n    const handleUserDropdownToggle = () => {\n      showUserDropdown.value = !showUserDropdown.value;\n    };\n    const handleMenuAction = action => {\n      console.log('Menu action:', action);\n    };\n    const handleLogout = () => {\n      try {\n        unifiedAuthService.logout();\n        console.log('Logout and navigate to WelcomePage');\n      } catch (err) {\n        console.error('Logout error:', err);\n      }\n    };\n    const handleError = err => {\n      console.error('Header error:', err);\n    };\n    const handleSearch = query => {\n      console.log('Search query:', query);\n    };\n\n    // Navigation methods\n    const scrollToServices = () => {\n      servicesSection.value?.$el?.scrollIntoView({\n        behavior: 'smooth'\n      });\n    };\n    const goToMyRequests = () => {\n      console.log('Navigate to MyRequests');\n    };\n    const goToMyDocuments = () => {\n      console.log('Navigate to MyDocuments');\n    };\n    const openHelp = () => {\n      console.log('Opening help...');\n    };\n    const contactSupport = () => {\n      console.log('Contacting support...');\n    };\n\n    // Lifecycle\n    onMounted(async () => {\n      await loadUserData();\n      await loadDocumentTypes();\n      await loadUserStats();\n    });\n\n    // Return reactive state and methods for template\n    return {\n      // State\n      documentTypes,\n      loading,\n      error,\n      showUserDropdown,\n      userName,\n      userEmail,\n      userAvatar,\n      firstName,\n      totalRequests,\n      pendingRequests,\n      servicesSection,\n      // Methods\n      selectDocumentType,\n      loadDocumentTypes,\n      handleUserDropdownToggle,\n      handleMenuAction,\n      handleLogout,\n      handleError,\n      handleSearch,\n      scrollToServices,\n      goToMyRequests,\n      goToMyDocuments,\n      openHelp,\n      contactSupport\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "onMounted", "onBeforeUnmount", "nextTick", "documentRequestService", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HeroSection", "QuickActionsSection", "DocumentServicesSection", "InformationSection", "unifiedAuthService", "name", "components", "setup", "documentTypes", "loading", "error", "showUserDropdown", "userName", "userEmail", "userAvatar", "firstName", "totalRequests", "pendingRequests", "servicesSection", "loadUserData", "currentUser", "getCurrentUser", "value", "username", "email", "first_name", "avatar", "console", "loadUserStats", "loadDocumentTypes", "response", "getDocumentTypes", "data", "err", "message", "selectDocumentType", "documentType", "is_active", "routeName", "getRouteForDocumentType", "type_name", "log", "id", "typeName", "routes", "handleUserDropdownToggle", "handleMenuAction", "action", "handleLogout", "logout", "handleError", "handleSearch", "query", "scrollToServices", "$el", "scrollIntoView", "behavior", "goToMyRequests", "goToMyDocuments", "openHelp", "contactSupport"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\client\\NewDocumentRequest.vue"], "sourcesContent": ["<template>\n  <div class=\"client-dashboard\">\n    <!-- Background -->\n    <div class=\"background-container\">\n      <div class=\"background-image\"></div>\n      <div class=\"background-overlay\"></div>\n    </div>\n\n    <!-- Client Header with Navigation -->\n    <ClientHeader\n      :userName=\"userName\"\n      :userEmail=\"userEmail\"\n      :userAvatar=\"userAvatar\"\n      :showUserDropdown=\"showUserDropdown\"\n      :showBreadcrumbs=\"true\"\n      @user-dropdown-toggle=\"handleUserDropdownToggle\"\n      @menu-action=\"handleMenuAction\"\n      @logout=\"handleLogout\"\n      @error=\"handleError\"\n      @search=\"handleSearch\"\n    />\n\n    <!-- Main Content -->\n    <main id=\"main-content\" class=\"main-content\">\n      <!-- Hero Section -->\n      <HeroSection\n        :firstName=\"firstName\"\n        :totalRequests=\"totalRequests\"\n        :pendingRequests=\"pendingRequests\"\n        @start-new-request=\"scrollToServices\"\n        @view-requests=\"goToMyRequests\"\n      />\n\n      <!-- Quick Actions Section -->\n      <QuickActionsSection\n        @start-new-request=\"scrollToServices\"\n        @view-requests=\"goToMyRequests\"\n        @view-documents=\"goToMyDocuments\"\n        @get-help=\"openHelp\"\n      />\n\n      <!-- Document Services Section -->\n      <DocumentServicesSection\n        ref=\"servicesSection\"\n        :documentTypes=\"documentTypes\"\n        :loading=\"loading\"\n        :error=\"error\"\n        @select-document-type=\"selectDocumentType\"\n        @retry=\"loadDocumentTypes\"\n      />\n\n      <!-- Information and Help Section -->\n      <InformationSection\n        @open-help=\"openHelp\"\n        @contact-support=\"contactSupport\"\n      />\n    </main>\n  </div>\n</template>\n\n<script>\nimport { ref, onMounted, onBeforeUnmount, nextTick } from 'vue';\nimport documentRequestService from '@/services/documentRequestService';\nimport ClientHeader from './ClientHeader.vue';\nimport HeroSection from './sections/HeroSection.vue';\nimport QuickActionsSection from './sections/QuickActionsSection.vue';\nimport DocumentServicesSection from './sections/DocumentServicesSection.vue';\nimport InformationSection from './sections/InformationSection.vue';\nimport unifiedAuthService from '@/services/unifiedAuthService';\n\nexport default {\n  name: 'NewDocumentRequest',\n  components: {\n    ClientHeader,\n    HeroSection,\n    QuickActionsSection,\n    DocumentServicesSection,\n    InformationSection\n  },\n  setup() {\n    // Reactive state\n    const documentTypes = ref([]);\n    const loading = ref(true);\n    const error = ref(null);\n\n    // Header state\n    const showUserDropdown = ref(false);\n\n    // User data\n    const userName = ref('User');\n    const userEmail = ref('<EMAIL>');\n    const userAvatar = ref(null);\n    const firstName = ref('User');\n    const totalRequests = ref(0);\n    const pendingRequests = ref(0);\n\n    // Template refs\n    const servicesSection = ref(null);\n    // Methods\n    const loadUserData = async () => {\n      try {\n        const currentUser = unifiedAuthService.getCurrentUser();\n        if (currentUser) {\n          userName.value = currentUser.username || 'User';\n          userEmail.value = currentUser.email || '<EMAIL>';\n          firstName.value = currentUser.first_name || currentUser.username || 'User';\n          userAvatar.value = currentUser.avatar || null;\n        }\n      } catch (error) {\n        console.error('Error loading user data:', error);\n      }\n    };\n\n    const loadUserStats = async () => {\n      try {\n        // TODO: Implement API call to get user statistics\n        // For now, using placeholder data\n        totalRequests.value = 5;\n        pendingRequests.value = 2;\n      } catch (error) {\n        console.error('Error loading user stats:', error);\n      }\n    };\n\n    const loadDocumentTypes = async () => {\n      try {\n        loading.value = true;\n        error.value = null;\n\n        const response = await documentRequestService.getDocumentTypes();\n        documentTypes.value = response.data || [];\n\n      } catch (err) {\n        console.error('Error loading document types:', err);\n        error.value = err.response?.data?.message || 'Failed to load available services';\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    const selectDocumentType = (documentType) => {\n      if (!documentType.is_active) return;\n\n      const routeName = getRouteForDocumentType(documentType.type_name);\n      if (routeName) {\n        console.log('Navigate to:', routeName, 'with ID:', documentType.id);\n      }\n    };\n\n    const getRouteForDocumentType = (typeName) => {\n      const routes = {\n        'Barangay Clearance': 'BarangayClearanceRequest',\n        'Cedula': 'CedulaRequest'\n      };\n      return routes[typeName];\n    };\n\n    // Header event handlers\n    const handleUserDropdownToggle = () => {\n      showUserDropdown.value = !showUserDropdown.value;\n    };\n\n    const handleMenuAction = (action) => {\n      console.log('Menu action:', action);\n    };\n\n    const handleLogout = () => {\n      try {\n        unifiedAuthService.logout();\n        console.log('Logout and navigate to WelcomePage');\n      } catch (err) {\n        console.error('Logout error:', err);\n      }\n    };\n\n    const handleError = (err) => {\n      console.error('Header error:', err);\n    };\n\n    const handleSearch = (query) => {\n      console.log('Search query:', query);\n    };\n\n    // Navigation methods\n    const scrollToServices = () => {\n      servicesSection.value?.$el?.scrollIntoView({ behavior: 'smooth' });\n    };\n\n    const goToMyRequests = () => {\n      console.log('Navigate to MyRequests');\n    };\n\n    const goToMyDocuments = () => {\n      console.log('Navigate to MyDocuments');\n    };\n\n    const openHelp = () => {\n      console.log('Opening help...');\n    };\n\n    const contactSupport = () => {\n      console.log('Contacting support...');\n    };\n\n    // Lifecycle\n    onMounted(async () => {\n      await loadUserData();\n      await loadDocumentTypes();\n      await loadUserStats();\n    });\n\n    // Return reactive state and methods for template\n    return {\n      // State\n      documentTypes,\n      loading,\n      error,\n      showUserDropdown,\n      userName,\n      userEmail,\n      userAvatar,\n      firstName,\n      totalRequests,\n      pendingRequests,\n      servicesSection,\n\n      // Methods\n      selectDocumentType,\n      loadDocumentTypes,\n      handleUserDropdownToggle,\n      handleMenuAction,\n      handleLogout,\n      handleError,\n      handleSearch,\n      scrollToServices,\n      goToMyRequests,\n      goToMyDocuments,\n      openHelp,\n      contactSupport\n    };\n  }\n};\n</script>\n\n<style scoped>\n/* Import design tokens from ClientHeader */\n@import './css/clientHeader.css';\n\n/* Client Dashboard Layout */\n.client-dashboard {\n  min-height: 100vh;\n  background: #f9f9f9;\n  position: relative;\n}\n\n/* Background Setup */\n.background-container {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: -2;\n}\n\n.background-image {\n  width: 100%;\n  height: 100%;\n  background-image: url('@/assets/bula-request-background-pic.png');\n  background-size: cover;\n  background-position: center;\n  background-repeat: no-repeat;\n  background-attachment: fixed;\n  opacity: 0.03;\n}\n\n.background-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    135deg,\n    rgba(0, 94, 162, 0.02) 0%,\n    rgba(35, 120, 195, 0.03) 100%\n  );\n  z-index: -1;\n}\n\n/* Main Content */\n.main-content {\n  margin-top: 140px; /* Account for fixed header */\n  padding-bottom: var(--spacing-16);\n  position: relative;\n  z-index: 1;\n}\n</style>\n"], "mappings": "AA6DA,SAASA,GAAG,EAAEC,SAAS,EAAEC,eAAe,EAAEC,QAAO,QAAS,KAAK;AAC/D,OAAOC,sBAAqB,MAAO,mCAAmC;AACtE,OAAOC,YAAW,MAAO,oBAAoB;AAC7C,OAAOC,WAAU,MAAO,4BAA4B;AACpD,OAAOC,mBAAkB,MAAO,oCAAoC;AACpE,OAAOC,uBAAsB,MAAO,wCAAwC;AAC5E,OAAOC,kBAAiB,MAAO,mCAAmC;AAClE,OAAOC,kBAAiB,MAAO,+BAA+B;AAE9D,eAAe;EACbC,IAAI,EAAE,oBAAoB;EAC1BC,UAAU,EAAE;IACVP,YAAY;IACZC,WAAW;IACXC,mBAAmB;IACnBC,uBAAuB;IACvBC;EACF,CAAC;EACDI,KAAKA,CAAA,EAAG;IACN;IACA,MAAMC,aAAY,GAAId,GAAG,CAAC,EAAE,CAAC;IAC7B,MAAMe,OAAM,GAAIf,GAAG,CAAC,IAAI,CAAC;IACzB,MAAMgB,KAAI,GAAIhB,GAAG,CAAC,IAAI,CAAC;;IAEvB;IACA,MAAMiB,gBAAe,GAAIjB,GAAG,CAAC,KAAK,CAAC;;IAEnC;IACA,MAAMkB,QAAO,GAAIlB,GAAG,CAAC,MAAM,CAAC;IAC5B,MAAMmB,SAAQ,GAAInB,GAAG,CAAC,kBAAkB,CAAC;IACzC,MAAMoB,UAAS,GAAIpB,GAAG,CAAC,IAAI,CAAC;IAC5B,MAAMqB,SAAQ,GAAIrB,GAAG,CAAC,MAAM,CAAC;IAC7B,MAAMsB,aAAY,GAAItB,GAAG,CAAC,CAAC,CAAC;IAC5B,MAAMuB,eAAc,GAAIvB,GAAG,CAAC,CAAC,CAAC;;IAE9B;IACA,MAAMwB,eAAc,GAAIxB,GAAG,CAAC,IAAI,CAAC;IACjC;IACA,MAAMyB,YAAW,GAAI,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACF,MAAMC,WAAU,GAAIhB,kBAAkB,CAACiB,cAAc,CAAC,CAAC;QACvD,IAAID,WAAW,EAAE;UACfR,QAAQ,CAACU,KAAI,GAAIF,WAAW,CAACG,QAAO,IAAK,MAAM;UAC/CV,SAAS,CAACS,KAAI,GAAIF,WAAW,CAACI,KAAI,IAAK,kBAAkB;UACzDT,SAAS,CAACO,KAAI,GAAIF,WAAW,CAACK,UAAS,IAAKL,WAAW,CAACG,QAAO,IAAK,MAAM;UAC1ET,UAAU,CAACQ,KAAI,GAAIF,WAAW,CAACM,MAAK,IAAK,IAAI;QAC/C;MACF,EAAE,OAAOhB,KAAK,EAAE;QACdiB,OAAO,CAACjB,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;IACF,CAAC;IAED,MAAMkB,aAAY,GAAI,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF;QACA;QACAZ,aAAa,CAACM,KAAI,GAAI,CAAC;QACvBL,eAAe,CAACK,KAAI,GAAI,CAAC;MAC3B,EAAE,OAAOZ,KAAK,EAAE;QACdiB,OAAO,CAACjB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;IACF,CAAC;IAED,MAAMmB,iBAAgB,GAAI,MAAAA,CAAA,KAAY;MACpC,IAAI;QACFpB,OAAO,CAACa,KAAI,GAAI,IAAI;QACpBZ,KAAK,CAACY,KAAI,GAAI,IAAI;QAElB,MAAMQ,QAAO,GAAI,MAAMhC,sBAAsB,CAACiC,gBAAgB,CAAC,CAAC;QAChEvB,aAAa,CAACc,KAAI,GAAIQ,QAAQ,CAACE,IAAG,IAAK,EAAE;MAE3C,EAAE,OAAOC,GAAG,EAAE;QACZN,OAAO,CAACjB,KAAK,CAAC,+BAA+B,EAAEuB,GAAG,CAAC;QACnDvB,KAAK,CAACY,KAAI,GAAIW,GAAG,CAACH,QAAQ,EAAEE,IAAI,EAAEE,OAAM,IAAK,mCAAmC;MAClF,UAAU;QACRzB,OAAO,CAACa,KAAI,GAAI,KAAK;MACvB;IACF,CAAC;IAED,MAAMa,kBAAiB,GAAKC,YAAY,IAAK;MAC3C,IAAI,CAACA,YAAY,CAACC,SAAS,EAAE;MAE7B,MAAMC,SAAQ,GAAIC,uBAAuB,CAACH,YAAY,CAACI,SAAS,CAAC;MACjE,IAAIF,SAAS,EAAE;QACbX,OAAO,CAACc,GAAG,CAAC,cAAc,EAAEH,SAAS,EAAE,UAAU,EAAEF,YAAY,CAACM,EAAE,CAAC;MACrE;IACF,CAAC;IAED,MAAMH,uBAAsB,GAAKI,QAAQ,IAAK;MAC5C,MAAMC,MAAK,GAAI;QACb,oBAAoB,EAAE,0BAA0B;QAChD,QAAQ,EAAE;MACZ,CAAC;MACD,OAAOA,MAAM,CAACD,QAAQ,CAAC;IACzB,CAAC;;IAED;IACA,MAAME,wBAAuB,GAAIA,CAAA,KAAM;MACrClC,gBAAgB,CAACW,KAAI,GAAI,CAACX,gBAAgB,CAACW,KAAK;IAClD,CAAC;IAED,MAAMwB,gBAAe,GAAKC,MAAM,IAAK;MACnCpB,OAAO,CAACc,GAAG,CAAC,cAAc,EAAEM,MAAM,CAAC;IACrC,CAAC;IAED,MAAMC,YAAW,GAAIA,CAAA,KAAM;MACzB,IAAI;QACF5C,kBAAkB,CAAC6C,MAAM,CAAC,CAAC;QAC3BtB,OAAO,CAACc,GAAG,CAAC,oCAAoC,CAAC;MACnD,EAAE,OAAOR,GAAG,EAAE;QACZN,OAAO,CAACjB,KAAK,CAAC,eAAe,EAAEuB,GAAG,CAAC;MACrC;IACF,CAAC;IAED,MAAMiB,WAAU,GAAKjB,GAAG,IAAK;MAC3BN,OAAO,CAACjB,KAAK,CAAC,eAAe,EAAEuB,GAAG,CAAC;IACrC,CAAC;IAED,MAAMkB,YAAW,GAAKC,KAAK,IAAK;MAC9BzB,OAAO,CAACc,GAAG,CAAC,eAAe,EAAEW,KAAK,CAAC;IACrC,CAAC;;IAED;IACA,MAAMC,gBAAe,GAAIA,CAAA,KAAM;MAC7BnC,eAAe,CAACI,KAAK,EAAEgC,GAAG,EAAEC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IACpE,CAAC;IAED,MAAMC,cAAa,GAAIA,CAAA,KAAM;MAC3B9B,OAAO,CAACc,GAAG,CAAC,wBAAwB,CAAC;IACvC,CAAC;IAED,MAAMiB,eAAc,GAAIA,CAAA,KAAM;MAC5B/B,OAAO,CAACc,GAAG,CAAC,yBAAyB,CAAC;IACxC,CAAC;IAED,MAAMkB,QAAO,GAAIA,CAAA,KAAM;MACrBhC,OAAO,CAACc,GAAG,CAAC,iBAAiB,CAAC;IAChC,CAAC;IAED,MAAMmB,cAAa,GAAIA,CAAA,KAAM;MAC3BjC,OAAO,CAACc,GAAG,CAAC,uBAAuB,CAAC;IACtC,CAAC;;IAED;IACA9C,SAAS,CAAC,YAAY;MACpB,MAAMwB,YAAY,CAAC,CAAC;MACpB,MAAMU,iBAAiB,CAAC,CAAC;MACzB,MAAMD,aAAa,CAAC,CAAC;IACvB,CAAC,CAAC;;IAEF;IACA,OAAO;MACL;MACApB,aAAa;MACbC,OAAO;MACPC,KAAK;MACLC,gBAAgB;MAChBC,QAAQ;MACRC,SAAS;MACTC,UAAU;MACVC,SAAS;MACTC,aAAa;MACbC,eAAe;MACfC,eAAe;MAEf;MACAiB,kBAAkB;MAClBN,iBAAiB;MACjBgB,wBAAwB;MACxBC,gBAAgB;MAChBE,YAAY;MACZE,WAAW;MACXC,YAAY;MACZE,gBAAgB;MAChBI,cAAc;MACdC,eAAe;MACfC,QAAQ;MACRC;IACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}