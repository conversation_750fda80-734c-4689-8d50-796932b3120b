require('dotenv').config();

console.log('🔍 Debugging Webhook Configuration');
console.log('==================================');

console.log('Environment Variables:');
console.log('PAYMONGO_WEBHOOK_SECRET:', process.env.PAYMONGO_WEBHOOK_SECRET ? 'SET' : 'NOT SET');
console.log('WEBHOOK_URL:', process.env.WEBHOOK_URL);

if (process.env.PAYMONGO_WEBHOOK_SECRET) {
  console.log('Webhook Secret (first 10 chars):', process.env.PAYMONGO_WEBHOOK_SECRET.substring(0, 10) + '...');
  console.log('Webhook Secret length:', process.env.PAYMONGO_WEBHOOK_SECRET.length);
} else {
  console.log('❌ PAYMONGO_WEBHOOK_SECRET is not set!');
}

// Test signature verification
const crypto = require('crypto');

function testSignatureVerification() {
  const secret = process.env.PAYMONGO_WEBHOOK_SECRET;
  if (!secret) {
    console.log('❌ Cannot test signature - secret not set');
    return;
  }

  console.log('\n🧪 Testing Signature Verification:');
  
  // Sample payload
  const testPayload = '{"test": "data"}';
  
  // Create expected signature
  const computedSignature = crypto
    .createHmac('sha256', secret)
    .update(testPayload, 'utf8')
    .digest('hex');
  
  const expectedSignature = `sha256=${computedSignature}`;
  
  console.log('Test payload:', testPayload);
  console.log('Computed signature:', expectedSignature);
  
  // Test verification
  try {
    const isValid = crypto.timingSafeEqual(
      Buffer.from(expectedSignature),
      Buffer.from(expectedSignature)
    );
    console.log('Signature verification test:', isValid ? '✅ PASS' : '❌ FAIL');
  } catch (error) {
    console.log('Signature verification error:', error.message);
  }
}

testSignatureVerification();
