{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.VUE_APP_API_BASE_URL || 'http://localhost:7000/api';\nclass UnifiedAuthService {\n  // Clear invalid authentication data\n  clearInvalidData() {\n    const userStr = localStorage.getItem('auth_user');\n    if (userStr === 'undefined' || userStr === 'null') {\n      localStorage.removeItem('auth_user');\n      localStorage.removeItem('auth_token');\n    }\n    const sessionUserStr = sessionStorage.getItem('auth_user');\n    if (sessionUserStr === 'undefined' || sessionUserStr === 'null') {\n      sessionStorage.removeItem('auth_user');\n      sessionStorage.removeItem('auth_token');\n    }\n  }\n  constructor() {\n    // Clean up any invalid data from previous sessions\n    this.clearInvalidData();\n    this.apiClient = axios.create({\n      baseURL: `${API_BASE_URL}/auth/unified`,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n\n    // Add request interceptor to include auth token\n    this.apiClient.interceptors.request.use(config => {\n      const token = this.getToken();\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n      return config;\n    }, error => {\n      return Promise.reject(error);\n    });\n\n    // Add response interceptor to handle token expiration\n    this.apiClient.interceptors.response.use(response => response, error => {\n      if (error.response?.status === 401) {\n        this.logout();\n        window.location.href = '/login';\n      }\n      return Promise.reject(error);\n    });\n  }\n\n  // Login method\n  async login(credentials) {\n    try {\n      const response = await this.apiClient.post('/login', credentials);\n      if (response.data.success) {\n        const {\n          user,\n          token,\n          redirectUrl\n        } = response.data.data;\n\n        // Store authentication data\n        this.setToken(token);\n        this.setUser(user);\n        return {\n          success: true,\n          user,\n          redirectUrl,\n          message: response.data.message\n        };\n      }\n      return {\n        success: false,\n        message: response.data.message || 'Login failed'\n      };\n    } catch (error) {\n      console.error('Login error:', error);\n      throw error;\n    }\n  }\n\n  // Check if user is logged in\n  isLoggedIn() {\n    const token = this.getToken();\n    const user = this.getUser();\n    return !!(token && user);\n  }\n\n  // Get current user\n  getCurrentUser() {\n    return this.getUser();\n  }\n\n  // Backward compatibility methods\n  getAdminData() {\n    const user = this.getUser();\n    return user && user.type === 'admin' ? user : null;\n  }\n  getClientData() {\n    const user = this.getUser();\n    return user && user.type === 'client' ? user : null;\n  }\n\n  // Get user type (admin or client)\n  getUserType() {\n    const user = this.getUser();\n    return user?.type || null;\n  }\n\n  // Get user role (for admin users)\n  getUserRole() {\n    const user = this.getUser();\n    return user?.role || null;\n  }\n\n  // Logout\n  logout() {\n    localStorage.removeItem('auth_token');\n    localStorage.removeItem('auth_user');\n    sessionStorage.removeItem('auth_token');\n    sessionStorage.removeItem('auth_user');\n  }\n\n  // Token management\n  setToken(token) {\n    localStorage.setItem('auth_token', token);\n  }\n  getToken() {\n    return localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');\n  }\n\n  // User data management\n  setUser(user) {\n    if (user && typeof user === 'object') {\n      localStorage.setItem('auth_user', JSON.stringify(user));\n      console.log('User data stored successfully:', user);\n    } else {\n      console.error('Invalid user data, not storing:', user);\n    }\n  }\n  getUser() {\n    const userStr = localStorage.getItem('auth_user') || sessionStorage.getItem('auth_user');\n    try {\n      if (!userStr || userStr === 'undefined' || userStr === 'null') {\n        return null;\n      }\n      return JSON.parse(userStr);\n    } catch (error) {\n      console.error('Error parsing user data:', error);\n      // Clear invalid data\n      localStorage.removeItem('auth_user');\n      sessionStorage.removeItem('auth_user');\n      return null;\n    }\n  }\n\n  // Parse error response\n  parseError(error) {\n    if (error.response?.data) {\n      return {\n        message: error.response.data.message || 'An error occurred',\n        status: error.response.status,\n        errors: error.response.data.errors || []\n      };\n    }\n    if (error.message) {\n      return {\n        message: error.message,\n        status: 500,\n        errors: []\n      };\n    }\n    return {\n      message: 'Network error occurred',\n      status: 0,\n      errors: []\n    };\n  }\n\n  // Get redirect URL based on user type and role\n  getRedirectUrl(user) {\n    if (user.type === 'admin') {\n      return '/admin/dashboard';\n    } else if (user.type === 'client') {\n      return '/client/home'; // Updated to redirect to new client home page\n    }\n    return '/';\n  }\n}\nexport default new UnifiedAuthService();", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "VUE_APP_API_BASE_URL", "UnifiedAuthService", "clearInvalidData", "userStr", "localStorage", "getItem", "removeItem", "sessionUserStr", "sessionStorage", "constructor", "apiClient", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "getToken", "Authorization", "error", "Promise", "reject", "response", "status", "logout", "window", "location", "href", "login", "credentials", "post", "data", "success", "user", "redirectUrl", "setToken", "setUser", "message", "console", "isLoggedIn", "getUser", "getCurrentUser", "getAdminData", "type", "getClientData", "getUserType", "getUserRole", "role", "setItem", "JSON", "stringify", "log", "parse", "parseError", "errors", "getRedirectUrl"], "sources": ["D:/cap2_rhai_front_and_back/BOSFDR/src/services/unifiedAuthService.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.VUE_APP_API_BASE_URL || 'http://localhost:7000/api';\n\nclass UnifiedAuthService {\n  // Clear invalid authentication data\n  clearInvalidData() {\n    const userStr = localStorage.getItem('auth_user');\n    if (userStr === 'undefined' || userStr === 'null') {\n      localStorage.removeItem('auth_user');\n      localStorage.removeItem('auth_token');\n    }\n\n    const sessionUserStr = sessionStorage.getItem('auth_user');\n    if (sessionUserStr === 'undefined' || sessionUserStr === 'null') {\n      sessionStorage.removeItem('auth_user');\n      sessionStorage.removeItem('auth_token');\n    }\n  }\n\n  constructor() {\n    // Clean up any invalid data from previous sessions\n    this.clearInvalidData();\n\n    this.apiClient = axios.create({\n      baseURL: `${API_BASE_URL}/auth/unified`,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    // Add request interceptor to include auth token\n    this.apiClient.interceptors.request.use(\n      (config) => {\n        const token = this.getToken();\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n\n    // Add response interceptor to handle token expiration\n    this.apiClient.interceptors.response.use(\n      (response) => response,\n      (error) => {\n        if (error.response?.status === 401) {\n          this.logout();\n          window.location.href = '/login';\n        }\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  // Login method\n  async login(credentials) {\n    try {\n      const response = await this.apiClient.post('/login', credentials);\n      \n      if (response.data.success) {\n        const { user, token, redirectUrl } = response.data.data;\n        \n        // Store authentication data\n        this.setToken(token);\n        this.setUser(user);\n        \n        return {\n          success: true,\n          user,\n          redirectUrl,\n          message: response.data.message\n        };\n      }\n      \n      return {\n        success: false,\n        message: response.data.message || 'Login failed'\n      };\n    } catch (error) {\n      console.error('Login error:', error);\n      throw error;\n    }\n  }\n\n  // Check if user is logged in\n  isLoggedIn() {\n    const token = this.getToken();\n    const user = this.getUser();\n    return !!(token && user);\n  }\n\n  // Get current user\n  getCurrentUser() {\n    return this.getUser();\n  }\n\n  // Backward compatibility methods\n  getAdminData() {\n    const user = this.getUser();\n    return user && user.type === 'admin' ? user : null;\n  }\n\n  getClientData() {\n    const user = this.getUser();\n    return user && user.type === 'client' ? user : null;\n  }\n\n  // Get user type (admin or client)\n  getUserType() {\n    const user = this.getUser();\n    return user?.type || null;\n  }\n\n  // Get user role (for admin users)\n  getUserRole() {\n    const user = this.getUser();\n    return user?.role || null;\n  }\n\n  // Logout\n  logout() {\n    localStorage.removeItem('auth_token');\n    localStorage.removeItem('auth_user');\n    sessionStorage.removeItem('auth_token');\n    sessionStorage.removeItem('auth_user');\n  }\n\n\n\n  // Token management\n  setToken(token) {\n    localStorage.setItem('auth_token', token);\n  }\n\n  getToken() {\n    return localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');\n  }\n\n  // User data management\n  setUser(user) {\n    if (user && typeof user === 'object') {\n      localStorage.setItem('auth_user', JSON.stringify(user));\n      console.log('User data stored successfully:', user);\n    } else {\n      console.error('Invalid user data, not storing:', user);\n    }\n  }\n\n  getUser() {\n    const userStr = localStorage.getItem('auth_user') || sessionStorage.getItem('auth_user');\n    try {\n      if (!userStr || userStr === 'undefined' || userStr === 'null') {\n        return null;\n      }\n      return JSON.parse(userStr);\n    } catch (error) {\n      console.error('Error parsing user data:', error);\n      // Clear invalid data\n      localStorage.removeItem('auth_user');\n      sessionStorage.removeItem('auth_user');\n      return null;\n    }\n  }\n\n  // Parse error response\n  parseError(error) {\n    if (error.response?.data) {\n      return {\n        message: error.response.data.message || 'An error occurred',\n        status: error.response.status,\n        errors: error.response.data.errors || []\n      };\n    }\n    \n    if (error.message) {\n      return {\n        message: error.message,\n        status: 500,\n        errors: []\n      };\n    }\n    \n    return {\n      message: 'Network error occurred',\n      status: 0,\n      errors: []\n    };\n  }\n\n  // Get redirect URL based on user type and role\n  getRedirectUrl(user) {\n    if (user.type === 'admin') {\n      return '/admin/dashboard';\n    } else if (user.type === 'client') {\n      return '/client/home'; // Updated to redirect to new client home page\n    }\n    return '/';\n  }\n}\n\nexport default new UnifiedAuthService();\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,oBAAoB,IAAI,2BAA2B;AAEpF,MAAMC,kBAAkB,CAAC;EACvB;EACAC,gBAAgBA,CAAA,EAAG;IACjB,MAAMC,OAAO,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACjD,IAAIF,OAAO,KAAK,WAAW,IAAIA,OAAO,KAAK,MAAM,EAAE;MACjDC,YAAY,CAACE,UAAU,CAAC,WAAW,CAAC;MACpCF,YAAY,CAACE,UAAU,CAAC,YAAY,CAAC;IACvC;IAEA,MAAMC,cAAc,GAAGC,cAAc,CAACH,OAAO,CAAC,WAAW,CAAC;IAC1D,IAAIE,cAAc,KAAK,WAAW,IAAIA,cAAc,KAAK,MAAM,EAAE;MAC/DC,cAAc,CAACF,UAAU,CAAC,WAAW,CAAC;MACtCE,cAAc,CAACF,UAAU,CAAC,YAAY,CAAC;IACzC;EACF;EAEAG,WAAWA,CAAA,EAAG;IACZ;IACA,IAAI,CAACP,gBAAgB,CAAC,CAAC;IAEvB,IAAI,CAACQ,SAAS,GAAGd,KAAK,CAACe,MAAM,CAAC;MAC5BC,OAAO,EAAE,GAAGf,YAAY,eAAe;MACvCgB,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;;IAEF;IACA,IAAI,CAACH,SAAS,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACpCC,MAAM,IAAK;MACV,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,IAAID,KAAK,EAAE;QACTD,MAAM,CAACJ,OAAO,CAACO,aAAa,GAAG,UAAUF,KAAK,EAAE;MAClD;MACA,OAAOD,MAAM;IACf,CAAC,EACAI,KAAK,IAAK;MACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;;IAED;IACA,IAAI,CAACX,SAAS,CAACI,YAAY,CAACU,QAAQ,CAACR,GAAG,CACrCQ,QAAQ,IAAKA,QAAQ,EACrBH,KAAK,IAAK;MACT,IAAIA,KAAK,CAACG,QAAQ,EAAEC,MAAM,KAAK,GAAG,EAAE;QAClC,IAAI,CAACC,MAAM,CAAC,CAAC;QACbC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;MACjC;MACA,OAAOP,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;EACH;;EAEA;EACA,MAAMS,KAAKA,CAACC,WAAW,EAAE;IACvB,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAM,IAAI,CAACd,SAAS,CAACsB,IAAI,CAAC,QAAQ,EAAED,WAAW,CAAC;MAEjE,IAAIP,QAAQ,CAACS,IAAI,CAACC,OAAO,EAAE;QACzB,MAAM;UAAEC,IAAI;UAAEjB,KAAK;UAAEkB;QAAY,CAAC,GAAGZ,QAAQ,CAACS,IAAI,CAACA,IAAI;;QAEvD;QACA,IAAI,CAACI,QAAQ,CAACnB,KAAK,CAAC;QACpB,IAAI,CAACoB,OAAO,CAACH,IAAI,CAAC;QAElB,OAAO;UACLD,OAAO,EAAE,IAAI;UACbC,IAAI;UACJC,WAAW;UACXG,OAAO,EAAEf,QAAQ,CAACS,IAAI,CAACM;QACzB,CAAC;MACH;MAEA,OAAO;QACLL,OAAO,EAAE,KAAK;QACdK,OAAO,EAAEf,QAAQ,CAACS,IAAI,CAACM,OAAO,IAAI;MACpC,CAAC;IACH,CAAC,CAAC,OAAOlB,KAAK,EAAE;MACdmB,OAAO,CAACnB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,MAAMA,KAAK;IACb;EACF;;EAEA;EACAoB,UAAUA,CAAA,EAAG;IACX,MAAMvB,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMgB,IAAI,GAAG,IAAI,CAACO,OAAO,CAAC,CAAC;IAC3B,OAAO,CAAC,EAAExB,KAAK,IAAIiB,IAAI,CAAC;EAC1B;;EAEA;EACAQ,cAAcA,CAAA,EAAG;IACf,OAAO,IAAI,CAACD,OAAO,CAAC,CAAC;EACvB;;EAEA;EACAE,YAAYA,CAAA,EAAG;IACb,MAAMT,IAAI,GAAG,IAAI,CAACO,OAAO,CAAC,CAAC;IAC3B,OAAOP,IAAI,IAAIA,IAAI,CAACU,IAAI,KAAK,OAAO,GAAGV,IAAI,GAAG,IAAI;EACpD;EAEAW,aAAaA,CAAA,EAAG;IACd,MAAMX,IAAI,GAAG,IAAI,CAACO,OAAO,CAAC,CAAC;IAC3B,OAAOP,IAAI,IAAIA,IAAI,CAACU,IAAI,KAAK,QAAQ,GAAGV,IAAI,GAAG,IAAI;EACrD;;EAEA;EACAY,WAAWA,CAAA,EAAG;IACZ,MAAMZ,IAAI,GAAG,IAAI,CAACO,OAAO,CAAC,CAAC;IAC3B,OAAOP,IAAI,EAAEU,IAAI,IAAI,IAAI;EAC3B;;EAEA;EACAG,WAAWA,CAAA,EAAG;IACZ,MAAMb,IAAI,GAAG,IAAI,CAACO,OAAO,CAAC,CAAC;IAC3B,OAAOP,IAAI,EAAEc,IAAI,IAAI,IAAI;EAC3B;;EAEA;EACAvB,MAAMA,CAAA,EAAG;IACPtB,YAAY,CAACE,UAAU,CAAC,YAAY,CAAC;IACrCF,YAAY,CAACE,UAAU,CAAC,WAAW,CAAC;IACpCE,cAAc,CAACF,UAAU,CAAC,YAAY,CAAC;IACvCE,cAAc,CAACF,UAAU,CAAC,WAAW,CAAC;EACxC;;EAIA;EACA+B,QAAQA,CAACnB,KAAK,EAAE;IACdd,YAAY,CAAC8C,OAAO,CAAC,YAAY,EAAEhC,KAAK,CAAC;EAC3C;EAEAC,QAAQA,CAAA,EAAG;IACT,OAAOf,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,IAAIG,cAAc,CAACH,OAAO,CAAC,YAAY,CAAC;EACnF;;EAEA;EACAiC,OAAOA,CAACH,IAAI,EAAE;IACZ,IAAIA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MACpC/B,YAAY,CAAC8C,OAAO,CAAC,WAAW,EAAEC,IAAI,CAACC,SAAS,CAACjB,IAAI,CAAC,CAAC;MACvDK,OAAO,CAACa,GAAG,CAAC,gCAAgC,EAAElB,IAAI,CAAC;IACrD,CAAC,MAAM;MACLK,OAAO,CAACnB,KAAK,CAAC,iCAAiC,EAAEc,IAAI,CAAC;IACxD;EACF;EAEAO,OAAOA,CAAA,EAAG;IACR,MAAMvC,OAAO,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,IAAIG,cAAc,CAACH,OAAO,CAAC,WAAW,CAAC;IACxF,IAAI;MACF,IAAI,CAACF,OAAO,IAAIA,OAAO,KAAK,WAAW,IAAIA,OAAO,KAAK,MAAM,EAAE;QAC7D,OAAO,IAAI;MACb;MACA,OAAOgD,IAAI,CAACG,KAAK,CAACnD,OAAO,CAAC;IAC5B,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdmB,OAAO,CAACnB,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD;MACAjB,YAAY,CAACE,UAAU,CAAC,WAAW,CAAC;MACpCE,cAAc,CAACF,UAAU,CAAC,WAAW,CAAC;MACtC,OAAO,IAAI;IACb;EACF;;EAEA;EACAiD,UAAUA,CAAClC,KAAK,EAAE;IAChB,IAAIA,KAAK,CAACG,QAAQ,EAAES,IAAI,EAAE;MACxB,OAAO;QACLM,OAAO,EAAElB,KAAK,CAACG,QAAQ,CAACS,IAAI,CAACM,OAAO,IAAI,mBAAmB;QAC3Dd,MAAM,EAAEJ,KAAK,CAACG,QAAQ,CAACC,MAAM;QAC7B+B,MAAM,EAAEnC,KAAK,CAACG,QAAQ,CAACS,IAAI,CAACuB,MAAM,IAAI;MACxC,CAAC;IACH;IAEA,IAAInC,KAAK,CAACkB,OAAO,EAAE;MACjB,OAAO;QACLA,OAAO,EAAElB,KAAK,CAACkB,OAAO;QACtBd,MAAM,EAAE,GAAG;QACX+B,MAAM,EAAE;MACV,CAAC;IACH;IAEA,OAAO;MACLjB,OAAO,EAAE,wBAAwB;MACjCd,MAAM,EAAE,CAAC;MACT+B,MAAM,EAAE;IACV,CAAC;EACH;;EAEA;EACAC,cAAcA,CAACtB,IAAI,EAAE;IACnB,IAAIA,IAAI,CAACU,IAAI,KAAK,OAAO,EAAE;MACzB,OAAO,kBAAkB;IAC3B,CAAC,MAAM,IAAIV,IAAI,CAACU,IAAI,KAAK,QAAQ,EAAE;MACjC,OAAO,cAAc,CAAC,CAAC;IACzB;IACA,OAAO,GAAG;EACZ;AACF;AAEA,eAAe,IAAI5C,kBAAkB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}