{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, Fragment as _Fragment, renderList as _renderList, normalizeClass as _normalizeClass, createStaticVNode as _createStaticVNode } from \"vue\";\nimport _imports_0 from '@/assets/bula-request-background-pic.png';\nimport _imports_1 from '@/assets/icon-of-bula.jpg';\nconst _hoisted_1 = {\n  class: \"new-request-page\"\n};\nconst _hoisted_2 = {\n  class: \"page-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-content\"\n};\nconst _hoisted_4 = {\n  key: 0,\n  class: \"loading-container\"\n};\nconst _hoisted_5 = {\n  class: \"error-container\"\n};\nconst _hoisted_6 = {\n  class: \"error-content\"\n};\nconst _hoisted_7 = {\n  class: \"document-types-container\"\n};\nconst _hoisted_8 = {\n  class: \"document-types-grid\"\n};\nconst _hoisted_9 = [\"onClick\"];\nconst _hoisted_10 = {\n  class: \"document-icon\"\n};\nconst _hoisted_11 = {\n  class: \"document-content\"\n};\nconst _hoisted_12 = {\n  class: \"document-title\"\n};\nconst _hoisted_13 = {\n  class: \"document-description\"\n};\nconst _hoisted_14 = {\n  class: \"document-details\"\n};\nconst _hoisted_15 = {\n  class: \"fee-info\"\n};\nconst _hoisted_16 = {\n  class: \"fee-amount\"\n};\nconst _hoisted_17 = {\n  class: \"processing-time\"\n};\nconst _hoisted_18 = {\n  class: \"document-action\"\n};\nconst _hoisted_19 = {\n  key: 0,\n  class: \"status-badge unavailable\"\n};\nconst _hoisted_20 = {\n  key: 1,\n  class: \"fas fa-chevron-right\"\n};\nconst _hoisted_21 = {\n  class: \"info-section\"\n};\nconst _hoisted_22 = {\n  class: \"help-card\"\n};\nconst _hoisted_23 = {\n  class: \"help-content\"\n};\nconst _hoisted_24 = {\n  class: \"help-actions\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" Background Image \"), _cache[17] || (_cache[17] = _createElementVNode(\"div\", {\n    class: \"background-container\"\n  }, [_createElementVNode(\"img\", {\n    src: _imports_0,\n    alt: \"Barangay Bula background\",\n    class: \"background-image\"\n  }), _createElementVNode(\"div\", {\n    class: \"background-overlay\"\n  })], -1 /* HOISTED */)), _createCommentVNode(\" Header \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_cache[5] || (_cache[5] = _createStaticVNode(\"<div class=\\\"header-main\\\" data-v-b2da9790><div class=\\\"logo-section\\\" data-v-b2da9790><img src=\\\"\" + _imports_1 + \"\\\" alt=\\\"Barangay Bula Official Seal\\\" class=\\\"header-logo\\\" data-v-b2da9790></div><h1 class=\\\"page-title\\\" data-v-b2da9790><i class=\\\"fas fa-plus-circle\\\" data-v-b2da9790></i> New Document Request </h1><p class=\\\"page-description\\\" data-v-b2da9790> Choose the type of document you want to request from Barangay Bula </p></div>\", 1)), _createElementVNode(\"button\", {\n    class: \"back-btn\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.goBack && $options.goBack(...args))\n  }, _cache[4] || (_cache[4] = [_createElementVNode(\"i\", {\n    class: \"fas fa-arrow-left\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Back to Dashboard \")]))])]), _createCommentVNode(\" Loading State \"), $data.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, _cache[6] || (_cache[6] = [_createElementVNode(\"div\", {\n    class: \"loading-spinner\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-spinner fa-spin\"\n  })], -1 /* HOISTED */), _createElementVNode(\"p\", null, \"Loading available services...\", -1 /* HOISTED */)]))) : $data.error ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" Error State \"), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_cache[8] || (_cache[8] = _createElementVNode(\"i\", {\n    class: \"fas fa-exclamation-triangle\"\n  }, null, -1 /* HOISTED */)), _cache[9] || (_cache[9] = _createElementVNode(\"h3\", null, \"Unable to Load Services\", -1 /* HOISTED */)), _createElementVNode(\"p\", null, _toDisplayString($data.error), 1 /* TEXT */), _createElementVNode(\"button\", {\n    class: \"retry-btn\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.loadDocumentTypes && $options.loadDocumentTypes(...args))\n  }, _cache[7] || (_cache[7] = [_createElementVNode(\"i\", {\n    class: \"fas fa-redo\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Try Again \")]))])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 2\n  }, [_createCommentVNode(\" Document Types \"), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.documentTypes, documentType => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: documentType.id,\n      class: _normalizeClass([\"document-card\", {\n        'disabled': !documentType.is_active\n      }]),\n      onClick: $event => $options.selectDocumentType(documentType)\n    }, [_createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"i\", {\n      class: _normalizeClass($options.getDocumentIcon(documentType.type_name))\n    }, null, 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"h3\", _hoisted_12, _toDisplayString(documentType.type_name), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_13, _toDisplayString(documentType.description), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [_cache[10] || (_cache[10] = _createElementVNode(\"span\", {\n      class: \"fee-label\"\n    }, \"Base Fee:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_16, \"₱\" + _toDisplayString($options.formatCurrency(documentType.base_fee)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_17, [_cache[11] || (_cache[11] = _createElementVNode(\"i\", {\n      class: \"fas fa-clock\"\n    }, null, -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($options.getProcessingTime(documentType.type_name)), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_18, [!documentType.is_active ? (_openBlock(), _createElementBlock(\"span\", _hoisted_19, \" Unavailable \")) : (_openBlock(), _createElementBlock(\"i\", _hoisted_20))])], 10 /* CLASS, PROPS */, _hoisted_9);\n  }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" Information Section \"), _createElementVNode(\"div\", _hoisted_21, [_cache[16] || (_cache[16] = _createStaticVNode(\"<div class=\\\"info-card\\\" data-v-b2da9790><div class=\\\"info-header\\\" data-v-b2da9790><i class=\\\"fas fa-info-circle\\\" data-v-b2da9790></i><h3 data-v-b2da9790>Important Information</h3></div><div class=\\\"info-content\\\" data-v-b2da9790><ul class=\\\"info-list\\\" data-v-b2da9790><li data-v-b2da9790><i class=\\\"fas fa-check\\\" data-v-b2da9790></i> Ensure your profile information is complete and accurate </li><li data-v-b2da9790><i class=\\\"fas fa-check\\\" data-v-b2da9790></i> Have your valid ID and supporting documents ready </li><li data-v-b2da9790><i class=\\\"fas fa-check\\\" data-v-b2da9790></i> Processing time may vary depending on document verification </li><li data-v-b2da9790><i class=\\\"fas fa-check\\\" data-v-b2da9790></i> You can pay online using various payment methods </li></ul></div></div>\", 1)), _createElementVNode(\"div\", _hoisted_22, [_cache[15] || (_cache[15] = _createElementVNode(\"div\", {\n    class: \"help-header\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-headset\"\n  }), _createElementVNode(\"h3\", null, \"Need Help?\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_23, [_cache[14] || (_cache[14] = _createElementVNode(\"p\", null, \"If you have questions about document requirements or the application process:\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"button\", {\n    class: \"help-btn\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.openHelp && $options.openHelp(...args))\n  }, _cache[12] || (_cache[12] = [_createElementVNode(\"i\", {\n    class: \"fas fa-question-circle\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" View FAQ \")])), _createElementVNode(\"button\", {\n    class: \"contact-btn\",\n    onClick: _cache[3] || (_cache[3] = (...args) => $options.contactSupport && $options.contactSupport(...args))\n  }, _cache[13] || (_cache[13] = [_createElementVNode(\"i\", {\n    class: \"fas fa-phone\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Contact Support \")]))])])])])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))]);\n}", "map": {"version": 3, "names": ["_imports_0", "_imports_1", "class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "src", "alt", "_hoisted_2", "_hoisted_3", "onClick", "_cache", "args", "$options", "goBack", "$data", "loading", "_hoisted_4", "error", "_Fragment", "key", "_hoisted_5", "_hoisted_6", "_toDisplayString", "loadDocumentTypes", "_hoisted_7", "_hoisted_8", "_renderList", "documentTypes", "documentType", "id", "_normalizeClass", "is_active", "$event", "selectDocumentType", "_hoisted_10", "getDocumentIcon", "type_name", "_hoisted_11", "_hoisted_12", "_hoisted_13", "description", "_hoisted_14", "_hoisted_15", "_hoisted_16", "formatCurrency", "base_fee", "_hoisted_17", "getProcessingTime", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "openHelp", "contactSupport"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\client\\NewDocumentRequest.vue"], "sourcesContent": ["<template>\n  <div class=\"new-request-page\">\n    <!-- Background Image -->\n    <div class=\"background-container\">\n      <img\n        src=\"@/assets/bula-request-background-pic.png\"\n        alt=\"Barangay Bula background\"\n        class=\"background-image\"\n      />\n      <div class=\"background-overlay\"></div>\n    </div>\n\n    <!-- Header -->\n    <div class=\"page-header\">\n      <div class=\"header-content\">\n        <div class=\"header-main\">\n          <div class=\"logo-section\">\n            <img\n              src=\"@/assets/icon-of-bula.jpg\"\n              alt=\"Barangay Bula Official Seal\"\n              class=\"header-logo\"\n            />\n          </div>\n          <h1 class=\"page-title\">\n            <i class=\"fas fa-plus-circle\"></i>\n            New Document Request\n          </h1>\n          <p class=\"page-description\">\n            Choose the type of document you want to request from Barangay Bula\n          </p>\n        </div>\n        <button class=\"back-btn\" @click=\"goBack\">\n          <i class=\"fas fa-arrow-left\"></i>\n          Back to Dashboard\n        </button>\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div v-if=\"loading\" class=\"loading-container\">\n      <div class=\"loading-spinner\">\n        <i class=\"fas fa-spinner fa-spin\"></i>\n      </div>\n      <p>Loading available services...</p>\n    </div>\n\n    <!-- Error State -->\n    <div v-else-if=\"error\" class=\"error-container\">\n      <div class=\"error-content\">\n        <i class=\"fas fa-exclamation-triangle\"></i>\n        <h3>Unable to Load Services</h3>\n        <p>{{ error }}</p>\n        <button class=\"retry-btn\" @click=\"loadDocumentTypes\">\n          <i class=\"fas fa-redo\"></i>\n          Try Again\n        </button>\n      </div>\n    </div>\n\n    <!-- Document Types -->\n    <div v-else class=\"document-types-container\">\n      <div class=\"document-types-grid\">\n        <div\n          v-for=\"documentType in documentTypes\"\n          :key=\"documentType.id\"\n          class=\"document-card\"\n          @click=\"selectDocumentType(documentType)\"\n          :class=\"{ 'disabled': !documentType.is_active }\"\n        >\n          <div class=\"document-icon\">\n            <i :class=\"getDocumentIcon(documentType.type_name)\"></i>\n          </div>\n          \n          <div class=\"document-content\">\n            <h3 class=\"document-title\">{{ documentType.type_name }}</h3>\n            <p class=\"document-description\">{{ documentType.description }}</p>\n            \n            <div class=\"document-details\">\n              <div class=\"fee-info\">\n                <span class=\"fee-label\">Base Fee:</span>\n                <span class=\"fee-amount\">₱{{ formatCurrency(documentType.base_fee) }}</span>\n              </div>\n              \n              <div class=\"processing-time\">\n                <i class=\"fas fa-clock\"></i>\n                <span>{{ getProcessingTime(documentType.type_name) }}</span>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"document-action\">\n            <span v-if=\"!documentType.is_active\" class=\"status-badge unavailable\">\n              Unavailable\n            </span>\n            <i v-else class=\"fas fa-chevron-right\"></i>\n          </div>\n        </div>\n      </div>\n\n      <!-- Information Section -->\n      <div class=\"info-section\">\n        <div class=\"info-card\">\n          <div class=\"info-header\">\n            <i class=\"fas fa-info-circle\"></i>\n            <h3>Important Information</h3>\n          </div>\n          <div class=\"info-content\">\n            <ul class=\"info-list\">\n              <li>\n                <i class=\"fas fa-check\"></i>\n                Ensure your profile information is complete and accurate\n              </li>\n              <li>\n                <i class=\"fas fa-check\"></i>\n                Have your valid ID and supporting documents ready\n              </li>\n              <li>\n                <i class=\"fas fa-check\"></i>\n                Processing time may vary depending on document verification\n              </li>\n              <li>\n                <i class=\"fas fa-check\"></i>\n                You can pay online using various payment methods\n              </li>\n            </ul>\n          </div>\n        </div>\n\n        <div class=\"help-card\">\n          <div class=\"help-header\">\n            <i class=\"fas fa-headset\"></i>\n            <h3>Need Help?</h3>\n          </div>\n          <div class=\"help-content\">\n            <p>If you have questions about document requirements or the application process:</p>\n            <div class=\"help-actions\">\n              <button class=\"help-btn\" @click=\"openHelp\">\n                <i class=\"fas fa-question-circle\"></i>\n                View FAQ\n              </button>\n              <button class=\"contact-btn\" @click=\"contactSupport\">\n                <i class=\"fas fa-phone\"></i>\n                Contact Support\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport documentRequestService from '@/services/documentRequestService';\n\nexport default {\n  name: 'NewDocumentRequest',\n  data() {\n    return {\n      documentTypes: [],\n      loading: true,\n      error: null\n    };\n  },\n  async mounted() {\n    await this.loadDocumentTypes();\n  },\n  methods: {\n    async loadDocumentTypes() {\n      try {\n        this.loading = true;\n        this.error = null;\n        \n        const response = await documentRequestService.getDocumentTypes();\n        this.documentTypes = response.data || [];\n        \n      } catch (error) {\n        console.error('Error loading document types:', error);\n        this.error = error.response?.data?.message || 'Failed to load available services';\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    selectDocumentType(documentType) {\n      if (!documentType.is_active) return;\n\n      // Navigate to specific document request form\n      const routeName = this.getRouteForDocumentType(documentType.type_name);\n      if (routeName) {\n        this.$router.push({ \n          name: routeName,\n          params: { documentTypeId: documentType.id }\n        });\n      }\n    },\n\n    getRouteForDocumentType(typeName) {\n      const routes = {\n        'Barangay Clearance': 'BarangayClearanceRequest',\n        'Cedula': 'CedulaRequest'\n      };\n      return routes[typeName];\n    },\n\n    getDocumentIcon(typeName) {\n      const icons = {\n        'Barangay Clearance': 'fas fa-certificate',\n        'Cedula': 'fas fa-id-card'\n      };\n      return icons[typeName] || 'fas fa-file-alt';\n    },\n\n    getProcessingTime(typeName) {\n      const times = {\n        'Barangay Clearance': '3-5 business days',\n        'Cedula': '1-2 business days'\n      };\n      return times[typeName] || '3-5 business days';\n    },\n\n    formatCurrency(amount) {\n      return parseFloat(amount).toFixed(2);\n    },\n\n    goBack() {\n      this.$router.push({ name: 'ClientDashboard' });\n    },\n\n    openHelp() {\n      // TODO: Implement help/FAQ modal or page\n      console.log('Opening help...');\n    },\n\n    contactSupport() {\n      // TODO: Implement contact support functionality\n      console.log('Contacting support...');\n    }\n  }\n};\n</script>\n\n<style scoped>\n.new-request-page {\n  padding: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.page-header {\n  margin-bottom: 2rem;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  gap: 2rem;\n}\n\n.page-title {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #1a365d;\n  margin-bottom: 0.5rem;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.page-title i {\n  color: #3182ce;\n}\n\n.page-description {\n  font-size: 1.1rem;\n  color: #4a5568;\n  margin: 0;\n}\n\n.back-btn {\n  background: #e2e8f0;\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 0.5rem;\n  color: #4a5568;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.back-btn:hover {\n  background: #cbd5e0;\n  color: #2d3748;\n}\n\n.loading-container, .error-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n  text-align: center;\n}\n\n.loading-spinner i {\n  font-size: 3rem;\n  color: #3182ce;\n  margin-bottom: 1rem;\n}\n\n.error-content {\n  max-width: 400px;\n}\n\n.error-content i {\n  font-size: 3rem;\n  color: #e53e3e;\n  margin-bottom: 1rem;\n}\n\n.retry-btn {\n  background: #3182ce;\n  color: white;\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 0.5rem;\n  cursor: pointer;\n  margin-top: 1rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.document-types-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n  gap: 1.5rem;\n  margin-bottom: 3rem;\n}\n\n.document-card {\n  background: white;\n  border: 2px solid #e2e8f0;\n  border-radius: 1rem;\n  padding: 2rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n}\n\n.document-card:hover:not(.disabled) {\n  border-color: #3182ce;\n  transform: translateY(-2px);\n  box-shadow: 0 10px 25px rgba(49, 130, 206, 0.1);\n}\n\n.document-card.disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.document-icon {\n  flex-shrink: 0;\n  width: 4rem;\n  height: 4rem;\n  background: linear-gradient(135deg, #3182ce, #2c5aa0);\n  border-radius: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 1.5rem;\n}\n\n.document-content {\n  flex: 1;\n}\n\n.document-title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 0.5rem;\n}\n\n.document-description {\n  color: #4a5568;\n  margin-bottom: 1rem;\n  line-height: 1.5;\n}\n\n.document-details {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.fee-info {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.fee-label {\n  color: #718096;\n  font-size: 0.9rem;\n}\n\n.fee-amount {\n  font-weight: 600;\n  color: #38a169;\n  font-size: 1.1rem;\n}\n\n.processing-time {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #718096;\n  font-size: 0.9rem;\n}\n\n.document-action {\n  flex-shrink: 0;\n  display: flex;\n  align-items: center;\n}\n\n.document-action i {\n  color: #a0aec0;\n  font-size: 1.25rem;\n}\n\n.status-badge.unavailable {\n  background: #fed7d7;\n  color: #c53030;\n  padding: 0.25rem 0.75rem;\n  border-radius: 1rem;\n  font-size: 0.8rem;\n  font-weight: 500;\n}\n\n.info-section {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 2rem;\n}\n\n.info-card, .help-card {\n  background: white;\n  border: 1px solid #e2e8f0;\n  border-radius: 0.75rem;\n  padding: 1.5rem;\n}\n\n.info-header, .help-header {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  margin-bottom: 1rem;\n}\n\n.info-header h3, .help-header h3 {\n  font-size: 1.1rem;\n  font-weight: 600;\n  color: #1a365d;\n  margin: 0;\n}\n\n.info-header i {\n  color: #3182ce;\n}\n\n.help-header i {\n  color: #38a169;\n}\n\n.info-list {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.info-list li {\n  display: flex;\n  align-items: flex-start;\n  gap: 0.75rem;\n  margin-bottom: 0.75rem;\n  color: #4a5568;\n  line-height: 1.5;\n}\n\n.info-list i {\n  color: #38a169;\n  margin-top: 0.125rem;\n  flex-shrink: 0;\n}\n\n.help-content p {\n  color: #4a5568;\n  margin-bottom: 1rem;\n  line-height: 1.5;\n}\n\n.help-actions {\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n}\n\n.help-btn, .contact-btn {\n  background: transparent;\n  border: 1px solid #e2e8f0;\n  padding: 0.75rem 1rem;\n  border-radius: 0.5rem;\n  cursor: pointer;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #4a5568;\n}\n\n.help-btn:hover {\n  border-color: #3182ce;\n  color: #3182ce;\n}\n\n.contact-btn:hover {\n  border-color: #38a169;\n  color: #38a169;\n}\n\n@media (max-width: 768px) {\n  .new-request-page {\n    padding: 1rem;\n  }\n  \n  .header-content {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .document-types-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .document-card {\n    flex-direction: column;\n    text-align: center;\n  }\n  \n  .info-section {\n    grid-template-columns: 1fr;\n  }\n}\n</style>\n"], "mappings": ";OAKQA,UAA8C;OAaxCC,UAA+B;;EAjBtCC,KAAK,EAAC;AAAkB;;EAYtBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAgB;;;EAyBTA,KAAK,EAAC;;;EAQHA,KAAK,EAAC;AAAiB;;EACvCA,KAAK,EAAC;AAAe;;EAYhBA,KAAK,EAAC;AAA0B;;EACrCA,KAAK,EAAC;AAAqB;;;EAQvBA,KAAK,EAAC;AAAe;;EAIrBA,KAAK,EAAC;AAAkB;;EACvBA,KAAK,EAAC;AAAgB;;EACvBA,KAAK,EAAC;AAAsB;;EAE1BA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAU;;EAEbA,KAAK,EAAC;AAAY;;EAGrBA,KAAK,EAAC;AAAiB;;EAO3BA,KAAK,EAAC;AAAiB;;;EACWA,KAAK,EAAC;;;;EAGjCA,KAAK,EAAC;;;EAMjBA,KAAK,EAAC;AAAc;;EA4BlBA,KAAK,EAAC;AAAW;;EAKfA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAc;;uBAtInCC,mBAAA,CAoJM,OApJNC,UAoJM,GAnJJC,mBAAA,sBAAyB,E,4BACzBC,mBAAA,CAOM;IAPDJ,KAAK,EAAC;EAAsB,IAC/BI,mBAAA,CAIE;IAHAC,GAA8C,EAA9CP,UAA8C;IAC9CQ,GAAG,EAAC,0BAA0B;IAC9BN,KAAK,EAAC;MAERI,mBAAA,CAAsC;IAAjCJ,KAAK,EAAC;EAAoB,G,sBAGjCG,mBAAA,YAAe,EACfC,mBAAA,CAuBM,OAvBNG,UAuBM,GAtBJH,mBAAA,CAqBM,OArBNI,UAqBM,G,ifAJJJ,mBAAA,CAGS;IAHDJ,KAAK,EAAC,UAAU;IAAES,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,MAAA,IAAAD,QAAA,CAAAC,MAAA,IAAAF,IAAA,CAAM;gCACrCP,mBAAA,CAAiC;IAA9BJ,KAAK,EAAC;EAAmB,4B,iBAAK,qBAEnC,E,QAIJG,mBAAA,mBAAsB,EACXW,KAAA,CAAAC,OAAO,I,cAAlBd,mBAAA,CAKM,OALNe,UAKM,EAAAN,MAAA,QAAAA,MAAA,OAJJN,mBAAA,CAEM;IAFDJ,KAAK,EAAC;EAAiB,IAC1BI,mBAAA,CAAsC;IAAnCJ,KAAK,EAAC;EAAwB,G,qBAEnCI,mBAAA,CAAoC,WAAjC,+BAA6B,oB,MAIlBU,KAAA,CAAAG,KAAK,I,cAArBhB,mBAAA,CAUMiB,SAAA;IAAAC,GAAA;EAAA,IAXNhB,mBAAA,iBAAoB,EACpBC,mBAAA,CAUM,OAVNgB,UAUM,GATJhB,mBAAA,CAQM,OARNiB,UAQM,G,0BAPJjB,mBAAA,CAA2C;IAAxCJ,KAAK,EAAC;EAA6B,6B,0BACtCI,mBAAA,CAAgC,YAA5B,yBAAuB,sBAC3BA,mBAAA,CAAkB,WAAAkB,gBAAA,CAAZR,KAAA,CAAAG,KAAK,kBACXb,mBAAA,CAGS;IAHDJ,KAAK,EAAC,WAAW;IAAES,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAW,iBAAA,IAAAX,QAAA,CAAAW,iBAAA,IAAAZ,IAAA,CAAiB;gCACjDP,mBAAA,CAA2B;IAAxBJ,KAAK,EAAC;EAAa,4B,iBAAK,aAE7B,E,0EAKJC,mBAAA,CAwFMiB,SAAA;IAAAC,GAAA;EAAA,IAzFNhB,mBAAA,oBAAuB,EACvBC,mBAAA,CAwFM,OAxFNoB,UAwFM,GAvFJpB,mBAAA,CAoCM,OApCNqB,UAoCM,I,kBAnCJxB,mBAAA,CAkCMiB,SAAA,QAAAQ,WAAA,CAjCmBZ,KAAA,CAAAa,aAAa,EAA7BC,YAAY;yBADrB3B,mBAAA,CAkCM;MAhCHkB,GAAG,EAAES,YAAY,CAACC,EAAE;MACrB7B,KAAK,EAAA8B,eAAA,EAAC,eAAe;QAAA,aAEEF,YAAY,CAACG;MAAS;MAD5CtB,OAAK,EAAAuB,MAAA,IAAEpB,QAAA,CAAAqB,kBAAkB,CAACL,YAAY;QAGvCxB,mBAAA,CAEM,OAFN8B,WAEM,GADJ9B,mBAAA,CAAwD;MAApDJ,KAAK,EAAA8B,eAAA,CAAElB,QAAA,CAAAuB,eAAe,CAACP,YAAY,CAACQ,SAAS;+BAGnDhC,mBAAA,CAeM,OAfNiC,WAeM,GAdJjC,mBAAA,CAA4D,MAA5DkC,WAA4D,EAAAhB,gBAAA,CAA9BM,YAAY,CAACQ,SAAS,kBACpDhC,mBAAA,CAAkE,KAAlEmC,WAAkE,EAAAjB,gBAAA,CAA/BM,YAAY,CAACY,WAAW,kBAE3DpC,mBAAA,CAUM,OAVNqC,WAUM,GATJrC,mBAAA,CAGM,OAHNsC,WAGM,G,4BAFJtC,mBAAA,CAAwC;MAAlCJ,KAAK,EAAC;IAAW,GAAC,WAAS,sBACjCI,mBAAA,CAA4E,QAA5EuC,WAA4E,EAAnD,GAAC,GAAArB,gBAAA,CAAGV,QAAA,CAAAgC,cAAc,CAAChB,YAAY,CAACiB,QAAQ,kB,GAGnEzC,mBAAA,CAGM,OAHN0C,WAGM,G,4BAFJ1C,mBAAA,CAA4B;MAAzBJ,KAAK,EAAC;IAAc,6BACvBI,mBAAA,CAA4D,cAAAkB,gBAAA,CAAnDV,QAAA,CAAAmC,iBAAiB,CAACnB,YAAY,CAACQ,SAAS,kB,OAKvDhC,mBAAA,CAKM,OALN4C,WAKM,G,CAJSpB,YAAY,CAACG,SAAS,I,cAAnC9B,mBAAA,CAEO,QAFPgD,WAEO,EAF+D,eAEtE,M,cACAhD,mBAAA,CAA2C,KAA3CiD,WAA2C,G;oCAKjD/C,mBAAA,yBAA4B,EAC5BC,mBAAA,CA+CM,OA/CN+C,WA+CM,G,i1BAnBJ/C,mBAAA,CAkBM,OAlBNgD,WAkBM,G,4BAjBJhD,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAA8B;IAA3BJ,KAAK,EAAC;EAAgB,IACzBI,mBAAA,CAAmB,YAAf,YAAU,E,sBAEhBA,mBAAA,CAYM,OAZNiD,WAYM,G,4BAXJjD,mBAAA,CAAoF,WAAjF,+EAA6E,sBAChFA,mBAAA,CASM,OATNkD,WASM,GARJlD,mBAAA,CAGS;IAHDJ,KAAK,EAAC,UAAU;IAAES,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAA2C,QAAA,IAAA3C,QAAA,CAAA2C,QAAA,IAAA5C,IAAA,CAAQ;kCACvCP,mBAAA,CAAsC;IAAnCJ,KAAK,EAAC;EAAwB,4B,iBAAK,YAExC,E,IACAI,mBAAA,CAGS;IAHDJ,KAAK,EAAC,aAAa;IAAES,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAA4C,cAAA,IAAA5C,QAAA,CAAA4C,cAAA,IAAA7C,IAAA,CAAc;kCAChDP,mBAAA,CAA4B;IAAzBJ,KAAK,EAAC;EAAc,4B,iBAAK,mBAE9B,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}