{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, reactive, computed, onMounted } from 'vue';\nimport { useRouter } from 'vue-router';\nimport { useUserStore } from '@/stores/userStore';\nimport { useDocumentStore } from '@/stores/documentStore';\nimport { useNotificationStore } from '@/stores/notificationStore';\n\n// Components\nimport ClientHeader from './ClientHeader.vue';\nimport PageHeader from './components/PageHeader.vue';\nimport UserStatsSection from './components/UserStatsSection.vue';\nimport QuickActionsSection from './components/QuickActionsSection.vue';\nimport DocumentServicesSection from './components/DocumentServicesSection.vue';\nimport InformationSection from './components/InformationSection.vue';\nimport HelpModal from './components/HelpModal.vue';\nimport SupportModal from './components/SupportModal.vue';\n\n// Composables\nimport { useDocumentTypes } from '@/composables/useDocumentTypes';\nimport { useUserStats } from '@/composables/useUserStats';\nimport { useNavigation } from '@/composables/useNavigation';\n\n// Router and stores\n\nexport default {\n  __name: 'NewDocumentRequest',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const router = useRouter();\n    const userStore = useUserStore();\n    const documentStore = useDocumentStore();\n    const notificationStore = useNotificationStore();\n\n    // Reactive state\n    const uiState = reactive({\n      showUserDropdown: false,\n      showHelpModal: false,\n      showSupportModal: false\n    });\n\n    // User state\n    const userState = computed(() => ({\n      name: userStore.currentUser?.username || 'User',\n      email: userStore.currentUser?.email || '<EMAIL>',\n      avatar: userStore.currentUser?.avatar || null,\n      firstName: userStore.currentUser?.first_name || userStore.currentUser?.username || 'User'\n    }));\n\n    // Breadcrumbs\n    const breadcrumbs = computed(() => [{\n      label: 'Dashboard',\n      href: '/dashboard',\n      icon: 'fas fa-home'\n    }, {\n      label: 'Document Services',\n      current: true\n    }]);\n\n    // Composables\n    const {\n      documentTypes,\n      loadDocumentTypes\n    } = useDocumentTypes();\n    const {\n      userStats\n    } = useUserStats();\n    const {\n      navigateToRequests,\n      navigateToProfile,\n      navigateToDocument\n    } = useNavigation();\n\n    // Refs\n    const servicesSection = ref(null);\n\n    // Methods\n    const toggleUserDropdown = () => {\n      uiState.showUserDropdown = !uiState.showUserDropdown;\n    };\n    const handleMenuAction = action => {\n      switch (action) {\n        case 'dashboard':\n          router.push({\n            name: 'ClientDashboard'\n          });\n          break;\n        case 'services':\n          scrollToServices();\n          break;\n        case 'requests':\n          navigateToRequests();\n          break;\n        case 'profile':\n          navigateToProfile();\n          break;\n        case 'help':\n          openHelpModal();\n          break;\n        default:\n          console.log('Menu action:', action);\n      }\n    };\n    const handleLogout = async () => {\n      try {\n        await userStore.logout();\n        router.push({\n          name: 'WelcomePage'\n        });\n      } catch (error) {\n        console.error('Logout error:', error);\n        notificationStore.addNotification({\n          type: 'error',\n          title: 'Logout Failed',\n          message: 'Unable to logout. Please try again.'\n        });\n      }\n    };\n    const handleError = error => {\n      console.error('Component error:', error);\n      notificationStore.addNotification({\n        type: 'error',\n        title: 'Error',\n        message: error.message || 'An unexpected error occurred'\n      });\n    };\n    const handleSearch = query => {\n      // TODO: Implement search functionality\n      console.log('Search query:', query);\n      // Could search through documents, services, or requests\n    };\n    const scrollToServices = () => {\n      servicesSection.value?.$el?.scrollIntoView({\n        behavior: 'smooth'\n      });\n    };\n    const selectDocumentType = documentType => {\n      if (!documentType.is_active) {\n        notificationStore.addNotification({\n          type: 'warning',\n          title: 'Service Unavailable',\n          message: `${documentType.type_name} service is currently unavailable.`\n        });\n        return;\n      }\n      navigateToDocument(documentType);\n    };\n    const openHelpModal = () => {\n      uiState.showHelpModal = true;\n    };\n    const closeHelpModal = () => {\n      uiState.showHelpModal = false;\n    };\n    const openSupportModal = () => {\n      uiState.showSupportModal = true;\n    };\n    const closeSupportModal = () => {\n      uiState.showSupportModal = false;\n    };\n\n    // Lifecycle\n    onMounted(async () => {\n      try {\n        await Promise.all([userStore.loadCurrentUser(), loadDocumentTypes(), userStats.load()]);\n      } catch (error) {\n        console.error('Failed to load initial data:', error);\n        handleError(error);\n      }\n    });\n    const __returned__ = {\n      router,\n      userStore,\n      documentStore,\n      notificationStore,\n      uiState,\n      userState,\n      breadcrumbs,\n      documentTypes,\n      loadDocumentTypes,\n      userStats,\n      navigateToRequests,\n      navigateToProfile,\n      navigateToDocument,\n      servicesSection,\n      toggleUserDropdown,\n      handleMenuAction,\n      handleLogout,\n      handleError,\n      handleSearch,\n      scrollToServices,\n      selectDocumentType,\n      openHelpModal,\n      closeHelpModal,\n      openSupportModal,\n      closeSupportModal,\n      ref,\n      reactive,\n      computed,\n      onMounted,\n      get useRouter() {\n        return useRouter;\n      },\n      get useUserStore() {\n        return useUserStore;\n      },\n      get useDocumentStore() {\n        return useDocumentStore;\n      },\n      get useNotificationStore() {\n        return useNotificationStore;\n      },\n      ClientHeader,\n      PageHeader,\n      UserStatsSection,\n      QuickActionsSection,\n      DocumentServicesSection,\n      InformationSection,\n      HelpModal,\n      SupportModal,\n      get useDocumentTypes() {\n        return useDocumentTypes;\n      },\n      get useUserStats() {\n        return useUserStats;\n      },\n      get useNavigation() {\n        return useNavigation;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "computed", "onMounted", "useRouter", "useUserStore", "useDocumentStore", "useNotificationStore", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "UserStatsSection", "QuickActionsSection", "DocumentServicesSection", "InformationSection", "HelpModal", "SupportModal", "useDocumentTypes", "useUserStats", "useNavigation", "router", "userStore", "documentStore", "notificationStore", "uiState", "showUserDropdown", "showHelpModal", "showSupportModal", "userState", "name", "currentUser", "username", "email", "avatar", "firstName", "first_name", "breadcrumbs", "label", "href", "icon", "current", "documentTypes", "loadDocumentTypes", "userStats", "navigateToRequests", "navigateToProfile", "navigateToDocument", "servicesSection", "toggleUserDropdown", "handleMenuAction", "action", "push", "scrollToServices", "openHelpModal", "console", "log", "handleLogout", "logout", "error", "addNotification", "type", "title", "message", "handleError", "handleSearch", "query", "value", "$el", "scrollIntoView", "behavior", "selectDocumentType", "documentType", "is_active", "type_name", "closeHelpModal", "openSupportModal", "closeSupportModal", "Promise", "all", "loadCurrentUser", "load"], "sources": ["D:/cap2_rhai_front_and_back/BOSFDR/src/components/client/NewDocumentRequest.vue"], "sourcesContent": ["<template>\n  <div class=\"document-request-page\">\n    <!-- Skip to main content link for accessibility -->\n    <a href=\"#main-content\" class=\"skip-link\">Skip to main content</a>\n\n    <!-- Client Header with Navigation -->\n    <ClientHeader\n      :user-name=\"userState.name\"\n      :user-email=\"userState.email\"\n      :user-avatar=\"userState.avatar\"\n      :show-user-dropdown=\"uiState.showUserDropdown\"\n      :active-menu=\"'services'\"\n      :show-breadcrumbs=\"true\"\n      @user-dropdown-toggle=\"toggleUserDropdown\"\n      @menu-action=\"handleMenuAction\"\n      @logout=\"handleLogout\"\n      @error=\"handleError\"\n      @search=\"handleSearch\"\n    />\n\n    <!-- Main Content -->\n    <main id=\"main-content\" class=\"main-content\" role=\"main\">\n      <!-- Page Header -->\n      <PageHeader\n        title=\"Document Request Services\"\n        subtitle=\"Request official documents from Barangay Bula through our secure digital platform\"\n        :breadcrumbs=\"breadcrumbs\"\n      />\n\n      <!-- User Dashboard Stats -->\n      <UserStatsSection\n        :total-requests=\"userStats.totalRequests\"\n        :pending-requests=\"userStats.pendingRequests\"\n        :completed-requests=\"userStats.completedRequests\"\n        :loading=\"userStats.loading\"\n        @view-requests=\"navigateToRequests\"\n      />\n\n      <!-- Quick Actions -->\n      <QuickActionsSection\n        @new-request=\"scrollToServices\"\n        @view-requests=\"navigateToRequests\"\n        @view-profile=\"navigateToProfile\"\n        @contact-support=\"openSupportModal\"\n      />\n\n      <!-- Document Services -->\n      <DocumentServicesSection\n        ref=\"servicesSection\"\n        :document-types=\"documentTypes.data\"\n        :loading=\"documentTypes.loading\"\n        :error=\"documentTypes.error\"\n        @select-document=\"selectDocumentType\"\n        @retry-load=\"loadDocumentTypes\"\n      />\n\n      <!-- Information Section -->\n      <InformationSection\n        @open-help=\"openHelpModal\"\n        @contact-support=\"openSupportModal\"\n      />\n    </main>\n\n    <!-- Modals -->\n    <HelpModal\n      v-if=\"uiState.showHelpModal\"\n      @close=\"closeHelpModal\"\n    />\n\n    <SupportModal\n      v-if=\"uiState.showSupportModal\"\n      @close=\"closeSupportModal\"\n    />\n  </div>\n\n\n\n\n\n\n</template>\n\n<script setup>\nimport { ref, reactive, computed, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { useUserStore } from '@/stores/userStore'\nimport { useDocumentStore } from '@/stores/documentStore'\nimport { useNotificationStore } from '@/stores/notificationStore'\n\n// Components\nimport ClientHeader from './ClientHeader.vue'\nimport PageHeader from './components/PageHeader.vue'\nimport UserStatsSection from './components/UserStatsSection.vue'\nimport QuickActionsSection from './components/QuickActionsSection.vue'\nimport DocumentServicesSection from './components/DocumentServicesSection.vue'\nimport InformationSection from './components/InformationSection.vue'\nimport HelpModal from './components/HelpModal.vue'\nimport SupportModal from './components/SupportModal.vue'\n\n// Composables\nimport { useDocumentTypes } from '@/composables/useDocumentTypes'\nimport { useUserStats } from '@/composables/useUserStats'\nimport { useNavigation } from '@/composables/useNavigation'\n\n// Router and stores\nconst router = useRouter()\nconst userStore = useUserStore()\nconst documentStore = useDocumentStore()\nconst notificationStore = useNotificationStore()\n\n// Reactive state\nconst uiState = reactive({\n  showUserDropdown: false,\n  showHelpModal: false,\n  showSupportModal: false\n})\n\n// User state\nconst userState = computed(() => ({\n  name: userStore.currentUser?.username || 'User',\n  email: userStore.currentUser?.email || '<EMAIL>',\n  avatar: userStore.currentUser?.avatar || null,\n  firstName: userStore.currentUser?.first_name || userStore.currentUser?.username || 'User'\n}))\n\n// Breadcrumbs\nconst breadcrumbs = computed(() => [\n  { label: 'Dashboard', href: '/dashboard', icon: 'fas fa-home' },\n  { label: 'Document Services', current: true }\n])\n\n// Composables\nconst { documentTypes, loadDocumentTypes } = useDocumentTypes()\nconst { userStats } = useUserStats()\nconst { navigateToRequests, navigateToProfile, navigateToDocument } = useNavigation()\n\n// Refs\nconst servicesSection = ref(null)\n\n// Methods\nconst toggleUserDropdown = () => {\n  uiState.showUserDropdown = !uiState.showUserDropdown\n}\n\nconst handleMenuAction = (action) => {\n  switch (action) {\n    case 'dashboard':\n      router.push({ name: 'ClientDashboard' })\n      break\n    case 'services':\n      scrollToServices()\n      break\n    case 'requests':\n      navigateToRequests()\n      break\n    case 'profile':\n      navigateToProfile()\n      break\n    case 'help':\n      openHelpModal()\n      break\n    default:\n      console.log('Menu action:', action)\n  }\n}\n\nconst handleLogout = async () => {\n  try {\n    await userStore.logout()\n    router.push({ name: 'WelcomePage' })\n  } catch (error) {\n    console.error('Logout error:', error)\n    notificationStore.addNotification({\n      type: 'error',\n      title: 'Logout Failed',\n      message: 'Unable to logout. Please try again.'\n    })\n  }\n}\n\nconst handleError = (error) => {\n  console.error('Component error:', error)\n  notificationStore.addNotification({\n    type: 'error',\n    title: 'Error',\n    message: error.message || 'An unexpected error occurred'\n  })\n}\n\nconst handleSearch = (query) => {\n  // TODO: Implement search functionality\n  console.log('Search query:', query)\n  // Could search through documents, services, or requests\n}\n\nconst scrollToServices = () => {\n  servicesSection.value?.$el?.scrollIntoView({ behavior: 'smooth' })\n}\n\nconst selectDocumentType = (documentType) => {\n  if (!documentType.is_active) {\n    notificationStore.addNotification({\n      type: 'warning',\n      title: 'Service Unavailable',\n      message: `${documentType.type_name} service is currently unavailable.`\n    })\n    return\n  }\n\n  navigateToDocument(documentType)\n}\n\nconst openHelpModal = () => {\n  uiState.showHelpModal = true\n}\n\nconst closeHelpModal = () => {\n  uiState.showHelpModal = false\n}\n\nconst openSupportModal = () => {\n  uiState.showSupportModal = true\n}\n\nconst closeSupportModal = () => {\n  uiState.showSupportModal = false\n}\n\n// Lifecycle\nonMounted(async () => {\n  try {\n    await Promise.all([\n      userStore.loadCurrentUser(),\n      loadDocumentTypes(),\n      userStats.load()\n    ])\n  } catch (error) {\n    console.error('Failed to load initial data:', error)\n    handleError(error)\n  }\n})\n</script>\n\n<style scoped>\n/* Modern Government Portal Styles - USWDS 3.0 Inspired */\n\n/* Skip link for accessibility */\n.skip-link {\n  position: absolute;\n  top: -40px;\n  left: 6px;\n  background: var(--uswds-color-primary-darker);\n  color: white;\n  padding: 8px;\n  text-decoration: none;\n  border-radius: 0 0 4px 4px;\n  z-index: 1000;\n  font-weight: 600;\n}\n\n.skip-link:focus {\n  top: 0;\n}\n\n/* Main page container */\n.document-request-page {\n  min-height: 100vh;\n  background: var(--uswds-color-base-lightest);\n  font-family: var(--uswds-font-family-sans);\n  line-height: 1.6;\n  color: var(--uswds-color-ink);\n}\n\n/* Main content area */\n.main-content {\n  margin-top: var(--header-height, 140px);\n  padding-bottom: var(--uswds-spacing-8);\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .main-content {\n    margin-top: var(--header-height-mobile, 120px);\n    padding: 0 var(--uswds-spacing-2);\n  }\n}\n\n/* Focus management for accessibility */\n.main-content:focus {\n  outline: 2px solid var(--uswds-color-focus);\n  outline-offset: 2px;\n}\n\n/* Animation for smooth page transitions */\n.document-request-page {\n  animation: fadeIn 0.3s ease-in-out;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Reduced motion support */\n@media (prefers-reduced-motion: reduce) {\n  .document-request-page {\n    animation: none;\n  }\n\n  * {\n    transition: none !important;\n    animation: none !important;\n  }\n}\n\n\n\n\n\n\n\n/* Info Section */\n.info-section {\n  padding: 2rem 0;\n  position: relative;\n  z-index: 1;\n}\n\n.info-grid {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 2rem;\n}\n\n.info-card, .help-card {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(30, 58, 138, 0.2);\n  border-radius: 1rem;\n  padding: 2rem;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n}\n\n.info-card:hover, .help-card:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 8px 30px rgba(30, 58, 138, 0.15);\n  background: rgba(255, 255, 255, 1);\n  border-color: #1e3a8a;\n}\n\n.info-header, .help-header {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  margin-bottom: 1.5rem;\n}\n\n.info-header h3, .help-header h3 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1e3a8a;\n  margin: 0;\n}\n\n.info-header i {\n  color: #1e3a8a;\n  font-size: 1.25rem;\n}\n\n.help-header i {\n  color: #059669;\n  font-size: 1.25rem;\n}\n\n.info-list {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.info-list li {\n  display: flex;\n  align-items: flex-start;\n  gap: 0.75rem;\n  margin-bottom: 1rem;\n  color: #6b7280;\n  line-height: 1.6;\n}\n\n.info-list i {\n  color: #059669;\n  margin-top: 0.125rem;\n  flex-shrink: 0;\n}\n\n.help-content p {\n  color: #6b7280;\n  margin-bottom: 1.5rem;\n  line-height: 1.6;\n}\n\n.help-actions {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.help-btn, .contact-btn {\n  background: rgba(255, 255, 255, 0.9);\n  backdrop-filter: blur(5px);\n  border: 2px solid rgba(30, 58, 138, 0.3);\n  padding: 0.875rem 1.25rem;\n  border-radius: 0.75rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  color: #6b7280;\n  font-weight: 500;\n  font-size: 0.95rem;\n}\n\n.help-btn:hover {\n  border-color: #1e3a8a;\n  color: #1e3a8a;\n  background: rgba(30, 58, 138, 0.1);\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.2);\n}\n\n.contact-btn:hover {\n  border-color: #059669;\n  color: #059669;\n  background: rgba(5, 150, 105, 0.1);\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(5, 150, 105, 0.2);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .container {\n    padding: 0 0.75rem;\n  }\n\n  .welcome-header {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n    padding: 1.5rem;\n  }\n\n  .welcome-stats {\n    justify-content: center;\n  }\n\n  .stat-card {\n    min-width: 120px;\n  }\n\n  .quick-actions-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n\n  .action-card {\n    padding: 1.5rem;\n  }\n\n  .document-types-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n\n  .document-card {\n    flex-direction: column;\n    text-align: center;\n    padding: 1.5rem;\n  }\n\n  .info-grid {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n\n  .info-card, .help-card {\n    padding: 1.5rem;\n  }\n\n  .loading-container, .error-container {\n    padding: 3rem 1.5rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .container {\n    padding: 0 0.5rem;\n  }\n\n  .welcome-section,\n  .quick-actions-section,\n  .services-section,\n  .info-section {\n    padding: 1.5rem 0;\n  }\n\n  .welcome-header {\n    padding: 1rem;\n  }\n\n  .stat-card {\n    padding: 1rem;\n  }\n\n  .stat-number {\n    font-size: 1.5rem;\n  }\n\n  .action-card {\n    padding: 1rem;\n    flex-direction: column;\n    text-align: center;\n    gap: 1rem;\n  }\n\n  .action-icon {\n    width: 3rem;\n    height: 3rem;\n  }\n\n  .document-card {\n    padding: 1rem;\n  }\n\n  .info-card, .help-card {\n    padding: 1rem;\n  }\n\n  .loading-container, .error-container {\n    padding: 2rem 1rem;\n  }\n}\n\n/* Animations */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.welcome-section,\n.quick-actions-section,\n.services-section,\n.info-section {\n  animation: fadeInUp 0.8s ease-out;\n}\n\n.quick-actions-section {\n  animation-delay: 0.2s;\n}\n\n.services-section {\n  animation-delay: 0.4s;\n}\n\n.info-section {\n  animation-delay: 0.6s;\n}\n\n/* Accessibility */\n@media (prefers-reduced-motion: reduce) {\n  * {\n    animation: none !important;\n    transition: none !important;\n  }\n\n  .action-card:hover,\n  .document-card:hover,\n  .info-card:hover,\n  .help-card:hover {\n    transform: none;\n  }\n}\n\n/* Focus styles */\n.action-card:focus,\n.document-card:focus,\n.help-btn:focus,\n.contact-btn:focus,\n.retry-btn:focus {\n  outline: 3px solid #fbbf24;\n  outline-offset: 2px;\n}\n</style>\n"], "mappings": ";AAmFA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,KAAI;AACvD,SAASC,SAAS,QAAQ,YAAW;AACrC,SAASC,YAAY,QAAQ,oBAAmB;AAChD,SAASC,gBAAgB,QAAQ,wBAAuB;AACxD,SAASC,oBAAoB,QAAQ,4BAA2B;;AAEhE;AACA,OAAOC,YAAY,MAAM,oBAAmB;AAC5C,OAAOC,UAAU,MAAM,6BAA4B;AACnD,OAAOC,gBAAgB,MAAM,mCAAkC;AAC/D,OAAOC,mBAAmB,MAAM,sCAAqC;AACrE,OAAOC,uBAAuB,MAAM,0CAAyC;AAC7E,OAAOC,kBAAkB,MAAM,qCAAoC;AACnE,OAAOC,SAAS,MAAM,4BAA2B;AACjD,OAAOC,YAAY,MAAM,+BAA8B;;AAEvD;AACA,SAASC,gBAAgB,QAAQ,gCAA+B;AAChE,SAASC,YAAY,QAAQ,4BAA2B;AACxD,SAASC,aAAa,QAAQ,6BAA4B;;AAE1D;;;;;;;;IACA,MAAMC,MAAM,GAAGf,SAAS,CAAC;IACzB,MAAMgB,SAAS,GAAGf,YAAY,CAAC;IAC/B,MAAMgB,aAAa,GAAGf,gBAAgB,CAAC;IACvC,MAAMgB,iBAAiB,GAAGf,oBAAoB,CAAC;;IAE/C;IACA,MAAMgB,OAAO,GAAGtB,QAAQ,CAAC;MACvBuB,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE,KAAK;MACpBC,gBAAgB,EAAE;IACpB,CAAC;;IAED;IACA,MAAMC,SAAS,GAAGzB,QAAQ,CAAC,OAAO;MAChC0B,IAAI,EAAER,SAAS,CAACS,WAAW,EAAEC,QAAQ,IAAI,MAAM;MAC/CC,KAAK,EAAEX,SAAS,CAACS,WAAW,EAAEE,KAAK,IAAI,kBAAkB;MACzDC,MAAM,EAAEZ,SAAS,CAACS,WAAW,EAAEG,MAAM,IAAI,IAAI;MAC7CC,SAAS,EAAEb,SAAS,CAACS,WAAW,EAAEK,UAAU,IAAId,SAAS,CAACS,WAAW,EAAEC,QAAQ,IAAI;IACrF,CAAC,CAAC;;IAEF;IACA,MAAMK,WAAW,GAAGjC,QAAQ,CAAC,MAAM,CACjC;MAAEkC,KAAK,EAAE,WAAW;MAAEC,IAAI,EAAE,YAAY;MAAEC,IAAI,EAAE;IAAc,CAAC,EAC/D;MAAEF,KAAK,EAAE,mBAAmB;MAAEG,OAAO,EAAE;IAAK,EAC7C;;IAED;IACA,MAAM;MAAEC,aAAa;MAAEC;IAAkB,CAAC,GAAGzB,gBAAgB,CAAC;IAC9D,MAAM;MAAE0B;IAAU,CAAC,GAAGzB,YAAY,CAAC;IACnC,MAAM;MAAE0B,kBAAkB;MAAEC,iBAAiB;MAAEC;IAAmB,CAAC,GAAG3B,aAAa,CAAC;;IAEpF;IACA,MAAM4B,eAAe,GAAG9C,GAAG,CAAC,IAAI;;IAEhC;IACA,MAAM+C,kBAAkB,GAAGA,CAAA,KAAM;MAC/BxB,OAAO,CAACC,gBAAgB,GAAG,CAACD,OAAO,CAACC,gBAAe;IACrD;IAEA,MAAMwB,gBAAgB,GAAIC,MAAM,IAAK;MACnC,QAAQA,MAAM;QACZ,KAAK,WAAW;UACd9B,MAAM,CAAC+B,IAAI,CAAC;YAAEtB,IAAI,EAAE;UAAkB,CAAC;UACvC;QACF,KAAK,UAAU;UACbuB,gBAAgB,CAAC;UACjB;QACF,KAAK,UAAU;UACbR,kBAAkB,CAAC;UACnB;QACF,KAAK,SAAS;UACZC,iBAAiB,CAAC;UAClB;QACF,KAAK,MAAM;UACTQ,aAAa,CAAC;UACd;QACF;UACEC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEL,MAAM;MACtC;IACF;IAEA,MAAMM,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACF,MAAMnC,SAAS,CAACoC,MAAM,CAAC;QACvBrC,MAAM,CAAC+B,IAAI,CAAC;UAAEtB,IAAI,EAAE;QAAc,CAAC;MACrC,CAAC,CAAC,OAAO6B,KAAK,EAAE;QACdJ,OAAO,CAACI,KAAK,CAAC,eAAe,EAAEA,KAAK;QACpCnC,iBAAiB,CAACoC,eAAe,CAAC;UAChCC,IAAI,EAAE,OAAO;UACbC,KAAK,EAAE,eAAe;UACtBC,OAAO,EAAE;QACX,CAAC;MACH;IACF;IAEA,MAAMC,WAAW,GAAIL,KAAK,IAAK;MAC7BJ,OAAO,CAACI,KAAK,CAAC,kBAAkB,EAAEA,KAAK;MACvCnC,iBAAiB,CAACoC,eAAe,CAAC;QAChCC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,OAAO;QACdC,OAAO,EAAEJ,KAAK,CAACI,OAAO,IAAI;MAC5B,CAAC;IACH;IAEA,MAAME,YAAY,GAAIC,KAAK,IAAK;MAC9B;MACAX,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEU,KAAK;MAClC;IACF;IAEA,MAAMb,gBAAgB,GAAGA,CAAA,KAAM;MAC7BL,eAAe,CAACmB,KAAK,EAAEC,GAAG,EAAEC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC;IACnE;IAEA,MAAMC,kBAAkB,GAAIC,YAAY,IAAK;MAC3C,IAAI,CAACA,YAAY,CAACC,SAAS,EAAE;QAC3BjD,iBAAiB,CAACoC,eAAe,CAAC;UAChCC,IAAI,EAAE,SAAS;UACfC,KAAK,EAAE,qBAAqB;UAC5BC,OAAO,EAAE,GAAGS,YAAY,CAACE,SAAS;QACpC,CAAC;QACD;MACF;MAEA3B,kBAAkB,CAACyB,YAAY;IACjC;IAEA,MAAMlB,aAAa,GAAGA,CAAA,KAAM;MAC1B7B,OAAO,CAACE,aAAa,GAAG,IAAG;IAC7B;IAEA,MAAMgD,cAAc,GAAGA,CAAA,KAAM;MAC3BlD,OAAO,CAACE,aAAa,GAAG,KAAI;IAC9B;IAEA,MAAMiD,gBAAgB,GAAGA,CAAA,KAAM;MAC7BnD,OAAO,CAACG,gBAAgB,GAAG,IAAG;IAChC;IAEA,MAAMiD,iBAAiB,GAAGA,CAAA,KAAM;MAC9BpD,OAAO,CAACG,gBAAgB,GAAG,KAAI;IACjC;;IAEA;IACAvB,SAAS,CAAC,YAAY;MACpB,IAAI;QACF,MAAMyE,OAAO,CAACC,GAAG,CAAC,CAChBzD,SAAS,CAAC0D,eAAe,CAAC,CAAC,EAC3BrC,iBAAiB,CAAC,CAAC,EACnBC,SAAS,CAACqC,IAAI,CAAC,EAChB;MACH,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACdJ,OAAO,CAACI,KAAK,CAAC,8BAA8B,EAAEA,KAAK;QACnDK,WAAW,CAACL,KAAK;MACnB;IACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}