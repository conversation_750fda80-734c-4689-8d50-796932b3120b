<template>
  <header class="client-header" role="banner">
    <!-- Skip to main content link -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Government Banner -->
    <div class="gov-banner" role="region" aria-label="Government website banner">
      <div class="gov-banner-content">
        <div class="gov-banner-flag">
          <img
            src="/assets/images/ph_flag_small.png"
            alt="Philippine flag"
            class="flag-icon"
            width="24"
            height="16"
          />
        </div>
        <div class="gov-banner-text">
          <span class="gov-banner-label">An official website of the</span>
          <strong class="gov-banner-agency">Barangay Bula, General Santos City</strong>
        </div>
        <div class="gov-banner-secure">
          <i class="fas fa-shield-alt" aria-hidden="true"></i>
          <span>Secure</span>
        </div>
      </div>
    </div>

    <!-- Main Header -->
    <div class="main-header">
      <div class="header-container">
        <!-- Logo and Site Identity -->
        <div class="header-brand">
          <button
            class="logo-button"
            @click="handleMenuAction('dashboard')"
            aria-label="Go to dashboard"
            type="button"
          >
            <img
              src="@/assets/icon-of-bula.jpg"
              alt="Barangay Bula Logo"
              class="logo"
              width="48"
              height="48"
            />
            <div class="site-identity">
              <h1 class="site-title">Barangay Bula</h1>
              <span class="site-subtitle">Digital Services Portal</span>
            </div>
          </button>
        </div>

        <!-- Primary Navigation -->
        <nav class="primary-nav" role="navigation" aria-label="Primary navigation">
          <ul class="nav-list" role="menubar">
            <li class="nav-item" role="none">
              <button
                class="nav-link"
                :class="{ active: activeMenu === 'dashboard' }"
                @click="handleMenuAction('dashboard')"
                role="menuitem"
                :aria-current="activeMenu === 'dashboard' ? 'page' : undefined"
              >
                <i class="fas fa-home" aria-hidden="true"></i>
                <span>Dashboard</span>
              </button>
            </li>
            <li class="nav-item" role="none">
              <button
                class="nav-link"
                :class="{ active: activeMenu === 'services' }"
                @click="handleMenuAction('services')"
                role="menuitem"
                :aria-current="activeMenu === 'services' ? 'page' : undefined"
              >
                <i class="fas fa-file-alt" aria-hidden="true"></i>
                <span>Services</span>
              </button>
            </li>
            <li class="nav-item" role="none">
              <button
                class="nav-link"
                :class="{ active: activeMenu === 'requests' }"
                @click="handleMenuAction('requests')"
                role="menuitem"
                :aria-current="activeMenu === 'requests' ? 'page' : undefined"
              >
                <i class="fas fa-clock" aria-hidden="true"></i>
                <span>My Requests</span>
              </button>
            </li>
            <li class="nav-item" role="none">
              <button
                class="nav-link"
                :class="{ active: activeMenu === 'help' }"
                @click="handleMenuAction('help')"
                role="menuitem"
                :aria-current="activeMenu === 'help' ? 'page' : undefined"
              >
                <i class="fas fa-question-circle" aria-hidden="true"></i>
                <span>Help</span>
              </button>
            </li>
          </ul>
        </nav>

        <!-- Header Actions -->
        <div class="header-actions">
          <!-- Search -->
          <div class="search-container">
            <button
              class="search-toggle"
              @click="toggleSearch"
              :aria-label="showSearch ? 'Close search' : 'Open search'"
              :aria-expanded="showSearch"
              type="button"
            >
              <i class="fas fa-search" aria-hidden="true"></i>
              <span class="sr-only">Search</span>
            </button>

            <Transition name="search-slide">
              <div v-if="showSearch" class="search-box" role="search">
                <label for="header-search" class="sr-only">Search documents and services</label>
                <input
                  id="header-search"
                  ref="searchInput"
                  type="search"
                  placeholder="Search documents, services..."
                  class="search-input"
                  v-model="searchQuery"
                  @keyup.enter="performSearch"
                  @keyup.escape="closeSearch"
                  autocomplete="off"
                  aria-describedby="search-help"
                />
                <span id="search-help" class="sr-only">
                  Press Enter to search, Escape to close
                </span>
                <button
                  class="search-submit"
                  @click="performSearch"
                  aria-label="Submit search"
                  type="button"
                >
                  <i class="fas fa-search" aria-hidden="true"></i>
                </button>
              </div>
            </Transition>
          </div>

          <!-- Notifications -->
          <ClientNotifications
            @new-notification="handleNewNotification"
            @notification-click="handleNotificationClick"
            @error="handleNotificationError"
          />

          <!-- User Profile Menu -->
          <div class="user-profile" ref="userProfileRef">
            <button
              class="user-button"
              @click="toggleUserDropdown"
              :aria-label="`User menu for ${userName}`"
              :aria-expanded="showUserDropdown"
              type="button"
            >
              <div class="user-avatar">
                <img
                  v-if="userAvatar"
                  :src="userAvatar"
                  :alt="`${userName} avatar`"
                  class="avatar-image"
                  width="32"
                  height="32"
                />
                <i v-else class="fas fa-user-circle avatar-icon" aria-hidden="true"></i>
              </div>
              <div class="user-info">
                <span class="user-name">{{ userName }}</span>
                <span class="user-role">Client Portal</span>
              </div>
              <i
                class="fas fa-chevron-down dropdown-arrow"
                :class="{ rotated: showUserDropdown }"
                aria-hidden="true"
              ></i>
            </button>

            <Transition name="dropdown-fade">
              <div
                v-if="showUserDropdown"
                class="user-dropdown"
                role="menu"
                aria-label="User account options"
              >
                <div class="dropdown-header">
                  <div class="user-details">
                    <strong class="user-display-name">{{ userName }}</strong>
                    <span class="user-email">{{ userEmail }}</span>
                  </div>
                </div>

                <div class="dropdown-divider" role="separator"></div>

                <button
                  class="dropdown-item"
                  @click="handleMenuAction('profile')"
                  role="menuitem"
                  type="button"
                >
                  <i class="fas fa-user" aria-hidden="true"></i>
                  <span>My Profile</span>
                </button>

                <button
                  class="dropdown-item"
                  @click="handleMenuAction('settings')"
                  role="menuitem"
                  type="button"
                >
                  <i class="fas fa-cog" aria-hidden="true"></i>
                  <span>Account Settings</span>
                </button>

                <button
                  class="dropdown-item"
                  @click="handleMenuAction('documents')"
                  role="menuitem"
                  type="button"
                >
                  <i class="fas fa-folder" aria-hidden="true"></i>
                  <span>My Documents</span>
                </button>

                <button
                  class="dropdown-item"
                  @click="handleMenuAction('history')"
                  role="menuitem"
                  type="button"
                >
                  <i class="fas fa-history" aria-hidden="true"></i>
                  <span>Request History</span>
                </button>

                <div class="dropdown-divider" role="separator"></div>

                <button
                  class="dropdown-item"
                  @click="handleMenuAction('help')"
                  role="menuitem"
                  type="button"
                >
                  <i class="fas fa-question-circle" aria-hidden="true"></i>
                  <span>Help & Support</span>
                </button>

                <div class="dropdown-divider" role="separator"></div>

                <button
                  class="dropdown-item logout-item"
                  @click="handleLogout"
                  role="menuitem"
                  type="button"
                >
                  <i class="fas fa-sign-out-alt" aria-hidden="true"></i>
                  <span>Sign Out</span>
                </button>
              </div>
            </Transition>
          </div>

          <!-- Mobile Menu Toggle -->
          <button
            class="mobile-menu-toggle"
            @click="toggleMobileMenu"
            :aria-label="showMobileMenu ? 'Close navigation menu' : 'Open navigation menu'"
            :aria-expanded="showMobileMenu"
            type="button"
          >
            <span class="hamburger-line" :class="{ active: showMobileMenu }"></span>
            <span class="hamburger-line" :class="{ active: showMobileMenu }"></span>
            <span class="hamburger-line" :class="{ active: showMobileMenu }"></span>
          </button>
        </div>
      </div>
    </div>

    <!-- Mobile Navigation Menu -->
    <Transition name="mobile-menu-slide">
      <div v-if="showMobileMenu" class="mobile-nav" role="navigation" aria-label="Mobile navigation">
        <div class="mobile-nav-content">
          <nav class="mobile-nav-menu">
            <ul class="mobile-nav-list" role="menubar">
              <li class="mobile-nav-item" role="none">
                <button
                  class="mobile-nav-link"
                  :class="{ active: activeMenu === 'dashboard' }"
                  @click="handleMobileMenuAction('dashboard')"
                  role="menuitem"
                  type="button"
                >
                  <i class="fas fa-home" aria-hidden="true"></i>
                  <span>Dashboard</span>
                </button>
              </li>
              <li class="mobile-nav-item" role="none">
                <button
                  class="mobile-nav-link"
                  :class="{ active: activeMenu === 'services' }"
                  @click="handleMobileMenuAction('services')"
                  role="menuitem"
                  type="button"
                >
                  <i class="fas fa-file-alt" aria-hidden="true"></i>
                  <span>Services</span>
                </button>
              </li>
              <li class="mobile-nav-item" role="none">
                <button
                  class="mobile-nav-link"
                  :class="{ active: activeMenu === 'requests' }"
                  @click="handleMobileMenuAction('requests')"
                  role="menuitem"
                  type="button"
                >
                  <i class="fas fa-clock" aria-hidden="true"></i>
                  <span>My Requests</span>
                </button>
              </li>
              <li class="mobile-nav-item" role="none">
                <button
                  class="mobile-nav-link"
                  :class="{ active: activeMenu === 'help' }"
                  @click="handleMobileMenuAction('help')"
                  role="menuitem"
                  type="button"
                >
                  <i class="fas fa-question-circle" aria-hidden="true"></i>
                  <span>Help</span>
                </button>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </Transition>

    <!-- Breadcrumb Navigation -->
    <div v-if="showBreadcrumbs" class="breadcrumb-section" role="navigation" aria-label="Breadcrumb">
      <div class="header-container">
        <nav class="breadcrumb-nav">
          <ol class="breadcrumb-list">
            <li class="breadcrumb-item">
              <button
                class="breadcrumb-link"
                @click="handleMenuAction('dashboard')"
                type="button"
              >
                <i class="fas fa-home" aria-hidden="true"></i>
                <span>Dashboard</span>
              </button>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
              <span>{{ getPageTitle() }}</span>
            </li>
          </ol>
        </nav>
      </div>
    </div>
  </header>
</template>

<script setup>
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import ClientNotifications from './ClientNotifications.vue'

// Props
const props = defineProps({
  userName: {
    type: String,
    default: 'User'
  },
  userEmail: {
    type: String,
    default: '<EMAIL>'
  },
  userAvatar: {
    type: String,
    default: null
  },
  showUserDropdown: {
    type: Boolean,
    default: false
  },
  activeMenu: {
    type: String,
    default: 'dashboard'
  },
  showBreadcrumbs: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits([
  'user-dropdown-toggle',
  'menu-action',
  'logout',
  'error',
  'search',
  'notification-click'
])

// Router
const router = useRouter()

// Reactive state
const showSearch = ref(false)
const showMobileMenu = ref(false)
const searchQuery = ref('')
const searchInput = ref(null)
const userProfileRef = ref(null)

// Computed
const pageTitle = computed(() => {
  const titles = {
    'dashboard': 'Dashboard',
    'services': 'Document Services',
    'requests': 'My Requests',
    'documents': 'My Documents',
    'profile': 'My Profile',
    'settings': 'Account Settings',
    'history': 'Request History',
    'notifications': 'Notifications',
    'help': 'Help & Support'
  }
  return titles[props.activeMenu] || 'Dashboard'
})

// Methods
const getPageTitle = () => pageTitle.value

const toggleSearch = async () => {
  showSearch.value = !showSearch.value
  if (showSearch.value) {
    await nextTick()
    searchInput.value?.focus()
  }
}

const closeSearch = () => {
  showSearch.value = false
  searchQuery.value = ''
}

const performSearch = () => {
  if (searchQuery.value.trim()) {
    emit('search', searchQuery.value.trim())
    // Close search on mobile after search
    if (window.innerWidth <= 768) {
      closeSearch()
    }
  }
}

const toggleUserDropdown = () => {
  emit('user-dropdown-toggle')
}

const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
  // Prevent body scroll when mobile menu is open
  document.body.style.overflow = showMobileMenu.value ? 'hidden' : ''
}

const handleMenuAction = (action) => {
  emit('menu-action', action)
  // Close mobile menu if open
  if (showMobileMenu.value) {
    toggleMobileMenu()
  }
}

const handleMobileMenuAction = (action) => {
  handleMenuAction(action)
}

const handleLogout = () => {
  emit('logout')
}

const handleOutsideClick = (event) => {
  // Check if click is outside user dropdown
  if (userProfileRef.value && !userProfileRef.value.contains(event.target)) {
    if (props.showUserDropdown) {
      toggleUserDropdown()
    }
  }

  // Check if click is outside search
  if (!event.target.closest('.search-container')) {
    showSearch.value = false
  }

  // Check if click is outside mobile menu
  if (!event.target.closest('.mobile-nav') && !event.target.closest('.mobile-menu-toggle')) {
    if (showMobileMenu.value) {
      toggleMobileMenu()
    }
  }
}

const handleKeydown = (event) => {
  // Close dropdowns on Escape key
  if (event.key === 'Escape') {
    if (showSearch.value) {
      closeSearch()
    }
    if (props.showUserDropdown) {
      toggleUserDropdown()
    }
    if (showMobileMenu.value) {
      toggleMobileMenu()
    }
  }
}

// Notification event handlers
const handleNewNotification = (notification) => {
  console.log('New notification received:', notification)
  // Handle new notification - could show toast, update UI, etc.
}

const handleNotificationClick = async (notification) => {
  console.log('🔔 ClientHeader: Notification clicked:', notification)

  // Ensure we have a valid notification object
  if (!notification || typeof notification !== 'object') {
    console.error('Invalid notification object received:', notification)
    return
  }

  try {
    // Parse notification data
    const notificationData = typeof notification.data === 'string'
      ? JSON.parse(notification.data)
      : notification.data || {}

    // Log navigation for debugging
    console.log('📊 ClientHeader: Notification data:', notificationData)

    // Additional header-specific logic can go here
    // For example, updating header state, showing badges, etc.

  } catch (error) {
    console.error('❌ ClientHeader: Error handling notification click:', error)
    emit('error', error)
  }

  // Always emit the event for other components that might need it
  emit('notification-click', notification)
}

const handleNotificationError = (error) => {
  console.error('Notification error:', error)
  emit('error', error)
}

// Lifecycle hooks
onMounted(() => {
  document.addEventListener('click', handleOutsideClick)
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('click', handleOutsideClick)
  document.removeEventListener('keydown', handleKeydown)
  // Reset body overflow
  document.body.style.overflow = ''
})
</script>

<style scoped src="./css/clientHeader.css"></style>
