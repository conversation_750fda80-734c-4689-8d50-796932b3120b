{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, with<PERSON><PERSON>s as _withKeys, openBlock as _openBlock, createElement<PERSON>lock as _createElementBlock, Fragment as _Fragment, renderList as _renderList, normalizeClass as _normalizeClass, createStaticVNode as _createStaticVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"client-dashboard\"\n};\nconst _hoisted_2 = {\n  class: \"hero-section\"\n};\nconst _hoisted_3 = {\n  class: \"container\"\n};\nconst _hoisted_4 = {\n  class: \"hero-content\"\n};\nconst _hoisted_5 = {\n  class: \"hero-text\"\n};\nconst _hoisted_6 = {\n  class: \"hero-title\"\n};\nconst _hoisted_7 = {\n  class: \"hero-actions\"\n};\nconst _hoisted_8 = {\n  class: \"hero-stats\"\n};\nconst _hoisted_9 = {\n  class: \"stats-grid\"\n};\nconst _hoisted_10 = {\n  class: \"stat-card\"\n};\nconst _hoisted_11 = {\n  class: \"stat-content\"\n};\nconst _hoisted_12 = {\n  class: \"stat-number\"\n};\nconst _hoisted_13 = {\n  class: \"stat-card\"\n};\nconst _hoisted_14 = {\n  class: \"stat-content\"\n};\nconst _hoisted_15 = {\n  class: \"stat-number\"\n};\nconst _hoisted_16 = {\n  class: \"stat-card\"\n};\nconst _hoisted_17 = {\n  class: \"stat-content\"\n};\nconst _hoisted_18 = {\n  class: \"stat-number\"\n};\nconst _hoisted_19 = {\n  class: \"quick-actions-section\"\n};\nconst _hoisted_20 = {\n  class: \"container\"\n};\nconst _hoisted_21 = {\n  class: \"quick-actions-grid\"\n};\nconst _hoisted_22 = {\n  class: \"services-section\",\n  ref: \"servicesSection\"\n};\nconst _hoisted_23 = {\n  class: \"container\"\n};\nconst _hoisted_24 = {\n  key: 0,\n  class: \"loading-container\",\n  role: \"status\",\n  \"aria-live\": \"polite\"\n};\nconst _hoisted_25 = {\n  class: \"error-container\",\n  role: \"alert\"\n};\nconst _hoisted_26 = {\n  class: \"error-content\"\n};\nconst _hoisted_27 = {\n  class: \"document-types-grid\"\n};\nconst _hoisted_28 = [\"onClick\", \"onKeyup\", \"tabindex\", \"aria-disabled\"];\nconst _hoisted_29 = {\n  class: \"document-header\"\n};\nconst _hoisted_30 = {\n  class: \"document-icon\"\n};\nconst _hoisted_31 = {\n  class: \"document-status\"\n};\nconst _hoisted_32 = {\n  key: 0,\n  class: \"status-badge unavailable\"\n};\nconst _hoisted_33 = {\n  key: 1,\n  class: \"status-badge available\"\n};\nconst _hoisted_34 = {\n  class: \"document-content\"\n};\nconst _hoisted_35 = {\n  class: \"document-title\"\n};\nconst _hoisted_36 = {\n  class: \"document-description\"\n};\nconst _hoisted_37 = {\n  class: \"document-details\"\n};\nconst _hoisted_38 = {\n  class: \"detail-item\"\n};\nconst _hoisted_39 = {\n  class: \"detail-value fee-amount\"\n};\nconst _hoisted_40 = {\n  class: \"detail-item\"\n};\nconst _hoisted_41 = {\n  class: \"detail-value\"\n};\nconst _hoisted_42 = {\n  class: \"document-action\"\n};\nconst _hoisted_43 = {\n  key: 0,\n  class: \"fas fa-chevron-right\",\n  \"aria-hidden\": \"true\"\n};\nconst _hoisted_44 = {\n  class: \"info-section\"\n};\nconst _hoisted_45 = {\n  class: \"container\"\n};\nconst _hoisted_46 = {\n  class: \"info-grid\"\n};\nconst _hoisted_47 = {\n  class: \"info-card help-card\"\n};\nconst _hoisted_48 = {\n  class: \"card-content\"\n};\nconst _hoisted_49 = {\n  class: \"help-actions\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_ClientHeader = _resolveComponent(\"ClientHeader\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" Background \"), _cache[42] || (_cache[42] = _createElementVNode(\"div\", {\n    class: \"background-container\"\n  }, [_createElementVNode(\"div\", {\n    class: \"background-image\"\n  }), _createElementVNode(\"div\", {\n    class: \"background-overlay\"\n  })], -1 /* HOISTED */)), _createCommentVNode(\" Client Header with Navigation \"), _createVNode(_component_ClientHeader, {\n    userName: $data.userName,\n    userEmail: $data.userEmail,\n    userAvatar: $data.userAvatar,\n    showUserDropdown: $data.showUserDropdown,\n    sidebarCollapsed: $data.sidebarCollapsed,\n    activeMenu: $data.activeMenu,\n    showBreadcrumbs: true,\n    onSidebarToggle: $options.handleSidebarToggle,\n    onUserDropdownToggle: $options.handleUserDropdownToggle,\n    onMenuAction: $options.handleMenuAction,\n    onLogout: $options.handleLogout,\n    onError: $options.handleError,\n    onSearch: $options.handleSearch\n  }, null, 8 /* PROPS */, [\"userName\", \"userEmail\", \"userAvatar\", \"showUserDropdown\", \"sidebarCollapsed\", \"activeMenu\", \"onSidebarToggle\", \"onUserDropdownToggle\", \"onMenuAction\", \"onLogout\", \"onError\", \"onSearch\"]), _createCommentVNode(\" Main Content \"), _createElementVNode(\"main\", {\n    class: _normalizeClass([\"main-content\", {\n      'sidebar-collapsed': $data.sidebarCollapsed\n    }])\n  }, [_createCommentVNode(\" Hero Section \"), _createElementVNode(\"section\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"h1\", _hoisted_6, \"Welcome back, \" + _toDisplayString($data.firstName) + \"!\", 1 /* TEXT */), _cache[15] || (_cache[15] = _createElementVNode(\"p\", {\n    class: \"hero-subtitle\"\n  }, \"Access government services and request official documents through our secure digital platform.\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"button\", {\n    class: \"btn btn-primary\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.scrollToServices && $options.scrollToServices(...args))\n  }, _cache[13] || (_cache[13] = [_createElementVNode(\"i\", {\n    class: \"fas fa-plus-circle\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Start New Request \")])), _createElementVNode(\"button\", {\n    class: \"btn btn-secondary\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.goToMyRequests && $options.goToMyRequests(...args))\n  }, _cache[14] || (_cache[14] = [_createElementVNode(\"i\", {\n    class: \"fas fa-list-alt\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" View My Requests \")]))])]), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_cache[17] || (_cache[17] = _createElementVNode(\"div\", {\n    class: \"stat-icon\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-file-alt\",\n    \"aria-hidden\": \"true\"\n  })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, _toDisplayString($data.totalRequests), 1 /* TEXT */), _cache[16] || (_cache[16] = _createElementVNode(\"div\", {\n    class: \"stat-label\"\n  }, \"Total Requests\", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_13, [_cache[19] || (_cache[19] = _createElementVNode(\"div\", {\n    class: \"stat-icon\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-clock\",\n    \"aria-hidden\": \"true\"\n  })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, _toDisplayString($data.pendingRequests), 1 /* TEXT */), _cache[18] || (_cache[18] = _createElementVNode(\"div\", {\n    class: \"stat-label\"\n  }, \"Pending\", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_16, [_cache[21] || (_cache[21] = _createElementVNode(\"div\", {\n    class: \"stat-icon\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-check-circle\",\n    \"aria-hidden\": \"true\"\n  })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, _toDisplayString($data.totalRequests - $data.pendingRequests), 1 /* TEXT */), _cache[20] || (_cache[20] = _createElementVNode(\"div\", {\n    class: \"stat-label\"\n  }, \"Completed\", -1 /* HOISTED */))])])])])])])]), _createCommentVNode(\" Quick Actions Section \"), _createElementVNode(\"section\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, [_cache[26] || (_cache[26] = _createElementVNode(\"div\", {\n    class: \"section-header\"\n  }, [_createElementVNode(\"h2\", {\n    class: \"section-title\"\n  }, \"Quick Actions\"), _createElementVNode(\"p\", {\n    class: \"section-description\"\n  }, \"Common tasks and frequently used services\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"div\", {\n    class: \"action-card primary\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.scrollToServices && $options.scrollToServices(...args)),\n    role: \"button\",\n    tabindex: \"0\",\n    onKeyup: _cache[3] || (_cache[3] = _withKeys((...args) => $options.scrollToServices && $options.scrollToServices(...args), [\"enter\"]))\n  }, _cache[22] || (_cache[22] = [_createStaticVNode(\"<div class=\\\"action-icon\\\" data-v-b2da9790><i class=\\\"fas fa-plus-circle\\\" aria-hidden=\\\"true\\\" data-v-b2da9790></i></div><div class=\\\"action-content\\\" data-v-b2da9790><h3 data-v-b2da9790>New Document Request</h3><p data-v-b2da9790>Start a new request for official documents</p></div><div class=\\\"action-arrow\\\" data-v-b2da9790><i class=\\\"fas fa-arrow-right\\\" aria-hidden=\\\"true\\\" data-v-b2da9790></i></div>\", 3)]), 32 /* NEED_HYDRATION */), _createElementVNode(\"div\", {\n    class: \"action-card\",\n    onClick: _cache[4] || (_cache[4] = (...args) => $options.goToMyRequests && $options.goToMyRequests(...args)),\n    role: \"button\",\n    tabindex: \"0\",\n    onKeyup: _cache[5] || (_cache[5] = _withKeys((...args) => $options.goToMyRequests && $options.goToMyRequests(...args), [\"enter\"]))\n  }, _cache[23] || (_cache[23] = [_createStaticVNode(\"<div class=\\\"action-icon\\\" data-v-b2da9790><i class=\\\"fas fa-list-alt\\\" aria-hidden=\\\"true\\\" data-v-b2da9790></i></div><div class=\\\"action-content\\\" data-v-b2da9790><h3 data-v-b2da9790>Track Requests</h3><p data-v-b2da9790>View status and track your submitted requests</p></div><div class=\\\"action-arrow\\\" data-v-b2da9790><i class=\\\"fas fa-arrow-right\\\" aria-hidden=\\\"true\\\" data-v-b2da9790></i></div>\", 3)]), 32 /* NEED_HYDRATION */), _createElementVNode(\"div\", {\n    class: \"action-card\",\n    onClick: _cache[6] || (_cache[6] = (...args) => $options.goToProfile && $options.goToProfile(...args)),\n    role: \"button\",\n    tabindex: \"0\",\n    onKeyup: _cache[7] || (_cache[7] = _withKeys((...args) => $options.goToProfile && $options.goToProfile(...args), [\"enter\"]))\n  }, _cache[24] || (_cache[24] = [_createStaticVNode(\"<div class=\\\"action-icon\\\" data-v-b2da9790><i class=\\\"fas fa-user-edit\\\" aria-hidden=\\\"true\\\" data-v-b2da9790></i></div><div class=\\\"action-content\\\" data-v-b2da9790><h3 data-v-b2da9790>Update Profile</h3><p data-v-b2da9790>Manage your account and personal information</p></div><div class=\\\"action-arrow\\\" data-v-b2da9790><i class=\\\"fas fa-arrow-right\\\" aria-hidden=\\\"true\\\" data-v-b2da9790></i></div>\", 3)]), 32 /* NEED_HYDRATION */), _createElementVNode(\"div\", {\n    class: \"action-card\",\n    onClick: _cache[8] || (_cache[8] = (...args) => $options.contactSupport && $options.contactSupport(...args)),\n    role: \"button\",\n    tabindex: \"0\",\n    onKeyup: _cache[9] || (_cache[9] = _withKeys((...args) => $options.contactSupport && $options.contactSupport(...args), [\"enter\"]))\n  }, _cache[25] || (_cache[25] = [_createStaticVNode(\"<div class=\\\"action-icon\\\" data-v-b2da9790><i class=\\\"fas fa-headset\\\" aria-hidden=\\\"true\\\" data-v-b2da9790></i></div><div class=\\\"action-content\\\" data-v-b2da9790><h3 data-v-b2da9790>Get Support</h3><p data-v-b2da9790>Contact our support team for assistance</p></div><div class=\\\"action-arrow\\\" data-v-b2da9790><i class=\\\"fas fa-arrow-right\\\" aria-hidden=\\\"true\\\" data-v-b2da9790></i></div>\", 3)]), 32 /* NEED_HYDRATION */)])])]), _createCommentVNode(\" Document Services Section \"), _createElementVNode(\"section\", _hoisted_22, [_createElementVNode(\"div\", _hoisted_23, [_cache[35] || (_cache[35] = _createElementVNode(\"div\", {\n    class: \"section-header\"\n  }, [_createElementVNode(\"h2\", {\n    class: \"section-title\"\n  }, \"Available Document Services\"), _createElementVNode(\"p\", {\n    class: \"section-description\"\n  }, \"Select the type of document you need to request\")], -1 /* HOISTED */)), _createCommentVNode(\" Loading State \"), $data.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_24, _cache[27] || (_cache[27] = [_createElementVNode(\"div\", {\n    class: \"loading-spinner\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-spinner fa-spin\",\n    \"aria-hidden\": \"true\"\n  })], -1 /* HOISTED */), _createElementVNode(\"p\", null, \"Loading available services...\", -1 /* HOISTED */)]))) : $data.error ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" Error State \"), _createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"div\", _hoisted_26, [_cache[29] || (_cache[29] = _createElementVNode(\"i\", {\n    class: \"fas fa-exclamation-triangle\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */)), _cache[30] || (_cache[30] = _createElementVNode(\"h3\", null, \"Unable to Load Services\", -1 /* HOISTED */)), _createElementVNode(\"p\", null, _toDisplayString($data.error), 1 /* TEXT */), _createElementVNode(\"button\", {\n    class: \"btn btn-primary retry-btn\",\n    onClick: _cache[10] || (_cache[10] = (...args) => $options.loadDocumentTypes && $options.loadDocumentTypes(...args))\n  }, _cache[28] || (_cache[28] = [_createElementVNode(\"i\", {\n    class: \"fas fa-redo\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Try Again \")]))])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 2\n  }, [_createCommentVNode(\" Document Types Grid \"), _createElementVNode(\"div\", _hoisted_27, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.documentTypes, documentType => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: documentType.id,\n      class: _normalizeClass([\"document-card\", {\n        'disabled': !documentType.is_active\n      }]),\n      onClick: $event => $options.selectDocumentType(documentType),\n      onKeyup: _withKeys($event => $options.selectDocumentType(documentType), [\"enter\"]),\n      role: \"button\",\n      tabindex: documentType.is_active ? 0 : -1,\n      \"aria-disabled\": !documentType.is_active\n    }, [_createElementVNode(\"div\", _hoisted_29, [_createElementVNode(\"div\", _hoisted_30, [_createElementVNode(\"i\", {\n      class: _normalizeClass($options.getDocumentIcon(documentType.type_name)),\n      \"aria-hidden\": \"true\"\n    }, null, 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_31, [!documentType.is_active ? (_openBlock(), _createElementBlock(\"span\", _hoisted_32, \" Unavailable \")) : (_openBlock(), _createElementBlock(\"span\", _hoisted_33, \" Available \"))])]), _createElementVNode(\"div\", _hoisted_34, [_createElementVNode(\"h3\", _hoisted_35, _toDisplayString(documentType.type_name), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_36, _toDisplayString(documentType.description), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_37, [_createElementVNode(\"div\", _hoisted_38, [_cache[31] || (_cache[31] = _createElementVNode(\"i\", {\n      class: \"fas fa-peso-sign\",\n      \"aria-hidden\": \"true\"\n    }, null, -1 /* HOISTED */)), _cache[32] || (_cache[32] = _createElementVNode(\"span\", {\n      class: \"detail-label\"\n    }, \"Fee:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_39, \"₱\" + _toDisplayString($options.formatCurrency(documentType.base_fee)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_40, [_cache[33] || (_cache[33] = _createElementVNode(\"i\", {\n      class: \"fas fa-clock\",\n      \"aria-hidden\": \"true\"\n    }, null, -1 /* HOISTED */)), _cache[34] || (_cache[34] = _createElementVNode(\"span\", {\n      class: \"detail-label\"\n    }, \"Processing:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_41, _toDisplayString($options.getProcessingTime(documentType.type_name)), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_42, [documentType.is_active ? (_openBlock(), _createElementBlock(\"i\", _hoisted_43)) : _createCommentVNode(\"v-if\", true)])], 42 /* CLASS, PROPS, NEED_HYDRATION */, _hoisted_28);\n  }), 128 /* KEYED_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))])], 512 /* NEED_PATCH */), _createCommentVNode(\" Information and Help Section \"), _createElementVNode(\"section\", _hoisted_44, [_createElementVNode(\"div\", _hoisted_45, [_createElementVNode(\"div\", _hoisted_46, [_createCommentVNode(\" Requirements Card \"), _cache[40] || (_cache[40] = _createStaticVNode(\"<div class=\\\"info-card requirements-card\\\" data-v-b2da9790><div class=\\\"card-header\\\" data-v-b2da9790><i class=\\\"fas fa-clipboard-list\\\" aria-hidden=\\\"true\\\" data-v-b2da9790></i><h3 data-v-b2da9790>Before You Start</h3></div><div class=\\\"card-content\\\" data-v-b2da9790><ul class=\\\"requirements-list\\\" data-v-b2da9790><li class=\\\"requirement-item\\\" data-v-b2da9790><i class=\\\"fas fa-check-circle\\\" aria-hidden=\\\"true\\\" data-v-b2da9790></i><span data-v-b2da9790>Complete and accurate profile information</span></li><li class=\\\"requirement-item\\\" data-v-b2da9790><i class=\\\"fas fa-check-circle\\\" aria-hidden=\\\"true\\\" data-v-b2da9790></i><span data-v-b2da9790>Valid government-issued ID ready for upload</span></li><li class=\\\"requirement-item\\\" data-v-b2da9790><i class=\\\"fas fa-check-circle\\\" aria-hidden=\\\"true\\\" data-v-b2da9790></i><span data-v-b2da9790>Supporting documents (if required)</span></li><li class=\\\"requirement-item\\\" data-v-b2da9790><i class=\\\"fas fa-check-circle\\\" aria-hidden=\\\"true\\\" data-v-b2da9790></i><span data-v-b2da9790>Payment method for processing fees</span></li></ul></div></div>\", 1)), _createCommentVNode(\" Process Card \"), _cache[41] || (_cache[41] = _createStaticVNode(\"<div class=\\\"info-card process-card\\\" data-v-b2da9790><div class=\\\"card-header\\\" data-v-b2da9790><i class=\\\"fas fa-route\\\" aria-hidden=\\\"true\\\" data-v-b2da9790></i><h3 data-v-b2da9790>How It Works</h3></div><div class=\\\"card-content\\\" data-v-b2da9790><ol class=\\\"process-steps\\\" data-v-b2da9790><li class=\\\"process-step\\\" data-v-b2da9790><div class=\\\"step-number\\\" data-v-b2da9790>1</div><div class=\\\"step-content\\\" data-v-b2da9790><strong data-v-b2da9790>Select Document</strong><span data-v-b2da9790>Choose the document type you need</span></div></li><li class=\\\"process-step\\\" data-v-b2da9790><div class=\\\"step-number\\\" data-v-b2da9790>2</div><div class=\\\"step-content\\\" data-v-b2da9790><strong data-v-b2da9790>Fill Application</strong><span data-v-b2da9790>Complete the required information</span></div></li><li class=\\\"process-step\\\" data-v-b2da9790><div class=\\\"step-number\\\" data-v-b2da9790>3</div><div class=\\\"step-content\\\" data-v-b2da9790><strong data-v-b2da9790>Submit &amp; Pay</strong><span data-v-b2da9790>Review and submit with payment</span></div></li><li class=\\\"process-step\\\" data-v-b2da9790><div class=\\\"step-number\\\" data-v-b2da9790>4</div><div class=\\\"step-content\\\" data-v-b2da9790><strong data-v-b2da9790>Track Progress</strong><span data-v-b2da9790>Monitor your request status</span></div></li></ol></div></div>\", 1)), _createCommentVNode(\" Help Card \"), _createElementVNode(\"div\", _hoisted_47, [_cache[39] || (_cache[39] = _createElementVNode(\"div\", {\n    class: \"card-header\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-headset\",\n    \"aria-hidden\": \"true\"\n  }), _createElementVNode(\"h3\", null, \"Need Assistance?\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_48, [_cache[38] || (_cache[38] = _createElementVNode(\"p\", {\n    class: \"help-description\"\n  }, \"Our support team is here to help you with any questions about document requirements or the application process.\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_49, [_createElementVNode(\"button\", {\n    class: \"btn btn-outline help-btn\",\n    onClick: _cache[11] || (_cache[11] = (...args) => $options.openHelp && $options.openHelp(...args))\n  }, _cache[36] || (_cache[36] = [_createElementVNode(\"i\", {\n    class: \"fas fa-question-circle\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" View FAQ \")])), _createElementVNode(\"button\", {\n    class: \"btn btn-outline contact-btn\",\n    onClick: _cache[12] || (_cache[12] = (...args) => $options.contactSupport && $options.contactSupport(...args))\n  }, _cache[37] || (_cache[37] = [_createElementVNode(\"i\", {\n    class: \"fas fa-phone\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Contact Support \")]))])])])])])])], 2 /* CLASS */)]);\n}", "map": {"version": 3, "names": ["class", "ref", "role", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_createVNode", "_component_ClientHeader", "userName", "$data", "userEmail", "userAvatar", "showUserDropdown", "sidebarCollapsed", "activeMenu", "showBreadcrumbs", "onSidebarToggle", "$options", "handleSidebarToggle", "onUserDropdownToggle", "handleUserDropdownToggle", "onMenuAction", "handleMenuAction", "onLogout", "handleLogout", "onError", "handleError", "onSearch", "handleSearch", "_normalizeClass", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_toDisplayString", "firstName", "_hoisted_7", "onClick", "_cache", "args", "scrollToServices", "goToMyRequests", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "totalRequests", "_hoisted_13", "_hoisted_14", "_hoisted_15", "pendingRequests", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "tabindex", "onKeyup", "_with<PERSON><PERSON><PERSON>", "goToProfile", "contactSupport", "_hoisted_22", "_hoisted_23", "loading", "_hoisted_24", "error", "_Fragment", "key", "_hoisted_25", "_hoisted_26", "loadDocumentTypes", "_hoisted_27", "_renderList", "documentTypes", "documentType", "id", "is_active", "$event", "selectDocumentType", "_hoisted_29", "_hoisted_30", "getDocumentIcon", "type_name", "_hoisted_31", "_hoisted_32", "_hoisted_33", "_hoisted_34", "_hoisted_35", "_hoisted_36", "description", "_hoisted_37", "_hoisted_38", "_hoisted_39", "formatCurrency", "base_fee", "_hoisted_40", "_hoisted_41", "getProcessingTime", "_hoisted_42", "_hoisted_43", "_hoisted_44", "_hoisted_45", "_hoisted_46", "_hoisted_47", "_hoisted_48", "_hoisted_49", "openHelp"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\client\\NewDocumentRequest.vue"], "sourcesContent": ["<template>\n  <div class=\"client-dashboard\">\n    <!-- Background -->\n    <div class=\"background-container\">\n      <div class=\"background-image\"></div>\n      <div class=\"background-overlay\"></div>\n    </div>\n\n    <!-- Client Header with Navigation -->\n    <ClientHeader\n      :userName=\"userName\"\n      :userEmail=\"userEmail\"\n      :userAvatar=\"userAvatar\"\n      :showUserDropdown=\"showUserDropdown\"\n      :sidebarCollapsed=\"sidebarCollapsed\"\n      :activeMenu=\"activeMenu\"\n      :showBreadcrumbs=\"true\"\n      @sidebar-toggle=\"handleSidebarToggle\"\n      @user-dropdown-toggle=\"handleUserDropdownToggle\"\n      @menu-action=\"handleMenuAction\"\n      @logout=\"handleLogout\"\n      @error=\"handleError\"\n      @search=\"handleSearch\"\n    />\n\n    <!-- Main Content -->\n    <main class=\"main-content\" :class=\"{ 'sidebar-collapsed': sidebarCollapsed }\">\n      <!-- Hero Section -->\n      <section class=\"hero-section\">\n        <div class=\"container\">\n          <div class=\"hero-content\">\n            <div class=\"hero-text\">\n              <h1 class=\"hero-title\">Welcome back, {{ firstName }}!</h1>\n              <p class=\"hero-subtitle\">Access government services and request official documents through our secure digital platform.</p>\n              <div class=\"hero-actions\">\n                <button class=\"btn btn-primary\" @click=\"scrollToServices\">\n                  <i class=\"fas fa-plus-circle\" aria-hidden=\"true\"></i>\n                  Start New Request\n                </button>\n                <button class=\"btn btn-secondary\" @click=\"goToMyRequests\">\n                  <i class=\"fas fa-list-alt\" aria-hidden=\"true\"></i>\n                  View My Requests\n                </button>\n              </div>\n            </div>\n            <div class=\"hero-stats\">\n              <div class=\"stats-grid\">\n                <div class=\"stat-card\">\n                  <div class=\"stat-icon\">\n                    <i class=\"fas fa-file-alt\" aria-hidden=\"true\"></i>\n                  </div>\n                  <div class=\"stat-content\">\n                    <div class=\"stat-number\">{{ totalRequests }}</div>\n                    <div class=\"stat-label\">Total Requests</div>\n                  </div>\n                </div>\n                <div class=\"stat-card\">\n                  <div class=\"stat-icon\">\n                    <i class=\"fas fa-clock\" aria-hidden=\"true\"></i>\n                  </div>\n                  <div class=\"stat-content\">\n                    <div class=\"stat-number\">{{ pendingRequests }}</div>\n                    <div class=\"stat-label\">Pending</div>\n                  </div>\n                </div>\n                <div class=\"stat-card\">\n                  <div class=\"stat-icon\">\n                    <i class=\"fas fa-check-circle\" aria-hidden=\"true\"></i>\n                  </div>\n                  <div class=\"stat-content\">\n                    <div class=\"stat-number\">{{ totalRequests - pendingRequests }}</div>\n                    <div class=\"stat-label\">Completed</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Quick Actions Section -->\n      <section class=\"quick-actions-section\">\n        <div class=\"container\">\n          <div class=\"section-header\">\n            <h2 class=\"section-title\">Quick Actions</h2>\n            <p class=\"section-description\">Common tasks and frequently used services</p>\n          </div>\n\n          <div class=\"quick-actions-grid\">\n            <div class=\"action-card primary\" @click=\"scrollToServices\" role=\"button\" tabindex=\"0\" @keyup.enter=\"scrollToServices\">\n              <div class=\"action-icon\">\n                <i class=\"fas fa-plus-circle\" aria-hidden=\"true\"></i>\n              </div>\n              <div class=\"action-content\">\n                <h3>New Document Request</h3>\n                <p>Start a new request for official documents</p>\n              </div>\n              <div class=\"action-arrow\">\n                <i class=\"fas fa-arrow-right\" aria-hidden=\"true\"></i>\n              </div>\n            </div>\n\n            <div class=\"action-card\" @click=\"goToMyRequests\" role=\"button\" tabindex=\"0\" @keyup.enter=\"goToMyRequests\">\n              <div class=\"action-icon\">\n                <i class=\"fas fa-list-alt\" aria-hidden=\"true\"></i>\n              </div>\n              <div class=\"action-content\">\n                <h3>Track Requests</h3>\n                <p>View status and track your submitted requests</p>\n              </div>\n              <div class=\"action-arrow\">\n                <i class=\"fas fa-arrow-right\" aria-hidden=\"true\"></i>\n              </div>\n            </div>\n\n            <div class=\"action-card\" @click=\"goToProfile\" role=\"button\" tabindex=\"0\" @keyup.enter=\"goToProfile\">\n              <div class=\"action-icon\">\n                <i class=\"fas fa-user-edit\" aria-hidden=\"true\"></i>\n              </div>\n              <div class=\"action-content\">\n                <h3>Update Profile</h3>\n                <p>Manage your account and personal information</p>\n              </div>\n              <div class=\"action-arrow\">\n                <i class=\"fas fa-arrow-right\" aria-hidden=\"true\"></i>\n              </div>\n            </div>\n\n            <div class=\"action-card\" @click=\"contactSupport\" role=\"button\" tabindex=\"0\" @keyup.enter=\"contactSupport\">\n              <div class=\"action-icon\">\n                <i class=\"fas fa-headset\" aria-hidden=\"true\"></i>\n              </div>\n              <div class=\"action-content\">\n                <h3>Get Support</h3>\n                <p>Contact our support team for assistance</p>\n              </div>\n              <div class=\"action-arrow\">\n                <i class=\"fas fa-arrow-right\" aria-hidden=\"true\"></i>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Document Services Section -->\n      <section class=\"services-section\" ref=\"servicesSection\">\n        <div class=\"container\">\n          <div class=\"section-header\">\n            <h2 class=\"section-title\">Available Document Services</h2>\n            <p class=\"section-description\">Select the type of document you need to request</p>\n          </div>\n\n          <!-- Loading State -->\n          <div v-if=\"loading\" class=\"loading-container\" role=\"status\" aria-live=\"polite\">\n            <div class=\"loading-spinner\">\n              <i class=\"fas fa-spinner fa-spin\" aria-hidden=\"true\"></i>\n            </div>\n            <p>Loading available services...</p>\n          </div>\n\n          <!-- Error State -->\n          <div v-else-if=\"error\" class=\"error-container\" role=\"alert\">\n            <div class=\"error-content\">\n              <i class=\"fas fa-exclamation-triangle\" aria-hidden=\"true\"></i>\n              <h3>Unable to Load Services</h3>\n              <p>{{ error }}</p>\n              <button class=\"btn btn-primary retry-btn\" @click=\"loadDocumentTypes\">\n                <i class=\"fas fa-redo\" aria-hidden=\"true\"></i>\n                Try Again\n              </button>\n            </div>\n          </div>\n\n          <!-- Document Types Grid -->\n          <div v-else class=\"document-types-grid\">\n            <div\n              v-for=\"documentType in documentTypes\"\n              :key=\"documentType.id\"\n              class=\"document-card\"\n              @click=\"selectDocumentType(documentType)\"\n              @keyup.enter=\"selectDocumentType(documentType)\"\n              :class=\"{ 'disabled': !documentType.is_active }\"\n              role=\"button\"\n              :tabindex=\"documentType.is_active ? 0 : -1\"\n              :aria-disabled=\"!documentType.is_active\"\n            >\n              <div class=\"document-header\">\n                <div class=\"document-icon\">\n                  <i :class=\"getDocumentIcon(documentType.type_name)\" aria-hidden=\"true\"></i>\n                </div>\n                <div class=\"document-status\">\n                  <span v-if=\"!documentType.is_active\" class=\"status-badge unavailable\">\n                    Unavailable\n                  </span>\n                  <span v-else class=\"status-badge available\">\n                    Available\n                  </span>\n                </div>\n              </div>\n\n              <div class=\"document-content\">\n                <h3 class=\"document-title\">{{ documentType.type_name }}</h3>\n                <p class=\"document-description\">{{ documentType.description }}</p>\n\n                <div class=\"document-details\">\n                  <div class=\"detail-item\">\n                    <i class=\"fas fa-peso-sign\" aria-hidden=\"true\"></i>\n                    <span class=\"detail-label\">Fee:</span>\n                    <span class=\"detail-value fee-amount\">₱{{ formatCurrency(documentType.base_fee) }}</span>\n                  </div>\n\n                  <div class=\"detail-item\">\n                    <i class=\"fas fa-clock\" aria-hidden=\"true\"></i>\n                    <span class=\"detail-label\">Processing:</span>\n                    <span class=\"detail-value\">{{ getProcessingTime(documentType.type_name) }}</span>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"document-action\">\n                <i v-if=\"documentType.is_active\" class=\"fas fa-chevron-right\" aria-hidden=\"true\"></i>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Information and Help Section -->\n      <section class=\"info-section\">\n        <div class=\"container\">\n          <div class=\"info-grid\">\n            <!-- Requirements Card -->\n            <div class=\"info-card requirements-card\">\n              <div class=\"card-header\">\n                <i class=\"fas fa-clipboard-list\" aria-hidden=\"true\"></i>\n                <h3>Before You Start</h3>\n              </div>\n              <div class=\"card-content\">\n                <ul class=\"requirements-list\">\n                  <li class=\"requirement-item\">\n                    <i class=\"fas fa-check-circle\" aria-hidden=\"true\"></i>\n                    <span>Complete and accurate profile information</span>\n                  </li>\n                  <li class=\"requirement-item\">\n                    <i class=\"fas fa-check-circle\" aria-hidden=\"true\"></i>\n                    <span>Valid government-issued ID ready for upload</span>\n                  </li>\n                  <li class=\"requirement-item\">\n                    <i class=\"fas fa-check-circle\" aria-hidden=\"true\"></i>\n                    <span>Supporting documents (if required)</span>\n                  </li>\n                  <li class=\"requirement-item\">\n                    <i class=\"fas fa-check-circle\" aria-hidden=\"true\"></i>\n                    <span>Payment method for processing fees</span>\n                  </li>\n                </ul>\n              </div>\n            </div>\n\n            <!-- Process Card -->\n            <div class=\"info-card process-card\">\n              <div class=\"card-header\">\n                <i class=\"fas fa-route\" aria-hidden=\"true\"></i>\n                <h3>How It Works</h3>\n              </div>\n              <div class=\"card-content\">\n                <ol class=\"process-steps\">\n                  <li class=\"process-step\">\n                    <div class=\"step-number\">1</div>\n                    <div class=\"step-content\">\n                      <strong>Select Document</strong>\n                      <span>Choose the document type you need</span>\n                    </div>\n                  </li>\n                  <li class=\"process-step\">\n                    <div class=\"step-number\">2</div>\n                    <div class=\"step-content\">\n                      <strong>Fill Application</strong>\n                      <span>Complete the required information</span>\n                    </div>\n                  </li>\n                  <li class=\"process-step\">\n                    <div class=\"step-number\">3</div>\n                    <div class=\"step-content\">\n                      <strong>Submit & Pay</strong>\n                      <span>Review and submit with payment</span>\n                    </div>\n                  </li>\n                  <li class=\"process-step\">\n                    <div class=\"step-number\">4</div>\n                    <div class=\"step-content\">\n                      <strong>Track Progress</strong>\n                      <span>Monitor your request status</span>\n                    </div>\n                  </li>\n                </ol>\n              </div>\n            </div>\n\n            <!-- Help Card -->\n            <div class=\"info-card help-card\">\n              <div class=\"card-header\">\n                <i class=\"fas fa-headset\" aria-hidden=\"true\"></i>\n                <h3>Need Assistance?</h3>\n              </div>\n              <div class=\"card-content\">\n                <p class=\"help-description\">Our support team is here to help you with any questions about document requirements or the application process.</p>\n                <div class=\"help-actions\">\n                  <button class=\"btn btn-outline help-btn\" @click=\"openHelp\">\n                    <i class=\"fas fa-question-circle\" aria-hidden=\"true\"></i>\n                    View FAQ\n                  </button>\n                  <button class=\"btn btn-outline contact-btn\" @click=\"contactSupport\">\n                    <i class=\"fas fa-phone\" aria-hidden=\"true\"></i>\n                    Contact Support\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n    </main>\n  </div>\n</template>\n\n<script>\nimport documentRequestService from '@/services/documentRequestService';\nimport ClientHeader from './ClientHeader.vue';\nimport unifiedAuthService from '@/services/unifiedAuthService';\n\nexport default {\n  name: 'NewDocumentRequest',\n  components: {\n    ClientHeader\n  },\n  data() {\n    return {\n      documentTypes: [],\n      loading: true,\n      error: null,\n      // Header state\n      showUserDropdown: false,\n      sidebarCollapsed: false,\n      activeMenu: 'dashboard',\n      // User data\n      userName: 'User',\n      userEmail: '<EMAIL>',\n      userAvatar: null,\n      firstName: 'User',\n      totalRequests: 0,\n      pendingRequests: 0\n    };\n  },\n  async mounted() {\n    await this.loadUserData();\n    await this.loadDocumentTypes();\n    await this.loadUserStats();\n  },\n  methods: {\n    async loadUserData() {\n      try {\n        const currentUser = unifiedAuthService.getCurrentUser();\n        if (currentUser) {\n          this.userName = currentUser.username || 'User';\n          this.userEmail = currentUser.email || '<EMAIL>';\n          this.firstName = currentUser.first_name || currentUser.username || 'User';\n          // Set user avatar if available\n          this.userAvatar = currentUser.avatar || null;\n        }\n      } catch (error) {\n        console.error('Error loading user data:', error);\n      }\n    },\n\n    async loadUserStats() {\n      try {\n        // TODO: Implement API call to get user statistics\n        // For now, using placeholder data\n        this.totalRequests = 5;\n        this.pendingRequests = 2;\n      } catch (error) {\n        console.error('Error loading user stats:', error);\n      }\n    },\n\n    async loadDocumentTypes() {\n      try {\n        this.loading = true;\n        this.error = null;\n\n        const response = await documentRequestService.getDocumentTypes();\n        this.documentTypes = response.data || [];\n\n      } catch (error) {\n        console.error('Error loading document types:', error);\n        this.error = error.response?.data?.message || 'Failed to load available services';\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    selectDocumentType(documentType) {\n      if (!documentType.is_active) return;\n\n      // Navigate to specific document request form\n      const routeName = this.getRouteForDocumentType(documentType.type_name);\n      if (routeName) {\n        this.$router.push({ \n          name: routeName,\n          params: { documentTypeId: documentType.id }\n        });\n      }\n    },\n\n    getRouteForDocumentType(typeName) {\n      const routes = {\n        'Barangay Clearance': 'BarangayClearanceRequest',\n        'Cedula': 'CedulaRequest'\n      };\n      return routes[typeName];\n    },\n\n    getDocumentIcon(typeName) {\n      const icons = {\n        'Barangay Clearance': 'fas fa-certificate',\n        'Cedula': 'fas fa-id-card'\n      };\n      return icons[typeName] || 'fas fa-file-alt';\n    },\n\n    getProcessingTime(typeName) {\n      const times = {\n        'Barangay Clearance': '3-5 business days',\n        'Cedula': '1-2 business days'\n      };\n      return times[typeName] || '3-5 business days';\n    },\n\n    formatCurrency(amount) {\n      return parseFloat(amount).toFixed(2);\n    },\n\n    // Header event handlers\n    handleSidebarToggle() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n    },\n\n    handleUserDropdownToggle() {\n      this.showUserDropdown = !this.showUserDropdown;\n    },\n\n    handleMenuAction(action) {\n      console.log('Menu action:', action);\n      switch (action) {\n        case 'profile':\n          // TODO: Navigate to profile page\n          break;\n        case 'settings':\n          // TODO: Navigate to settings page\n          break;\n        case 'account':\n          // TODO: Navigate to account page\n          break;\n      }\n    },\n\n    handleLogout() {\n      try {\n        unifiedAuthService.logout();\n        this.$router.push({ name: 'WelcomePage' });\n      } catch (error) {\n        console.error('Logout error:', error);\n      }\n    },\n\n    handleError(error) {\n      console.error('Header error:', error);\n    },\n\n    // Search handler\n    handleSearch(query) {\n      console.log('Search query:', query);\n      // TODO: Implement search functionality\n      // This could search through documents, services, or requests\n      // For now, we'll just log the query\n    },\n\n    // Navigation methods\n    scrollToServices() {\n      this.$refs.servicesSection?.scrollIntoView({ behavior: 'smooth' });\n    },\n\n    goToMyRequests() {\n      this.$router.push({ name: 'MyRequests' });\n    },\n\n    goToProfile() {\n      // TODO: Navigate to profile page\n      console.log('Navigate to profile');\n    },\n\n    goBack() {\n      this.$router.push({ name: 'ClientDashboard' });\n    },\n\n    openHelp() {\n      // TODO: Implement help/FAQ modal or page\n      console.log('Opening help...');\n    },\n\n    contactSupport() {\n      // TODO: Implement contact support functionality\n      console.log('Contacting support...');\n    }\n  }\n};\n</script>\n\n<style scoped>\n/* Modern Government Portal Styles - USWDS Inspired */\n:root {\n  /* Government Colors */\n  --gov-blue: #005ea2;\n  --gov-blue-dark: #0f4c96;\n  --gov-blue-light: #2378c3;\n  --gov-blue-lighter: #e7f6f8;\n  --gov-red: #d63384;\n  --gov-green: #00a91c;\n  --gov-yellow: #ffbe2e;\n  --gov-yellow-light: #fef0cd;\n\n  /* Neutral Colors */\n  --text-primary: #1b1b1b;\n  --text-secondary: #454545;\n  --text-light: #757575;\n  --text-white: #ffffff;\n\n  /* Background Colors */\n  --bg-white: #ffffff;\n  --bg-gray-5: #f9f9f9;\n  --bg-gray-10: #f0f0f0;\n  --bg-gray-20: #dfe1e2;\n\n  /* Shadows */\n  --shadow-1: 0 1px 2px 0 rgba(0, 0, 0, 0.1);\n  --shadow-2: 0 1px 4px 0 rgba(0, 0, 0, 0.1);\n  --shadow-3: 0 4px 8px 0 rgba(0, 0, 0, 0.1);\n  --shadow-4: 0 8px 16px 0 rgba(0, 0, 0, 0.1);\n\n  /* Typography */\n  --font-family-sans: 'Source Sans Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n  --font-family-serif: 'Merriweather', Georgia, serif;\n\n  /* Spacing */\n  --spacing-1: 0.25rem;\n  --spacing-2: 0.5rem;\n  --spacing-3: 0.75rem;\n  --spacing-4: 1rem;\n  --spacing-5: 1.25rem;\n  --spacing-6: 1.5rem;\n  --spacing-8: 2rem;\n  --spacing-10: 2.5rem;\n  --spacing-12: 3rem;\n  --spacing-16: 4rem;\n\n  /* Border Radius */\n  --border-radius-sm: 0.125rem;\n  --border-radius-md: 0.25rem;\n  --border-radius-lg: 0.5rem;\n  --border-radius-xl: 1rem;\n\n  /* Transitions */\n  --transition-fast: 0.15s ease-in-out;\n  --transition-base: 0.2s ease-in-out;\n}\n\n/* Reset and base styles */\n* {\n  box-sizing: border-box;\n}\n\n.client-dashboard {\n  font-family: var(--font-family-sans);\n  line-height: 1.6;\n  color: var(--text-primary);\n  background: linear-gradient(135deg, var(--gov-blue-lighter) 0%, var(--bg-white) 100%);\n  min-height: 100vh;\n}\n\n/* Background Setup */\n.background-container {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: -2;\n}\n\n.background-image {\n  width: 100%;\n  height: 100%;\n  background-image: url('@/assets/bula-request-background-pic.png');\n  background-size: cover;\n  background-position: center;\n  background-repeat: no-repeat;\n  background-attachment: fixed;\n  opacity: 0.1;\n}\n\n.background-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    135deg,\n    rgba(0, 94, 162, 0.05) 0%,\n    rgba(35, 120, 195, 0.1) 100%\n  );\n  z-index: -1;\n}\n\n/* Container utility */\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 var(--spacing-4);\n}\n\n/* Main Content */\n.main-content {\n  margin-top: 140px; /* Account for new fixed header (gov banner + main header + breadcrumb) */\n  transition: margin-left var(--transition-base);\n}\n\n.main-content.sidebar-collapsed {\n  margin-left: 0;\n}\n\n/* Modern Button Styles */\n.btn {\n  display: inline-flex;\n  align-items: center;\n  gap: var(--spacing-2);\n  padding: var(--spacing-3) var(--spacing-6);\n  font-family: var(--font-family-sans);\n  font-size: 0.875rem;\n  font-weight: 600;\n  line-height: 1.2;\n  text-decoration: none;\n  border: 2px solid transparent;\n  border-radius: var(--border-radius-md);\n  cursor: pointer;\n  transition: all var(--transition-fast);\n  white-space: nowrap;\n}\n\n.btn:focus {\n  outline: 2px solid var(--gov-yellow);\n  outline-offset: 2px;\n}\n\n.btn-primary {\n  background-color: var(--gov-blue);\n  color: var(--text-white);\n  border-color: var(--gov-blue);\n}\n\n.btn-primary:hover {\n  background-color: var(--gov-blue-dark);\n  border-color: var(--gov-blue-dark);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-3);\n}\n\n.btn-secondary {\n  background-color: var(--bg-white);\n  color: var(--gov-blue);\n  border-color: var(--gov-blue);\n}\n\n.btn-secondary:hover {\n  background-color: var(--gov-blue);\n  color: var(--text-white);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-3);\n}\n\n.btn-outline {\n  background-color: transparent;\n  color: var(--gov-blue);\n  border-color: var(--gov-blue);\n}\n\n.btn-outline:hover {\n  background-color: var(--gov-blue);\n  color: var(--text-white);\n}\n\n/* Hero Section */\n.hero-section {\n  padding: var(--spacing-8) 0;\n  position: relative;\n  z-index: 1;\n  background: var(--bg-white);\n  margin: var(--spacing-4);\n  border-radius: var(--border-radius-xl);\n  box-shadow: var(--shadow-2);\n}\n\n.hero-content {\n  display: grid;\n  grid-template-columns: 1fr auto;\n  gap: var(--spacing-8);\n  align-items: start;\n  padding: var(--spacing-8);\n}\n\n.hero-text {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-4);\n}\n\n.hero-title {\n  font-size: clamp(1.75rem, 4vw, 2.5rem);\n  font-weight: 700;\n  color: var(--gov-blue);\n  margin: 0;\n  line-height: 1.2;\n  font-family: var(--font-family-serif);\n}\n\n.hero-subtitle {\n  font-size: 1.125rem;\n  color: var(--text-secondary);\n  margin: 0;\n  line-height: 1.5;\n  max-width: 600px;\n}\n\n.hero-actions {\n  display: flex;\n  gap: var(--spacing-3);\n  flex-wrap: wrap;\n  margin-top: var(--spacing-2);\n}\n\n.hero-stats {\n  display: flex;\n  justify-content: center;\n  min-width: 280px;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: var(--spacing-3);\n  width: 100%;\n}\n\n.stat-card {\n  background: linear-gradient(135deg, var(--gov-blue), var(--gov-blue-light));\n  border-radius: var(--border-radius-lg);\n  padding: var(--spacing-6);\n  color: var(--text-white);\n  text-align: center;\n  flex: 1;\n  box-shadow: var(--shadow-3);\n  transition: all var(--transition-fast);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.stat-card:hover {\n  transform: translateY(-2px);\n  box-shadow: var(--shadow-4);\n}\n\n.stat-icon {\n  font-size: 2rem;\n  color: var(--gov-yellow);\n  margin-bottom: var(--spacing-3);\n  display: block;\n}\n\n.stat-number {\n  font-size: 2.25rem;\n  font-weight: 700;\n  margin-bottom: var(--spacing-1);\n  color: var(--gov-yellow);\n  font-family: var(--font-family-serif);\n  line-height: 1;\n}\n\n.stat-label {\n  font-size: 0.875rem;\n  opacity: 0.9;\n  font-weight: 500;\n  line-height: 1.2;\n}\n\n/* Quick Actions Section */\n.quick-actions-section {\n  padding: 2rem 0;\n  position: relative;\n  z-index: 1;\n}\n\n.section-header {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n\n.section-title {\n  font-size: clamp(1.75rem, 4vw, 2.25rem);\n  font-weight: 700;\n  color: white;\n  margin-bottom: 0.75rem;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n}\n\n.section-description {\n  font-size: 1.125rem;\n  color: rgba(255, 255, 255, 0.9);\n  margin: 0;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\n}\n\n.quick-actions-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: 1.5rem;\n}\n\n.action-card {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: 1rem;\n  padding: 2rem;\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n}\n\n.action-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);\n  border-color: #1e3a8a;\n}\n\n.action-card.primary {\n  background: linear-gradient(135deg, #1e3a8a, #1e40af);\n  color: white;\n}\n\n.action-card.primary:hover {\n  border-color: #fbbf24;\n}\n\n.action-icon {\n  width: 4rem;\n  height: 4rem;\n  background: linear-gradient(135deg, #fbbf24, #f59e0b);\n  border-radius: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.5rem;\n  color: #1e3a8a;\n  flex-shrink: 0;\n}\n\n.action-card.primary .action-icon {\n  background: rgba(251, 191, 36, 0.2);\n  color: #fbbf24;\n}\n\n.action-content {\n  flex: 1;\n}\n\n.action-content h3 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  color: inherit;\n}\n\n.action-content p {\n  margin: 0;\n  opacity: 0.8;\n  line-height: 1.5;\n}\n\n.action-arrow {\n  color: #6b7280;\n  font-size: 1.25rem;\n}\n\n.action-card.primary .action-arrow {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n/* Services Section */\n.services-section {\n  padding: 2rem 0;\n  position: relative;\n  z-index: 1;\n}\n\n.services-section .section-title {\n  color: white;\n}\n\n.services-section .section-description {\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.loading-container, .error-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n  text-align: center;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: 1rem;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.loading-spinner i {\n  font-size: 3rem;\n  color: #1e3a8a;\n  margin-bottom: 1rem;\n}\n\n.error-content {\n  max-width: 400px;\n}\n\n.error-content i {\n  font-size: 3rem;\n  color: #dc2626;\n  margin-bottom: 1rem;\n}\n\n.retry-btn {\n  background: linear-gradient(135deg, #1e3a8a, #1e40af);\n  color: white;\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 0.75rem;\n  cursor: pointer;\n  margin-top: 1rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);\n}\n\n.retry-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.4);\n}\n\n.document-types-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n  gap: 1.5rem;\n}\n\n.document-card {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border: 2px solid rgba(30, 58, 138, 0.2);\n  border-radius: 1rem;\n  padding: 2rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n}\n\n.document-card:hover:not(.disabled) {\n  border-color: #1e3a8a;\n  transform: translateY(-5px);\n  box-shadow: 0 12px 30px rgba(30, 58, 138, 0.2);\n  background: rgba(255, 255, 255, 1);\n}\n\n.document-card.disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.document-icon {\n  flex-shrink: 0;\n  width: 4rem;\n  height: 4rem;\n  background: linear-gradient(135deg, #1e3a8a, #1e40af);\n  border-radius: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 1.5rem;\n  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);\n}\n\n.document-content {\n  flex: 1;\n}\n\n.document-title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1e3a8a;\n  margin-bottom: 0.5rem;\n}\n\n.document-description {\n  color: #6b7280;\n  margin-bottom: 1rem;\n  line-height: 1.5;\n}\n\n.document-details {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.fee-info {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.fee-label {\n  color: #9ca3af;\n  font-size: 0.9rem;\n}\n\n.fee-amount {\n  font-weight: 600;\n  color: #059669;\n  font-size: 1.1rem;\n}\n\n.processing-time {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #9ca3af;\n  font-size: 0.9rem;\n}\n\n.document-action {\n  flex-shrink: 0;\n  display: flex;\n  align-items: center;\n}\n\n.document-action i {\n  color: #d1d5db;\n  font-size: 1.25rem;\n}\n\n.status-badge.unavailable {\n  background: #fecaca;\n  color: #dc2626;\n  padding: 0.25rem 0.75rem;\n  border-radius: 1rem;\n  font-size: 0.8rem;\n  font-weight: 500;\n}\n\n/* Info Section */\n.info-section {\n  padding: 2rem 0;\n  position: relative;\n  z-index: 1;\n}\n\n.info-grid {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 2rem;\n}\n\n.info-card, .help-card {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(30, 58, 138, 0.2);\n  border-radius: 1rem;\n  padding: 2rem;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n}\n\n.info-card:hover, .help-card:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 8px 30px rgba(30, 58, 138, 0.15);\n  background: rgba(255, 255, 255, 1);\n  border-color: #1e3a8a;\n}\n\n.info-header, .help-header {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  margin-bottom: 1.5rem;\n}\n\n.info-header h3, .help-header h3 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1e3a8a;\n  margin: 0;\n}\n\n.info-header i {\n  color: #1e3a8a;\n  font-size: 1.25rem;\n}\n\n.help-header i {\n  color: #059669;\n  font-size: 1.25rem;\n}\n\n.info-list {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.info-list li {\n  display: flex;\n  align-items: flex-start;\n  gap: 0.75rem;\n  margin-bottom: 1rem;\n  color: #6b7280;\n  line-height: 1.6;\n}\n\n.info-list i {\n  color: #059669;\n  margin-top: 0.125rem;\n  flex-shrink: 0;\n}\n\n.help-content p {\n  color: #6b7280;\n  margin-bottom: 1.5rem;\n  line-height: 1.6;\n}\n\n.help-actions {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.help-btn, .contact-btn {\n  background: rgba(255, 255, 255, 0.9);\n  backdrop-filter: blur(5px);\n  border: 2px solid rgba(30, 58, 138, 0.3);\n  padding: 0.875rem 1.25rem;\n  border-radius: 0.75rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  color: #6b7280;\n  font-weight: 500;\n  font-size: 0.95rem;\n}\n\n.help-btn:hover {\n  border-color: #1e3a8a;\n  color: #1e3a8a;\n  background: rgba(30, 58, 138, 0.1);\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.2);\n}\n\n.contact-btn:hover {\n  border-color: #059669;\n  color: #059669;\n  background: rgba(5, 150, 105, 0.1);\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(5, 150, 105, 0.2);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .container {\n    padding: 0 0.75rem;\n  }\n\n  .welcome-header {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n    padding: 1.5rem;\n  }\n\n  .welcome-stats {\n    justify-content: center;\n  }\n\n  .stat-card {\n    min-width: 120px;\n  }\n\n  .quick-actions-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n\n  .action-card {\n    padding: 1.5rem;\n  }\n\n  .document-types-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n\n  .document-card {\n    flex-direction: column;\n    text-align: center;\n    padding: 1.5rem;\n  }\n\n  .info-grid {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n\n  .info-card, .help-card {\n    padding: 1.5rem;\n  }\n\n  .loading-container, .error-container {\n    padding: 3rem 1.5rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .container {\n    padding: 0 0.5rem;\n  }\n\n  .welcome-section,\n  .quick-actions-section,\n  .services-section,\n  .info-section {\n    padding: 1.5rem 0;\n  }\n\n  .welcome-header {\n    padding: 1rem;\n  }\n\n  .stat-card {\n    padding: 1rem;\n  }\n\n  .stat-number {\n    font-size: 1.5rem;\n  }\n\n  .action-card {\n    padding: 1rem;\n    flex-direction: column;\n    text-align: center;\n    gap: 1rem;\n  }\n\n  .action-icon {\n    width: 3rem;\n    height: 3rem;\n  }\n\n  .document-card {\n    padding: 1rem;\n  }\n\n  .info-card, .help-card {\n    padding: 1rem;\n  }\n\n  .loading-container, .error-container {\n    padding: 2rem 1rem;\n  }\n}\n\n/* Animations */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.welcome-section,\n.quick-actions-section,\n.services-section,\n.info-section {\n  animation: fadeInUp 0.8s ease-out;\n}\n\n.quick-actions-section {\n  animation-delay: 0.2s;\n}\n\n.services-section {\n  animation-delay: 0.4s;\n}\n\n.info-section {\n  animation-delay: 0.6s;\n}\n\n/* Accessibility */\n@media (prefers-reduced-motion: reduce) {\n  * {\n    animation: none !important;\n    transition: none !important;\n  }\n\n  .action-card:hover,\n  .document-card:hover,\n  .info-card:hover,\n  .help-card:hover {\n    transform: none;\n  }\n}\n\n/* Focus styles */\n.action-card:focus,\n.document-card:focus,\n.help-btn:focus,\n.contact-btn:focus,\n.retry-btn:focus {\n  outline: 3px solid #fbbf24;\n  outline-offset: 2px;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAkB;;EA2BhBA,KAAK,EAAC;AAAc;;EACtBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAW;;EAChBA,KAAK,EAAC;AAAY;;EAEjBA,KAAK,EAAC;AAAc;;EAWtBA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAW;;EAIfA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAa;;EAIvBA,KAAK,EAAC;AAAW;;EAIfA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAa;;EAIvBA,KAAK,EAAC;AAAW;;EAIfA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAa;;EAW7BA,KAAK,EAAC;AAAuB;;EAC/BA,KAAK,EAAC;AAAW;;EAMfA,KAAK,EAAC;AAAoB;;EAyD1BA,KAAK,EAAC,kBAAkB;EAACC,GAAG,EAAC;;;EAC/BD,KAAK,EAAC;AAAW;;;EAOAA,KAAK,EAAC,mBAAmB;EAACE,IAAI,EAAC,QAAQ;EAAC,WAAS,EAAC;;;EAQ/CF,KAAK,EAAC,iBAAiB;EAACE,IAAI,EAAC;;;EAC7CF,KAAK,EAAC;AAAe;;EAYhBA,KAAK,EAAC;AAAqB;;;EAY9BA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAe;;EAGrBA,KAAK,EAAC;AAAiB;;;EACWA,KAAK,EAAC;;;;EAG9BA,KAAK,EAAC;;;EAMlBA,KAAK,EAAC;AAAkB;;EACvBA,KAAK,EAAC;AAAgB;;EACvBA,KAAK,EAAC;AAAsB;;EAE1BA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAa;;EAGhBA,KAAK,EAAC;AAAyB;;EAGlCA,KAAK,EAAC;AAAa;;EAGhBA,KAAK,EAAC;AAAc;;EAK3BA,KAAK,EAAC;AAAiB;;;EACOA,KAAK,EAAC,sBAAsB;EAAC,aAAW,EAAC;;;EAQ3EA,KAAK,EAAC;AAAc;;EACtBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAW;;EAsEfA,KAAK,EAAC;AAAqB;;EAKzBA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAc;;;uBAlTvCG,mBAAA,CAkUM,OAlUNC,UAkUM,GAjUJC,mBAAA,gBAAmB,E,4BACnBC,mBAAA,CAGM;IAHDN,KAAK,EAAC;EAAsB,IAC/BM,mBAAA,CAAoC;IAA/BN,KAAK,EAAC;EAAkB,IAC7BM,mBAAA,CAAsC;IAAjCN,KAAK,EAAC;EAAoB,G,sBAGjCK,mBAAA,mCAAsC,EACtCE,YAAA,CAcEC,uBAAA;IAbCC,QAAQ,EAAEC,KAAA,CAAAD,QAAQ;IAClBE,SAAS,EAAED,KAAA,CAAAC,SAAS;IACpBC,UAAU,EAAEF,KAAA,CAAAE,UAAU;IACtBC,gBAAgB,EAAEH,KAAA,CAAAG,gBAAgB;IAClCC,gBAAgB,EAAEJ,KAAA,CAAAI,gBAAgB;IAClCC,UAAU,EAAEL,KAAA,CAAAK,UAAU;IACtBC,eAAe,EAAE,IAAI;IACrBC,eAAc,EAAEC,QAAA,CAAAC,mBAAmB;IACnCC,oBAAoB,EAAEF,QAAA,CAAAG,wBAAwB;IAC9CC,YAAW,EAAEJ,QAAA,CAAAK,gBAAgB;IAC7BC,QAAM,EAAEN,QAAA,CAAAO,YAAY;IACpBC,OAAK,EAAER,QAAA,CAAAS,WAAW;IAClBC,QAAM,EAAEV,QAAA,CAAAW;wNAGXxB,mBAAA,kBAAqB,EACrBC,mBAAA,CAwSO;IAxSDN,KAAK,EAAA8B,eAAA,EAAC,cAAc;MAAA,qBAAgCpB,KAAA,CAAAI;IAAgB;MACxET,mBAAA,kBAAqB,EACrBC,mBAAA,CAkDU,WAlDVyB,UAkDU,GAjDRzB,mBAAA,CAgDM,OAhDN0B,UAgDM,GA/CJ1B,mBAAA,CA8CM,OA9CN2B,UA8CM,GA7CJ3B,mBAAA,CAaM,OAbN4B,UAaM,GAZJ5B,mBAAA,CAA0D,MAA1D6B,UAA0D,EAAnC,gBAAc,GAAAC,gBAAA,CAAG1B,KAAA,CAAA2B,SAAS,IAAG,GAAC,iB,4BACrD/B,mBAAA,CAA2H;IAAxHN,KAAK,EAAC;EAAe,GAAC,gGAA8F,sBACvHM,mBAAA,CASM,OATNgC,UASM,GARJhC,mBAAA,CAGS;IAHDN,KAAK,EAAC,iBAAiB;IAAEuC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEvB,QAAA,CAAAwB,gBAAA,IAAAxB,QAAA,CAAAwB,gBAAA,IAAAD,IAAA,CAAgB;kCACtDnC,mBAAA,CAAqD;IAAlDN,KAAK,EAAC,oBAAoB;IAAC,aAAW,EAAC;+CAAW,qBAEvD,E,IACAM,mBAAA,CAGS;IAHDN,KAAK,EAAC,mBAAmB;IAAEuC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEvB,QAAA,CAAAyB,cAAA,IAAAzB,QAAA,CAAAyB,cAAA,IAAAF,IAAA,CAAc;kCACtDnC,mBAAA,CAAkD;IAA/CN,KAAK,EAAC,iBAAiB;IAAC,aAAW,EAAC;+CAAW,oBAEpD,E,QAGJM,mBAAA,CA8BM,OA9BNsC,UA8BM,GA7BJtC,mBAAA,CA4BM,OA5BNuC,UA4BM,GA3BJvC,mBAAA,CAQM,OARNwC,WAQM,G,4BAPJxC,mBAAA,CAEM;IAFDN,KAAK,EAAC;EAAW,IACpBM,mBAAA,CAAkD;IAA/CN,KAAK,EAAC,iBAAiB;IAAC,aAAW,EAAC;2BAEzCM,mBAAA,CAGM,OAHNyC,WAGM,GAFJzC,mBAAA,CAAkD,OAAlD0C,WAAkD,EAAAZ,gBAAA,CAAtB1B,KAAA,CAAAuC,aAAa,kB,4BACzC3C,mBAAA,CAA4C;IAAvCN,KAAK,EAAC;EAAY,GAAC,gBAAc,qB,KAG1CM,mBAAA,CAQM,OARN4C,WAQM,G,4BAPJ5C,mBAAA,CAEM;IAFDN,KAAK,EAAC;EAAW,IACpBM,mBAAA,CAA+C;IAA5CN,KAAK,EAAC,cAAc;IAAC,aAAW,EAAC;2BAEtCM,mBAAA,CAGM,OAHN6C,WAGM,GAFJ7C,mBAAA,CAAoD,OAApD8C,WAAoD,EAAAhB,gBAAA,CAAxB1B,KAAA,CAAA2C,eAAe,kB,4BAC3C/C,mBAAA,CAAqC;IAAhCN,KAAK,EAAC;EAAY,GAAC,SAAO,qB,KAGnCM,mBAAA,CAQM,OARNgD,WAQM,G,4BAPJhD,mBAAA,CAEM;IAFDN,KAAK,EAAC;EAAW,IACpBM,mBAAA,CAAsD;IAAnDN,KAAK,EAAC,qBAAqB;IAAC,aAAW,EAAC;2BAE7CM,mBAAA,CAGM,OAHNiD,WAGM,GAFJjD,mBAAA,CAAoE,OAApEkD,WAAoE,EAAApB,gBAAA,CAAxC1B,KAAA,CAAAuC,aAAa,GAAGvC,KAAA,CAAA2C,eAAe,kB,4BAC3D/C,mBAAA,CAAuC;IAAlCN,KAAK,EAAC;EAAY,GAAC,WAAS,qB,eAS/CK,mBAAA,2BAA8B,EAC9BC,mBAAA,CA6DU,WA7DVmD,WA6DU,GA5DRnD,mBAAA,CA2DM,OA3DNoD,WA2DM,G,4BA1DJpD,mBAAA,CAGM;IAHDN,KAAK,EAAC;EAAgB,IACzBM,mBAAA,CAA4C;IAAxCN,KAAK,EAAC;EAAe,GAAC,eAAa,GACvCM,mBAAA,CAA4E;IAAzEN,KAAK,EAAC;EAAqB,GAAC,2CAAyC,E,sBAG1EM,mBAAA,CAoDM,OApDNqD,WAoDM,GAnDJrD,mBAAA,CAWM;IAXDN,KAAK,EAAC,qBAAqB;IAAEuC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEvB,QAAA,CAAAwB,gBAAA,IAAAxB,QAAA,CAAAwB,gBAAA,IAAAD,IAAA,CAAgB;IAAEvC,IAAI,EAAC,QAAQ;IAAC0D,QAAQ,EAAC,GAAG;IAAEC,OAAK,EAAArB,MAAA,QAAAA,MAAA,MAAAsB,SAAA,KAAArB,IAAA,KAAQvB,QAAA,CAAAwB,gBAAA,IAAAxB,QAAA,CAAAwB,gBAAA,IAAAD,IAAA,CAAgB;gfAapHnC,mBAAA,CAWM;IAXDN,KAAK,EAAC,aAAa;IAAEuC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEvB,QAAA,CAAAyB,cAAA,IAAAzB,QAAA,CAAAyB,cAAA,IAAAF,IAAA,CAAc;IAAEvC,IAAI,EAAC,QAAQ;IAAC0D,QAAQ,EAAC,GAAG;IAAEC,OAAK,EAAArB,MAAA,QAAAA,MAAA,MAAAsB,SAAA,KAAArB,IAAA,KAAQvB,QAAA,CAAAyB,cAAA,IAAAzB,QAAA,CAAAyB,cAAA,IAAAF,IAAA,CAAc;0eAaxGnC,mBAAA,CAWM;IAXDN,KAAK,EAAC,aAAa;IAAEuC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEvB,QAAA,CAAA6C,WAAA,IAAA7C,QAAA,CAAA6C,WAAA,IAAAtB,IAAA,CAAW;IAAEvC,IAAI,EAAC,QAAQ;IAAC0D,QAAQ,EAAC,GAAG;IAAEC,OAAK,EAAArB,MAAA,QAAAA,MAAA,MAAAsB,SAAA,KAAArB,IAAA,KAAQvB,QAAA,CAAA6C,WAAA,IAAA7C,QAAA,CAAA6C,WAAA,IAAAtB,IAAA,CAAW;0eAalGnC,mBAAA,CAWM;IAXDN,KAAK,EAAC,aAAa;IAAEuC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEvB,QAAA,CAAA8C,cAAA,IAAA9C,QAAA,CAAA8C,cAAA,IAAAvB,IAAA,CAAc;IAAEvC,IAAI,EAAC,QAAQ;IAAC0D,QAAQ,EAAC,GAAG;IAAEC,OAAK,EAAArB,MAAA,QAAAA,MAAA,MAAAsB,SAAA,KAAArB,IAAA,KAAQvB,QAAA,CAAA8C,cAAA,IAAA9C,QAAA,CAAA8C,cAAA,IAAAvB,IAAA,CAAc;seAgB9GpC,mBAAA,+BAAkC,EAClCC,mBAAA,CAgFU,WAhFV2D,WAgFU,GA/ER3D,mBAAA,CA8EM,OA9EN4D,WA8EM,G,4BA7EJ5D,mBAAA,CAGM;IAHDN,KAAK,EAAC;EAAgB,IACzBM,mBAAA,CAA0D;IAAtDN,KAAK,EAAC;EAAe,GAAC,6BAA2B,GACrDM,mBAAA,CAAkF;IAA/EN,KAAK,EAAC;EAAqB,GAAC,iDAA+C,E,sBAGhFK,mBAAA,mBAAsB,EACXK,KAAA,CAAAyD,OAAO,I,cAAlBhE,mBAAA,CAKM,OALNiE,WAKM,EAAA5B,MAAA,SAAAA,MAAA,QAJJlC,mBAAA,CAEM;IAFDN,KAAK,EAAC;EAAiB,IAC1BM,mBAAA,CAAyD;IAAtDN,KAAK,EAAC,wBAAwB;IAAC,aAAW,EAAC;0BAEhDM,mBAAA,CAAoC,WAAjC,+BAA6B,oB,MAIlBI,KAAA,CAAA2D,KAAK,I,cAArBlE,mBAAA,CAUMmE,SAAA;IAAAC,GAAA;EAAA,IAXNlE,mBAAA,iBAAoB,EACpBC,mBAAA,CAUM,OAVNkE,WAUM,GATJlE,mBAAA,CAQM,OARNmE,WAQM,G,4BAPJnE,mBAAA,CAA8D;IAA3DN,KAAK,EAAC,6BAA6B;IAAC,aAAW,EAAC;2DACnDM,mBAAA,CAAgC,YAA5B,yBAAuB,sBAC3BA,mBAAA,CAAkB,WAAA8B,gBAAA,CAAZ1B,KAAA,CAAA2D,KAAK,kBACX/D,mBAAA,CAGS;IAHDN,KAAK,EAAC,2BAA2B;IAAEuC,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEvB,QAAA,CAAAwD,iBAAA,IAAAxD,QAAA,CAAAwD,iBAAA,IAAAjC,IAAA,CAAiB;kCACjEnC,mBAAA,CAA8C;IAA3CN,KAAK,EAAC,aAAa;IAAC,aAAW,EAAC;+CAAW,aAEhD,E,0EAKJG,mBAAA,CAiDMmE,SAAA;IAAAC,GAAA;EAAA,IAlDNlE,mBAAA,yBAA4B,EAC5BC,mBAAA,CAiDM,OAjDNqE,WAiDM,I,kBAhDJxE,mBAAA,CA+CMmE,SAAA,QAAAM,WAAA,CA9CmBlE,KAAA,CAAAmE,aAAa,EAA7BC,YAAY;yBADrB3E,mBAAA,CA+CM;MA7CHoE,GAAG,EAAEO,YAAY,CAACC,EAAE;MACrB/E,KAAK,EAAA8B,eAAA,EAAC,eAAe;QAAA,aAGEgD,YAAY,CAACE;MAAS;MAF5CzC,OAAK,EAAA0C,MAAA,IAAE/D,QAAA,CAAAgE,kBAAkB,CAACJ,YAAY;MACtCjB,OAAK,EAAAC,SAAA,CAAAmB,MAAA,IAAQ/D,QAAA,CAAAgE,kBAAkB,CAACJ,YAAY;MAE7C5E,IAAI,EAAC,QAAQ;MACZ0D,QAAQ,EAAEkB,YAAY,CAACE,SAAS;MAChC,eAAa,GAAGF,YAAY,CAACE;QAE9B1E,mBAAA,CAYM,OAZN6E,WAYM,GAXJ7E,mBAAA,CAEM,OAFN8E,WAEM,GADJ9E,mBAAA,CAA2E;MAAvEN,KAAK,EAAA8B,eAAA,CAAEZ,QAAA,CAAAmE,eAAe,CAACP,YAAY,CAACQ,SAAS;MAAG,aAAW,EAAC;+BAElEhF,mBAAA,CAOM,OAPNiF,WAOM,G,CANST,YAAY,CAACE,SAAS,I,cAAnC7E,mBAAA,CAEO,QAFPqF,WAEO,EAF+D,eAEtE,M,cACArF,mBAAA,CAEO,QAFPsF,WAEO,EAFqC,aAE5C,G,KAIJnF,mBAAA,CAiBM,OAjBNoF,WAiBM,GAhBJpF,mBAAA,CAA4D,MAA5DqF,WAA4D,EAAAvD,gBAAA,CAA9B0C,YAAY,CAACQ,SAAS,kBACpDhF,mBAAA,CAAkE,KAAlEsF,WAAkE,EAAAxD,gBAAA,CAA/B0C,YAAY,CAACe,WAAW,kBAE3DvF,mBAAA,CAYM,OAZNwF,WAYM,GAXJxF,mBAAA,CAIM,OAJNyF,WAIM,G,4BAHJzF,mBAAA,CAAmD;MAAhDN,KAAK,EAAC,kBAAkB;MAAC,aAAW,EAAC;6DACxCM,mBAAA,CAAsC;MAAhCN,KAAK,EAAC;IAAc,GAAC,MAAI,sBAC/BM,mBAAA,CAAyF,QAAzF0F,WAAyF,EAAnD,GAAC,GAAA5D,gBAAA,CAAGlB,QAAA,CAAA+E,cAAc,CAACnB,YAAY,CAACoB,QAAQ,kB,GAGhF5F,mBAAA,CAIM,OAJN6F,WAIM,G,4BAHJ7F,mBAAA,CAA+C;MAA5CN,KAAK,EAAC,cAAc;MAAC,aAAW,EAAC;6DACpCM,mBAAA,CAA6C;MAAvCN,KAAK,EAAC;IAAc,GAAC,aAAW,sBACtCM,mBAAA,CAAiF,QAAjF8F,WAAiF,EAAAhE,gBAAA,CAAnDlB,QAAA,CAAAmF,iBAAiB,CAACvB,YAAY,CAACQ,SAAS,kB,OAK5EhF,mBAAA,CAEM,OAFNgG,WAEM,GADKxB,YAAY,CAACE,SAAS,I,cAA/B7E,mBAAA,CAAqF,KAArFoG,WAAqF,K;gHAO/FlG,mBAAA,kCAAqC,EACrCC,mBAAA,CA6FU,WA7FVkG,WA6FU,GA5FRlG,mBAAA,CA2FM,OA3FNmG,WA2FM,GA1FJnG,mBAAA,CAyFM,OAzFNoG,WAyFM,GAxFJrG,mBAAA,uBAA0B,E,0oCA4B1BA,mBAAA,kBAAqB,E,g3CAwCrBA,mBAAA,eAAkB,EAClBC,mBAAA,CAkBM,OAlBNqG,WAkBM,G,4BAjBJrG,mBAAA,CAGM;IAHDN,KAAK,EAAC;EAAa,IACtBM,mBAAA,CAAiD;IAA9CN,KAAK,EAAC,gBAAgB;IAAC,aAAW,EAAC;MACtCM,mBAAA,CAAyB,YAArB,kBAAgB,E,sBAEtBA,mBAAA,CAYM,OAZNsG,WAYM,G,4BAXJtG,mBAAA,CAA+I;IAA5IN,KAAK,EAAC;EAAkB,GAAC,iHAA+G,sBAC3IM,mBAAA,CASM,OATNuG,WASM,GARJvG,mBAAA,CAGS;IAHDN,KAAK,EAAC,0BAA0B;IAAEuC,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEvB,QAAA,CAAA4F,QAAA,IAAA5F,QAAA,CAAA4F,QAAA,IAAArE,IAAA,CAAQ;kCACvDnC,mBAAA,CAAyD;IAAtDN,KAAK,EAAC,wBAAwB;IAAC,aAAW,EAAC;+CAAW,YAE3D,E,IACAM,mBAAA,CAGS;IAHDN,KAAK,EAAC,6BAA6B;IAAEuC,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEvB,QAAA,CAAA8C,cAAA,IAAA9C,QAAA,CAAA8C,cAAA,IAAAvB,IAAA,CAAc;kCAChEnC,mBAAA,CAA+C;IAA5CN,KAAK,EAAC,cAAc;IAAC,aAAW,EAAC;+CAAW,mBAEjD,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}