{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, normalizeClass as _normalizeClass, vModelText as _vModelText, withKeys as _withKeys, withDirectives as _withDirectives, openBlock as _openBlock, createElementBlock as _createElementBlock, Transition as _Transition, withCtx as _withCtx, createVNode as _createVNode, toDisplayString as _toDisplayString } from \"vue\";\nimport _imports_0 from '@/assets/icon-of-bula.jpg';\nconst _hoisted_1 = {\n  class: \"client-header\",\n  role: \"banner\"\n};\nconst _hoisted_2 = {\n  class: \"main-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-container\"\n};\nconst _hoisted_4 = {\n  class: \"header-brand\"\n};\nconst _hoisted_5 = {\n  class: \"primary-nav\",\n  role: \"navigation\",\n  \"aria-label\": \"Primary navigation\"\n};\nconst _hoisted_6 = {\n  class: \"nav-list\",\n  role: \"menubar\"\n};\nconst _hoisted_7 = {\n  class: \"nav-item\",\n  role: \"none\"\n};\nconst _hoisted_8 = [\"aria-current\"];\nconst _hoisted_9 = {\n  class: \"nav-item\",\n  role: \"none\"\n};\nconst _hoisted_10 = [\"aria-current\"];\nconst _hoisted_11 = {\n  class: \"nav-item\",\n  role: \"none\"\n};\nconst _hoisted_12 = [\"aria-current\"];\nconst _hoisted_13 = {\n  class: \"nav-item\",\n  role: \"none\"\n};\nconst _hoisted_14 = [\"aria-current\"];\nconst _hoisted_15 = {\n  class: \"header-actions\"\n};\nconst _hoisted_16 = {\n  class: \"search-container\"\n};\nconst _hoisted_17 = [\"aria-label\", \"aria-expanded\"];\nconst _hoisted_18 = {\n  key: 0,\n  class: \"search-box\",\n  role: \"search\"\n};\nconst _hoisted_19 = {\n  class: \"user-profile\",\n  ref: \"userProfileRef\"\n};\nconst _hoisted_20 = [\"aria-label\", \"aria-expanded\"];\nconst _hoisted_21 = {\n  class: \"user-avatar\"\n};\nconst _hoisted_22 = [\"src\", \"alt\"];\nconst _hoisted_23 = {\n  key: 1,\n  class: \"fas fa-user-circle avatar-icon\",\n  \"aria-hidden\": \"true\"\n};\nconst _hoisted_24 = {\n  class: \"user-info\"\n};\nconst _hoisted_25 = {\n  class: \"user-name\"\n};\nconst _hoisted_26 = {\n  key: 0,\n  class: \"user-dropdown\",\n  role: \"menu\",\n  \"aria-label\": \"User account options\"\n};\nconst _hoisted_27 = {\n  class: \"dropdown-header\"\n};\nconst _hoisted_28 = {\n  class: \"user-details\"\n};\nconst _hoisted_29 = {\n  class: \"user-display-name\"\n};\nconst _hoisted_30 = {\n  class: \"user-email\"\n};\nconst _hoisted_31 = [\"aria-label\", \"aria-expanded\"];\nconst _hoisted_32 = {\n  key: 0,\n  class: \"mobile-nav\",\n  role: \"navigation\",\n  \"aria-label\": \"Mobile navigation\"\n};\nconst _hoisted_33 = {\n  class: \"mobile-nav-content\"\n};\nconst _hoisted_34 = {\n  class: \"mobile-nav-menu\"\n};\nconst _hoisted_35 = {\n  class: \"mobile-nav-list\",\n  role: \"menubar\"\n};\nconst _hoisted_36 = {\n  class: \"mobile-nav-item\",\n  role: \"none\"\n};\nconst _hoisted_37 = {\n  class: \"mobile-nav-item\",\n  role: \"none\"\n};\nconst _hoisted_38 = {\n  class: \"mobile-nav-item\",\n  role: \"none\"\n};\nconst _hoisted_39 = {\n  class: \"mobile-nav-item\",\n  role: \"none\"\n};\nconst _hoisted_40 = {\n  key: 0,\n  class: \"breadcrumb-section\",\n  role: \"navigation\",\n  \"aria-label\": \"Breadcrumb\"\n};\nconst _hoisted_41 = {\n  class: \"header-container\"\n};\nconst _hoisted_42 = {\n  class: \"breadcrumb-nav\"\n};\nconst _hoisted_43 = {\n  class: \"breadcrumb-list\"\n};\nconst _hoisted_44 = {\n  class: \"breadcrumb-item\"\n};\nconst _hoisted_45 = {\n  class: \"breadcrumb-item active\",\n  \"aria-current\": \"page\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"header\", _hoisted_1, [_createCommentVNode(\" Skip to main content link \"), _cache[40] || (_cache[40] = _createElementVNode(\"a\", {\n    href: \"#main-content\",\n    class: \"skip-link\"\n  }, \"Skip to main content\", -1 /* HOISTED */)), _createCommentVNode(\" Government Banner \"), _cache[41] || (_cache[41] = _createElementVNode(\"div\", {\n    class: \"gov-banner\",\n    role: \"region\",\n    \"aria-label\": \"Government website banner\"\n  }, [_createElementVNode(\"div\", {\n    class: \"gov-banner-content\"\n  }, [_createElementVNode(\"div\", {\n    class: \"gov-banner-flag\"\n  }, [_createElementVNode(\"img\", {\n    src: \"/assets/images/ph_flag_small.png\",\n    alt: \"Philippine flag\",\n    class: \"flag-icon\",\n    width: \"24\",\n    height: \"16\"\n  })]), _createElementVNode(\"div\", {\n    class: \"gov-banner-text\"\n  }, [_createElementVNode(\"span\", {\n    class: \"gov-banner-label\"\n  }, \"An official website of the\"), _createElementVNode(\"strong\", {\n    class: \"gov-banner-agency\"\n  }, \"Barangay Bula, General Santos City\")]), _createElementVNode(\"div\", {\n    class: \"gov-banner-secure\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-shield-alt\",\n    \"aria-hidden\": \"true\"\n  }), _createElementVNode(\"span\", null, \"Secure\")])])], -1 /* HOISTED */)), _createCommentVNode(\" Main Header \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createCommentVNode(\" Logo and Site Identity \"), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"button\", {\n    class: \"logo-button\",\n    onClick: _cache[0] || (_cache[0] = $event => $setup.handleMenuAction('dashboard')),\n    \"aria-label\": \"Go to dashboard\",\n    type: \"button\"\n  }, _cache[16] || (_cache[16] = [_createElementVNode(\"img\", {\n    src: _imports_0,\n    alt: \"Barangay Bula Logo\",\n    class: \"logo\",\n    width: \"48\",\n    height: \"48\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"div\", {\n    class: \"site-identity\"\n  }, [_createElementVNode(\"h1\", {\n    class: \"site-title\"\n  }, \"Barangay Bula\"), _createElementVNode(\"span\", {\n    class: \"site-subtitle\"\n  }, \"Digital Services Portal\")], -1 /* HOISTED */)]))]), _createCommentVNode(\" Primary Navigation \"), _createElementVNode(\"nav\", _hoisted_5, [_createElementVNode(\"ul\", _hoisted_6, [_createElementVNode(\"li\", _hoisted_7, [_createElementVNode(\"button\", {\n    class: _normalizeClass([\"nav-link\", {\n      active: $props.activeMenu === 'dashboard'\n    }]),\n    onClick: _cache[1] || (_cache[1] = $event => $setup.handleMenuAction('dashboard')),\n    role: \"menuitem\",\n    \"aria-current\": $props.activeMenu === 'dashboard' ? 'page' : undefined\n  }, _cache[17] || (_cache[17] = [_createElementVNode(\"i\", {\n    class: \"fas fa-home\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Dashboard\", -1 /* HOISTED */)]), 10 /* CLASS, PROPS */, _hoisted_8)]), _createElementVNode(\"li\", _hoisted_9, [_createElementVNode(\"button\", {\n    class: _normalizeClass([\"nav-link\", {\n      active: $props.activeMenu === 'services'\n    }]),\n    onClick: _cache[2] || (_cache[2] = $event => $setup.handleMenuAction('services')),\n    role: \"menuitem\",\n    \"aria-current\": $props.activeMenu === 'services' ? 'page' : undefined\n  }, _cache[18] || (_cache[18] = [_createElementVNode(\"i\", {\n    class: \"fas fa-file-alt\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Services\", -1 /* HOISTED */)]), 10 /* CLASS, PROPS */, _hoisted_10)]), _createElementVNode(\"li\", _hoisted_11, [_createElementVNode(\"button\", {\n    class: _normalizeClass([\"nav-link\", {\n      active: $props.activeMenu === 'requests'\n    }]),\n    onClick: _cache[3] || (_cache[3] = $event => $setup.handleMenuAction('requests')),\n    role: \"menuitem\",\n    \"aria-current\": $props.activeMenu === 'requests' ? 'page' : undefined\n  }, _cache[19] || (_cache[19] = [_createElementVNode(\"i\", {\n    class: \"fas fa-clock\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"My Requests\", -1 /* HOISTED */)]), 10 /* CLASS, PROPS */, _hoisted_12)]), _createElementVNode(\"li\", _hoisted_13, [_createElementVNode(\"button\", {\n    class: _normalizeClass([\"nav-link\", {\n      active: $props.activeMenu === 'help'\n    }]),\n    onClick: _cache[4] || (_cache[4] = $event => $setup.handleMenuAction('help')),\n    role: \"menuitem\",\n    \"aria-current\": $props.activeMenu === 'help' ? 'page' : undefined\n  }, _cache[20] || (_cache[20] = [_createElementVNode(\"i\", {\n    class: \"fas fa-question-circle\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Help\", -1 /* HOISTED */)]), 10 /* CLASS, PROPS */, _hoisted_14)])])]), _createCommentVNode(\" Header Actions \"), _createElementVNode(\"div\", _hoisted_15, [_createCommentVNode(\" Search \"), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"button\", {\n    class: \"search-toggle\",\n    onClick: $setup.toggleSearch,\n    \"aria-label\": $setup.showSearch ? 'Close search' : 'Open search',\n    \"aria-expanded\": $setup.showSearch,\n    type: \"button\"\n  }, _cache[21] || (_cache[21] = [_createElementVNode(\"i\", {\n    class: \"fas fa-search\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", {\n    class: \"sr-only\"\n  }, \"Search\", -1 /* HOISTED */)]), 8 /* PROPS */, _hoisted_17), _createVNode(_Transition, {\n    name: \"search-slide\"\n  }, {\n    default: _withCtx(() => [$setup.showSearch ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, [_cache[23] || (_cache[23] = _createElementVNode(\"label\", {\n      for: \"header-search\",\n      class: \"sr-only\"\n    }, \"Search documents and services\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      id: \"header-search\",\n      ref: \"searchInput\",\n      type: \"search\",\n      placeholder: \"Search documents, services...\",\n      class: \"search-input\",\n      \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.searchQuery = $event),\n      onKeyup: [_withKeys($setup.performSearch, [\"enter\"]), _withKeys($setup.closeSearch, [\"escape\"])],\n      autocomplete: \"off\",\n      \"aria-describedby\": \"search-help\"\n    }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelText, $setup.searchQuery]]), _cache[24] || (_cache[24] = _createElementVNode(\"span\", {\n      id: \"search-help\",\n      class: \"sr-only\"\n    }, \" Press Enter to search, Escape to close \", -1 /* HOISTED */)), _createElementVNode(\"button\", {\n      class: \"search-submit\",\n      onClick: $setup.performSearch,\n      \"aria-label\": \"Submit search\",\n      type: \"button\"\n    }, _cache[22] || (_cache[22] = [_createElementVNode(\"i\", {\n      class: \"fas fa-search\",\n      \"aria-hidden\": \"true\"\n    }, null, -1 /* HOISTED */)]))])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  })]), _createCommentVNode(\" Notifications \"), _createVNode($setup[\"ClientNotifications\"], {\n    onNewNotification: $setup.handleNewNotification,\n    onNotificationClick: $setup.handleNotificationClick,\n    onError: $setup.handleNotificationError\n  }), _createCommentVNode(\" User Profile Menu \"), _createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"button\", {\n    class: \"user-button\",\n    onClick: $setup.toggleUserDropdown,\n    \"aria-label\": `User menu for ${$props.userName}`,\n    \"aria-expanded\": $props.showUserDropdown,\n    type: \"button\"\n  }, [_createElementVNode(\"div\", _hoisted_21, [$props.userAvatar ? (_openBlock(), _createElementBlock(\"img\", {\n    key: 0,\n    src: $props.userAvatar,\n    alt: `${$props.userName} avatar`,\n    class: \"avatar-image\",\n    width: \"32\",\n    height: \"32\"\n  }, null, 8 /* PROPS */, _hoisted_22)) : (_openBlock(), _createElementBlock(\"i\", _hoisted_23))]), _createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"span\", _hoisted_25, _toDisplayString($props.userName), 1 /* TEXT */), _cache[25] || (_cache[25] = _createElementVNode(\"span\", {\n    class: \"user-role\"\n  }, \"Client Portal\", -1 /* HOISTED */))]), _createElementVNode(\"i\", {\n    class: _normalizeClass([\"fas fa-chevron-down dropdown-arrow\", {\n      rotated: $props.showUserDropdown\n    }]),\n    \"aria-hidden\": \"true\"\n  }, null, 2 /* CLASS */)], 8 /* PROPS */, _hoisted_20), _createVNode(_Transition, {\n    name: \"dropdown-fade\"\n  }, {\n    default: _withCtx(() => [$props.showUserDropdown ? (_openBlock(), _createElementBlock(\"div\", _hoisted_26, [_createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"strong\", _hoisted_29, _toDisplayString($props.userName), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_30, _toDisplayString($props.userEmail), 1 /* TEXT */)])]), _cache[32] || (_cache[32] = _createElementVNode(\"div\", {\n      class: \"dropdown-divider\",\n      role: \"separator\"\n    }, null, -1 /* HOISTED */)), _createElementVNode(\"button\", {\n      class: \"dropdown-item\",\n      onClick: _cache[6] || (_cache[6] = $event => $setup.handleMenuAction('profile')),\n      role: \"menuitem\",\n      type: \"button\"\n    }, _cache[26] || (_cache[26] = [_createElementVNode(\"i\", {\n      class: \"fas fa-user\",\n      \"aria-hidden\": \"true\"\n    }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"My Profile\", -1 /* HOISTED */)])), _createElementVNode(\"button\", {\n      class: \"dropdown-item\",\n      onClick: _cache[7] || (_cache[7] = $event => $setup.handleMenuAction('settings')),\n      role: \"menuitem\",\n      type: \"button\"\n    }, _cache[27] || (_cache[27] = [_createElementVNode(\"i\", {\n      class: \"fas fa-cog\",\n      \"aria-hidden\": \"true\"\n    }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Account Settings\", -1 /* HOISTED */)])), _createElementVNode(\"button\", {\n      class: \"dropdown-item\",\n      onClick: _cache[8] || (_cache[8] = $event => $setup.handleMenuAction('documents')),\n      role: \"menuitem\",\n      type: \"button\"\n    }, _cache[28] || (_cache[28] = [_createElementVNode(\"i\", {\n      class: \"fas fa-folder\",\n      \"aria-hidden\": \"true\"\n    }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"My Documents\", -1 /* HOISTED */)])), _createElementVNode(\"button\", {\n      class: \"dropdown-item\",\n      onClick: _cache[9] || (_cache[9] = $event => $setup.handleMenuAction('history')),\n      role: \"menuitem\",\n      type: \"button\"\n    }, _cache[29] || (_cache[29] = [_createElementVNode(\"i\", {\n      class: \"fas fa-history\",\n      \"aria-hidden\": \"true\"\n    }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Request History\", -1 /* HOISTED */)])), _cache[33] || (_cache[33] = _createElementVNode(\"div\", {\n      class: \"dropdown-divider\",\n      role: \"separator\"\n    }, null, -1 /* HOISTED */)), _createElementVNode(\"button\", {\n      class: \"dropdown-item\",\n      onClick: _cache[10] || (_cache[10] = $event => $setup.handleMenuAction('help')),\n      role: \"menuitem\",\n      type: \"button\"\n    }, _cache[30] || (_cache[30] = [_createElementVNode(\"i\", {\n      class: \"fas fa-question-circle\",\n      \"aria-hidden\": \"true\"\n    }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Help & Support\", -1 /* HOISTED */)])), _cache[34] || (_cache[34] = _createElementVNode(\"div\", {\n      class: \"dropdown-divider\",\n      role: \"separator\"\n    }, null, -1 /* HOISTED */)), _createElementVNode(\"button\", {\n      class: \"dropdown-item logout-item\",\n      onClick: $setup.handleLogout,\n      role: \"menuitem\",\n      type: \"button\"\n    }, _cache[31] || (_cache[31] = [_createElementVNode(\"i\", {\n      class: \"fas fa-sign-out-alt\",\n      \"aria-hidden\": \"true\"\n    }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Sign Out\", -1 /* HOISTED */)]))])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  })], 512 /* NEED_PATCH */), _createCommentVNode(\" Mobile Menu Toggle \"), _createElementVNode(\"button\", {\n    class: \"mobile-menu-toggle\",\n    onClick: $setup.toggleMobileMenu,\n    \"aria-label\": $setup.showMobileMenu ? 'Close navigation menu' : 'Open navigation menu',\n    \"aria-expanded\": $setup.showMobileMenu,\n    type: \"button\"\n  }, [_createElementVNode(\"span\", {\n    class: _normalizeClass([\"hamburger-line\", {\n      active: $setup.showMobileMenu\n    }])\n  }, null, 2 /* CLASS */), _createElementVNode(\"span\", {\n    class: _normalizeClass([\"hamburger-line\", {\n      active: $setup.showMobileMenu\n    }])\n  }, null, 2 /* CLASS */), _createElementVNode(\"span\", {\n    class: _normalizeClass([\"hamburger-line\", {\n      active: $setup.showMobileMenu\n    }])\n  }, null, 2 /* CLASS */)], 8 /* PROPS */, _hoisted_31)])])]), _createCommentVNode(\" Mobile Navigation Menu \"), _createVNode(_Transition, {\n    name: \"mobile-menu-slide\"\n  }, {\n    default: _withCtx(() => [$setup.showMobileMenu ? (_openBlock(), _createElementBlock(\"div\", _hoisted_32, [_createElementVNode(\"div\", _hoisted_33, [_createElementVNode(\"nav\", _hoisted_34, [_createElementVNode(\"ul\", _hoisted_35, [_createElementVNode(\"li\", _hoisted_36, [_createElementVNode(\"button\", {\n      class: _normalizeClass([\"mobile-nav-link\", {\n        active: $props.activeMenu === 'dashboard'\n      }]),\n      onClick: _cache[11] || (_cache[11] = $event => $setup.handleMobileMenuAction('dashboard')),\n      role: \"menuitem\",\n      type: \"button\"\n    }, _cache[35] || (_cache[35] = [_createElementVNode(\"i\", {\n      class: \"fas fa-home\",\n      \"aria-hidden\": \"true\"\n    }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Dashboard\", -1 /* HOISTED */)]), 2 /* CLASS */)]), _createElementVNode(\"li\", _hoisted_37, [_createElementVNode(\"button\", {\n      class: _normalizeClass([\"mobile-nav-link\", {\n        active: $props.activeMenu === 'services'\n      }]),\n      onClick: _cache[12] || (_cache[12] = $event => $setup.handleMobileMenuAction('services')),\n      role: \"menuitem\",\n      type: \"button\"\n    }, _cache[36] || (_cache[36] = [_createElementVNode(\"i\", {\n      class: \"fas fa-file-alt\",\n      \"aria-hidden\": \"true\"\n    }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Services\", -1 /* HOISTED */)]), 2 /* CLASS */)]), _createElementVNode(\"li\", _hoisted_38, [_createElementVNode(\"button\", {\n      class: _normalizeClass([\"mobile-nav-link\", {\n        active: $props.activeMenu === 'requests'\n      }]),\n      onClick: _cache[13] || (_cache[13] = $event => $setup.handleMobileMenuAction('requests')),\n      role: \"menuitem\",\n      type: \"button\"\n    }, _cache[37] || (_cache[37] = [_createElementVNode(\"i\", {\n      class: \"fas fa-clock\",\n      \"aria-hidden\": \"true\"\n    }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"My Requests\", -1 /* HOISTED */)]), 2 /* CLASS */)]), _createElementVNode(\"li\", _hoisted_39, [_createElementVNode(\"button\", {\n      class: _normalizeClass([\"mobile-nav-link\", {\n        active: $props.activeMenu === 'help'\n      }]),\n      onClick: _cache[14] || (_cache[14] = $event => $setup.handleMobileMenuAction('help')),\n      role: \"menuitem\",\n      type: \"button\"\n    }, _cache[38] || (_cache[38] = [_createElementVNode(\"i\", {\n      class: \"fas fa-question-circle\",\n      \"aria-hidden\": \"true\"\n    }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Help\", -1 /* HOISTED */)]), 2 /* CLASS */)])])])])])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" Breadcrumb Navigation \"), $props.showBreadcrumbs ? (_openBlock(), _createElementBlock(\"div\", _hoisted_40, [_createElementVNode(\"div\", _hoisted_41, [_createElementVNode(\"nav\", _hoisted_42, [_createElementVNode(\"ol\", _hoisted_43, [_createElementVNode(\"li\", _hoisted_44, [_createElementVNode(\"button\", {\n    class: \"breadcrumb-link\",\n    onClick: _cache[15] || (_cache[15] = $event => $setup.handleMenuAction('dashboard')),\n    type: \"button\"\n  }, _cache[39] || (_cache[39] = [_createElementVNode(\"i\", {\n    class: \"fas fa-home\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Dashboard\", -1 /* HOISTED */)]))]), _createElementVNode(\"li\", _hoisted_45, [_createElementVNode(\"span\", null, _toDisplayString($setup.getPageTitle()), 1 /* TEXT */)])])])])])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["_imports_0", "class", "role", "ref", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "href", "src", "alt", "width", "height", "_hoisted_2", "_hoisted_3", "_hoisted_4", "onClick", "_cache", "$event", "$setup", "handleMenuAction", "type", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_normalizeClass", "active", "$props", "activeMenu", "undefined", "_hoisted_9", "_hoisted_11", "_hoisted_13", "_hoisted_15", "_hoisted_16", "toggleSearch", "showSearch", "_createVNode", "_Transition", "name", "_hoisted_18", "for", "id", "placeholder", "searchQuery", "onKeyup", "performSearch", "closeSearch", "autocomplete", "onNewNotification", "handleNewNotification", "onNotificationClick", "handleNotificationClick", "onError", "handleNotificationError", "_hoisted_19", "toggleUserDropdown", "userName", "showUserDropdown", "_hoisted_21", "userAvatar", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_toDisplayString", "rotated", "_hoisted_26", "_hoisted_27", "_hoisted_28", "_hoisted_29", "_hoisted_30", "userEmail", "handleLogout", "toggleMobileMenu", "showMobileMenu", "_hoisted_32", "_hoisted_33", "_hoisted_34", "_hoisted_35", "_hoisted_36", "handleMobileMenuAction", "_hoisted_37", "_hoisted_38", "_hoisted_39", "showBreadcrumbs", "_hoisted_40", "_hoisted_41", "_hoisted_42", "_hoisted_43", "_hoisted_44", "_hoisted_45", "getPageTitle"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\client\\ClientHeader.vue"], "sourcesContent": ["<template>\n  <header class=\"client-header\" role=\"banner\">\n    <!-- Skip to main content link -->\n    <a href=\"#main-content\" class=\"skip-link\">Skip to main content</a>\n\n    <!-- Government Banner -->\n    <div class=\"gov-banner\" role=\"region\" aria-label=\"Government website banner\">\n      <div class=\"gov-banner-content\">\n        <div class=\"gov-banner-flag\">\n          <img\n            src=\"/assets/images/ph_flag_small.png\"\n            alt=\"Philippine flag\"\n            class=\"flag-icon\"\n            width=\"24\"\n            height=\"16\"\n          />\n        </div>\n        <div class=\"gov-banner-text\">\n          <span class=\"gov-banner-label\">An official website of the</span>\n          <strong class=\"gov-banner-agency\">Barangay Bula, General Santos City</strong>\n        </div>\n        <div class=\"gov-banner-secure\">\n          <i class=\"fas fa-shield-alt\" aria-hidden=\"true\"></i>\n          <span>Secure</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Main Header -->\n    <div class=\"main-header\">\n      <div class=\"header-container\">\n        <!-- Logo and Site Identity -->\n        <div class=\"header-brand\">\n          <button\n            class=\"logo-button\"\n            @click=\"handleMenuAction('dashboard')\"\n            aria-label=\"Go to dashboard\"\n            type=\"button\"\n          >\n            <img\n              src=\"@/assets/icon-of-bula.jpg\"\n              alt=\"Barangay Bula Logo\"\n              class=\"logo\"\n              width=\"48\"\n              height=\"48\"\n            />\n            <div class=\"site-identity\">\n              <h1 class=\"site-title\">Barangay Bula</h1>\n              <span class=\"site-subtitle\">Digital Services Portal</span>\n            </div>\n          </button>\n        </div>\n\n        <!-- Primary Navigation -->\n        <nav class=\"primary-nav\" role=\"navigation\" aria-label=\"Primary navigation\">\n          <ul class=\"nav-list\" role=\"menubar\">\n            <li class=\"nav-item\" role=\"none\">\n              <button\n                class=\"nav-link\"\n                :class=\"{ active: activeMenu === 'dashboard' }\"\n                @click=\"handleMenuAction('dashboard')\"\n                role=\"menuitem\"\n                :aria-current=\"activeMenu === 'dashboard' ? 'page' : undefined\"\n              >\n                <i class=\"fas fa-home\" aria-hidden=\"true\"></i>\n                <span>Dashboard</span>\n              </button>\n            </li>\n            <li class=\"nav-item\" role=\"none\">\n              <button\n                class=\"nav-link\"\n                :class=\"{ active: activeMenu === 'services' }\"\n                @click=\"handleMenuAction('services')\"\n                role=\"menuitem\"\n                :aria-current=\"activeMenu === 'services' ? 'page' : undefined\"\n              >\n                <i class=\"fas fa-file-alt\" aria-hidden=\"true\"></i>\n                <span>Services</span>\n              </button>\n            </li>\n            <li class=\"nav-item\" role=\"none\">\n              <button\n                class=\"nav-link\"\n                :class=\"{ active: activeMenu === 'requests' }\"\n                @click=\"handleMenuAction('requests')\"\n                role=\"menuitem\"\n                :aria-current=\"activeMenu === 'requests' ? 'page' : undefined\"\n              >\n                <i class=\"fas fa-clock\" aria-hidden=\"true\"></i>\n                <span>My Requests</span>\n              </button>\n            </li>\n            <li class=\"nav-item\" role=\"none\">\n              <button\n                class=\"nav-link\"\n                :class=\"{ active: activeMenu === 'help' }\"\n                @click=\"handleMenuAction('help')\"\n                role=\"menuitem\"\n                :aria-current=\"activeMenu === 'help' ? 'page' : undefined\"\n              >\n                <i class=\"fas fa-question-circle\" aria-hidden=\"true\"></i>\n                <span>Help</span>\n              </button>\n            </li>\n          </ul>\n        </nav>\n\n        <!-- Header Actions -->\n        <div class=\"header-actions\">\n          <!-- Search -->\n          <div class=\"search-container\">\n            <button\n              class=\"search-toggle\"\n              @click=\"toggleSearch\"\n              :aria-label=\"showSearch ? 'Close search' : 'Open search'\"\n              :aria-expanded=\"showSearch\"\n              type=\"button\"\n            >\n              <i class=\"fas fa-search\" aria-hidden=\"true\"></i>\n              <span class=\"sr-only\">Search</span>\n            </button>\n\n            <Transition name=\"search-slide\">\n              <div v-if=\"showSearch\" class=\"search-box\" role=\"search\">\n                <label for=\"header-search\" class=\"sr-only\">Search documents and services</label>\n                <input\n                  id=\"header-search\"\n                  ref=\"searchInput\"\n                  type=\"search\"\n                  placeholder=\"Search documents, services...\"\n                  class=\"search-input\"\n                  v-model=\"searchQuery\"\n                  @keyup.enter=\"performSearch\"\n                  @keyup.escape=\"closeSearch\"\n                  autocomplete=\"off\"\n                  aria-describedby=\"search-help\"\n                />\n                <span id=\"search-help\" class=\"sr-only\">\n                  Press Enter to search, Escape to close\n                </span>\n                <button\n                  class=\"search-submit\"\n                  @click=\"performSearch\"\n                  aria-label=\"Submit search\"\n                  type=\"button\"\n                >\n                  <i class=\"fas fa-search\" aria-hidden=\"true\"></i>\n                </button>\n              </div>\n            </Transition>\n          </div>\n\n          <!-- Notifications -->\n          <ClientNotifications\n            @new-notification=\"handleNewNotification\"\n            @notification-click=\"handleNotificationClick\"\n            @error=\"handleNotificationError\"\n          />\n\n          <!-- User Profile Menu -->\n          <div class=\"user-profile\" ref=\"userProfileRef\">\n            <button\n              class=\"user-button\"\n              @click=\"toggleUserDropdown\"\n              :aria-label=\"`User menu for ${userName}`\"\n              :aria-expanded=\"showUserDropdown\"\n              type=\"button\"\n            >\n              <div class=\"user-avatar\">\n                <img\n                  v-if=\"userAvatar\"\n                  :src=\"userAvatar\"\n                  :alt=\"`${userName} avatar`\"\n                  class=\"avatar-image\"\n                  width=\"32\"\n                  height=\"32\"\n                />\n                <i v-else class=\"fas fa-user-circle avatar-icon\" aria-hidden=\"true\"></i>\n              </div>\n              <div class=\"user-info\">\n                <span class=\"user-name\">{{ userName }}</span>\n                <span class=\"user-role\">Client Portal</span>\n              </div>\n              <i\n                class=\"fas fa-chevron-down dropdown-arrow\"\n                :class=\"{ rotated: showUserDropdown }\"\n                aria-hidden=\"true\"\n              ></i>\n            </button>\n\n            <Transition name=\"dropdown-fade\">\n              <div\n                v-if=\"showUserDropdown\"\n                class=\"user-dropdown\"\n                role=\"menu\"\n                aria-label=\"User account options\"\n              >\n                <div class=\"dropdown-header\">\n                  <div class=\"user-details\">\n                    <strong class=\"user-display-name\">{{ userName }}</strong>\n                    <span class=\"user-email\">{{ userEmail }}</span>\n                  </div>\n                </div>\n\n                <div class=\"dropdown-divider\" role=\"separator\"></div>\n\n                <button\n                  class=\"dropdown-item\"\n                  @click=\"handleMenuAction('profile')\"\n                  role=\"menuitem\"\n                  type=\"button\"\n                >\n                  <i class=\"fas fa-user\" aria-hidden=\"true\"></i>\n                  <span>My Profile</span>\n                </button>\n\n                <button\n                  class=\"dropdown-item\"\n                  @click=\"handleMenuAction('settings')\"\n                  role=\"menuitem\"\n                  type=\"button\"\n                >\n                  <i class=\"fas fa-cog\" aria-hidden=\"true\"></i>\n                  <span>Account Settings</span>\n                </button>\n\n                <button\n                  class=\"dropdown-item\"\n                  @click=\"handleMenuAction('documents')\"\n                  role=\"menuitem\"\n                  type=\"button\"\n                >\n                  <i class=\"fas fa-folder\" aria-hidden=\"true\"></i>\n                  <span>My Documents</span>\n                </button>\n\n                <button\n                  class=\"dropdown-item\"\n                  @click=\"handleMenuAction('history')\"\n                  role=\"menuitem\"\n                  type=\"button\"\n                >\n                  <i class=\"fas fa-history\" aria-hidden=\"true\"></i>\n                  <span>Request History</span>\n                </button>\n\n                <div class=\"dropdown-divider\" role=\"separator\"></div>\n\n                <button\n                  class=\"dropdown-item\"\n                  @click=\"handleMenuAction('help')\"\n                  role=\"menuitem\"\n                  type=\"button\"\n                >\n                  <i class=\"fas fa-question-circle\" aria-hidden=\"true\"></i>\n                  <span>Help & Support</span>\n                </button>\n\n                <div class=\"dropdown-divider\" role=\"separator\"></div>\n\n                <button\n                  class=\"dropdown-item logout-item\"\n                  @click=\"handleLogout\"\n                  role=\"menuitem\"\n                  type=\"button\"\n                >\n                  <i class=\"fas fa-sign-out-alt\" aria-hidden=\"true\"></i>\n                  <span>Sign Out</span>\n                </button>\n              </div>\n            </Transition>\n          </div>\n\n          <!-- Mobile Menu Toggle -->\n          <button\n            class=\"mobile-menu-toggle\"\n            @click=\"toggleMobileMenu\"\n            :aria-label=\"showMobileMenu ? 'Close navigation menu' : 'Open navigation menu'\"\n            :aria-expanded=\"showMobileMenu\"\n            type=\"button\"\n          >\n            <span class=\"hamburger-line\" :class=\"{ active: showMobileMenu }\"></span>\n            <span class=\"hamburger-line\" :class=\"{ active: showMobileMenu }\"></span>\n            <span class=\"hamburger-line\" :class=\"{ active: showMobileMenu }\"></span>\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Mobile Navigation Menu -->\n    <Transition name=\"mobile-menu-slide\">\n      <div v-if=\"showMobileMenu\" class=\"mobile-nav\" role=\"navigation\" aria-label=\"Mobile navigation\">\n        <div class=\"mobile-nav-content\">\n          <nav class=\"mobile-nav-menu\">\n            <ul class=\"mobile-nav-list\" role=\"menubar\">\n              <li class=\"mobile-nav-item\" role=\"none\">\n                <button\n                  class=\"mobile-nav-link\"\n                  :class=\"{ active: activeMenu === 'dashboard' }\"\n                  @click=\"handleMobileMenuAction('dashboard')\"\n                  role=\"menuitem\"\n                  type=\"button\"\n                >\n                  <i class=\"fas fa-home\" aria-hidden=\"true\"></i>\n                  <span>Dashboard</span>\n                </button>\n              </li>\n              <li class=\"mobile-nav-item\" role=\"none\">\n                <button\n                  class=\"mobile-nav-link\"\n                  :class=\"{ active: activeMenu === 'services' }\"\n                  @click=\"handleMobileMenuAction('services')\"\n                  role=\"menuitem\"\n                  type=\"button\"\n                >\n                  <i class=\"fas fa-file-alt\" aria-hidden=\"true\"></i>\n                  <span>Services</span>\n                </button>\n              </li>\n              <li class=\"mobile-nav-item\" role=\"none\">\n                <button\n                  class=\"mobile-nav-link\"\n                  :class=\"{ active: activeMenu === 'requests' }\"\n                  @click=\"handleMobileMenuAction('requests')\"\n                  role=\"menuitem\"\n                  type=\"button\"\n                >\n                  <i class=\"fas fa-clock\" aria-hidden=\"true\"></i>\n                  <span>My Requests</span>\n                </button>\n              </li>\n              <li class=\"mobile-nav-item\" role=\"none\">\n                <button\n                  class=\"mobile-nav-link\"\n                  :class=\"{ active: activeMenu === 'help' }\"\n                  @click=\"handleMobileMenuAction('help')\"\n                  role=\"menuitem\"\n                  type=\"button\"\n                >\n                  <i class=\"fas fa-question-circle\" aria-hidden=\"true\"></i>\n                  <span>Help</span>\n                </button>\n              </li>\n            </ul>\n          </nav>\n        </div>\n      </div>\n    </Transition>\n\n    <!-- Breadcrumb Navigation -->\n    <div v-if=\"showBreadcrumbs\" class=\"breadcrumb-section\" role=\"navigation\" aria-label=\"Breadcrumb\">\n      <div class=\"header-container\">\n        <nav class=\"breadcrumb-nav\">\n          <ol class=\"breadcrumb-list\">\n            <li class=\"breadcrumb-item\">\n              <button\n                class=\"breadcrumb-link\"\n                @click=\"handleMenuAction('dashboard')\"\n                type=\"button\"\n              >\n                <i class=\"fas fa-home\" aria-hidden=\"true\"></i>\n                <span>Dashboard</span>\n              </button>\n            </li>\n            <li class=\"breadcrumb-item active\" aria-current=\"page\">\n              <span>{{ getPageTitle() }}</span>\n            </li>\n          </ol>\n        </nav>\n      </div>\n    </div>\n  </header>\n</template>\n\n<script setup>\nimport { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport ClientNotifications from './ClientNotifications.vue'\n\n// Props\nconst props = defineProps({\n  userName: {\n    type: String,\n    default: 'User'\n  },\n  userEmail: {\n    type: String,\n    default: '<EMAIL>'\n  },\n  userAvatar: {\n    type: String,\n    default: null\n  },\n  showUserDropdown: {\n    type: Boolean,\n    default: false\n  },\n  activeMenu: {\n    type: String,\n    default: 'dashboard'\n  },\n  showBreadcrumbs: {\n    type: Boolean,\n    default: true\n  }\n})\n\n// Emits\nconst emit = defineEmits([\n  'user-dropdown-toggle',\n  'menu-action',\n  'logout',\n  'error',\n  'search',\n  'notification-click'\n])\n\n// Router\nconst router = useRouter()\n\n// Reactive state\nconst showSearch = ref(false)\nconst showMobileMenu = ref(false)\nconst searchQuery = ref('')\nconst searchInput = ref(null)\nconst userProfileRef = ref(null)\n\n// Computed\nconst pageTitle = computed(() => {\n  const titles = {\n    'dashboard': 'Dashboard',\n    'services': 'Document Services',\n    'requests': 'My Requests',\n    'documents': 'My Documents',\n    'profile': 'My Profile',\n    'settings': 'Account Settings',\n    'history': 'Request History',\n    'notifications': 'Notifications',\n    'help': 'Help & Support'\n  }\n  return titles[props.activeMenu] || 'Dashboard'\n})\n\n// Methods\nconst getPageTitle = () => pageTitle.value\n\nconst toggleSearch = async () => {\n  showSearch.value = !showSearch.value\n  if (showSearch.value) {\n    await nextTick()\n    searchInput.value?.focus()\n  }\n}\n\nconst closeSearch = () => {\n  showSearch.value = false\n  searchQuery.value = ''\n}\n\nconst performSearch = () => {\n  if (searchQuery.value.trim()) {\n    emit('search', searchQuery.value.trim())\n    // Close search on mobile after search\n    if (window.innerWidth <= 768) {\n      closeSearch()\n    }\n  }\n}\n\nconst toggleUserDropdown = () => {\n  emit('user-dropdown-toggle')\n}\n\nconst toggleMobileMenu = () => {\n  showMobileMenu.value = !showMobileMenu.value\n  // Prevent body scroll when mobile menu is open\n  document.body.style.overflow = showMobileMenu.value ? 'hidden' : ''\n}\n\nconst handleMenuAction = (action) => {\n  emit('menu-action', action)\n  // Close mobile menu if open\n  if (showMobileMenu.value) {\n    toggleMobileMenu()\n  }\n}\n\nconst handleMobileMenuAction = (action) => {\n  handleMenuAction(action)\n}\n\nconst handleLogout = () => {\n  emit('logout')\n}\n\nconst handleOutsideClick = (event) => {\n  // Check if click is outside user dropdown\n  if (userProfileRef.value && !userProfileRef.value.contains(event.target)) {\n    if (props.showUserDropdown) {\n      toggleUserDropdown()\n    }\n  }\n\n  // Check if click is outside search\n  if (!event.target.closest('.search-container')) {\n    showSearch.value = false\n  }\n\n  // Check if click is outside mobile menu\n  if (!event.target.closest('.mobile-nav') && !event.target.closest('.mobile-menu-toggle')) {\n    if (showMobileMenu.value) {\n      toggleMobileMenu()\n    }\n  }\n}\n\nconst handleKeydown = (event) => {\n  // Close dropdowns on Escape key\n  if (event.key === 'Escape') {\n    if (showSearch.value) {\n      closeSearch()\n    }\n    if (props.showUserDropdown) {\n      toggleUserDropdown()\n    }\n    if (showMobileMenu.value) {\n      toggleMobileMenu()\n    }\n  }\n}\n\n// Notification event handlers\nconst handleNewNotification = (notification) => {\n  console.log('New notification received:', notification)\n  // Handle new notification - could show toast, update UI, etc.\n}\n\nconst handleNotificationClick = async (notification) => {\n  console.log('🔔 ClientHeader: Notification clicked:', notification)\n\n  // Ensure we have a valid notification object\n  if (!notification || typeof notification !== 'object') {\n    console.error('Invalid notification object received:', notification)\n    return\n  }\n\n  try {\n    // Parse notification data\n    const notificationData = typeof notification.data === 'string'\n      ? JSON.parse(notification.data)\n      : notification.data || {}\n\n    // Log navigation for debugging\n    console.log('📊 ClientHeader: Notification data:', notificationData)\n\n    // Additional header-specific logic can go here\n    // For example, updating header state, showing badges, etc.\n\n  } catch (error) {\n    console.error('❌ ClientHeader: Error handling notification click:', error)\n    emit('error', error)\n  }\n\n  // Always emit the event for other components that might need it\n  emit('notification-click', notification)\n}\n\nconst handleNotificationError = (error) => {\n  console.error('Notification error:', error)\n  emit('error', error)\n}\n\n// Lifecycle hooks\nonMounted(() => {\n  document.addEventListener('click', handleOutsideClick)\n  document.addEventListener('keydown', handleKeydown)\n})\n\nonUnmounted(() => {\n  document.removeEventListener('click', handleOutsideClick)\n  document.removeEventListener('keydown', handleKeydown)\n  // Reset body overflow\n  document.body.style.overflow = ''\n})\n</script>\n\n<style scoped src=\"./css/clientHeader.css\"></style>\n"], "mappings": ";OAwCcA,UAA+B;;EAvCnCC,KAAK,EAAC,eAAe;EAACC,IAAI,EAAC;;;EA4B5BD,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAkB;;EAEtBA,KAAK,EAAC;AAAc;;EAsBpBA,KAAK,EAAC,aAAa;EAACC,IAAI,EAAC,YAAY;EAAC,YAAU,EAAC;;;EAChDD,KAAK,EAAC,UAAU;EAACC,IAAI,EAAC;;;EACpBD,KAAK,EAAC,UAAU;EAACC,IAAI,EAAC;;;;EAYtBD,KAAK,EAAC,UAAU;EAACC,IAAI,EAAC;;;;EAYtBD,KAAK,EAAC,UAAU;EAACC,IAAI,EAAC;;;;EAYtBD,KAAK,EAAC,UAAU;EAACC,IAAI,EAAC;;;;EAgBzBD,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAkB;;;;EAaFA,KAAK,EAAC,YAAY;EAACC,IAAI,EAAC;;;EAqC9CD,KAAK,EAAC,cAAc;EAACE,GAAG,EAAC;;;;EAQrBF,KAAK,EAAC;AAAa;;;;EASZA,KAAK,EAAC,gCAAgC;EAAC,aAAW,EAAC;;;EAE1DA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAW;;;EAavBA,KAAK,EAAC,eAAe;EACrBC,IAAI,EAAC,MAAM;EACX,YAAU,EAAC;;;EAEND,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAc;;EACfA,KAAK,EAAC;AAAmB;;EAC3BA,KAAK,EAAC;AAAY;;;;EA2FXA,KAAK,EAAC,YAAY;EAACC,IAAI,EAAC,YAAY;EAAC,YAAU,EAAC;;;EACpED,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAiB;;EACtBA,KAAK,EAAC,iBAAiB;EAACC,IAAI,EAAC;;;EAC3BD,KAAK,EAAC,iBAAiB;EAACC,IAAI,EAAC;;;EAY7BD,KAAK,EAAC,iBAAiB;EAACC,IAAI,EAAC;;;EAY7BD,KAAK,EAAC,iBAAiB;EAACC,IAAI,EAAC;;;EAY7BD,KAAK,EAAC,iBAAiB;EAACC,IAAI,EAAC;;;;EAmBfD,KAAK,EAAC,oBAAoB;EAACC,IAAI,EAAC,YAAY;EAAC,YAAU,EAAC;;;EAC7ED,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAgB;;EACrBA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAiB;;EAUvBA,KAAK,EAAC,wBAAwB;EAAC,cAAY,EAAC;;;uBA3W1DG,mBAAA,CAkXS,UAlXTC,UAkXS,GAjXPC,mBAAA,+BAAkC,E,4BAClCC,mBAAA,CAAkE;IAA/DC,IAAI,EAAC,eAAe;IAACP,KAAK,EAAC;KAAY,sBAAoB,sBAE9DK,mBAAA,uBAA0B,E,4BAC1BC,mBAAA,CAoBM;IApBDN,KAAK,EAAC,YAAY;IAACC,IAAI,EAAC,QAAQ;IAAC,YAAU,EAAC;MAC/CK,mBAAA,CAkBM;IAlBDN,KAAK,EAAC;EAAoB,IAC7BM,mBAAA,CAQM;IARDN,KAAK,EAAC;EAAiB,IAC1BM,mBAAA,CAME;IALAE,GAAG,EAAC,kCAAkC;IACtCC,GAAG,EAAC,iBAAiB;IACrBT,KAAK,EAAC,WAAW;IACjBU,KAAK,EAAC,IAAI;IACVC,MAAM,EAAC;QAGXL,mBAAA,CAGM;IAHDN,KAAK,EAAC;EAAiB,IAC1BM,mBAAA,CAAgE;IAA1DN,KAAK,EAAC;EAAkB,GAAC,4BAA0B,GACzDM,mBAAA,CAA6E;IAArEN,KAAK,EAAC;EAAmB,GAAC,oCAAkC,E,GAEtEM,mBAAA,CAGM;IAHDN,KAAK,EAAC;EAAmB,IAC5BM,mBAAA,CAAoD;IAAjDN,KAAK,EAAC,mBAAmB;IAAC,aAAW,EAAC;MACzCM,mBAAA,CAAmB,cAAb,QAAM,E,0BAKlBD,mBAAA,iBAAoB,EACpBC,mBAAA,CAkQM,OAlQNM,UAkQM,GAjQJN,mBAAA,CAgQM,OAhQNO,UAgQM,GA/PJR,mBAAA,4BAA+B,EAC/BC,mBAAA,CAmBM,OAnBNQ,UAmBM,GAlBJR,mBAAA,CAiBS;IAhBPN,KAAK,EAAC,aAAa;IAClBe,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,MAAA,CAAAC,gBAAgB;IACxB,YAAU,EAAC,iBAAiB;IAC5BC,IAAI,EAAC;kCAELd,mBAAA,CAME;IALAE,GAA+B,EAA/BT,UAA+B;IAC/BU,GAAG,EAAC,oBAAoB;IACxBT,KAAK,EAAC,MAAM;IACZU,KAAK,EAAC,IAAI;IACVC,MAAM,EAAC;8BAETL,mBAAA,CAGM;IAHDN,KAAK,EAAC;EAAe,IACxBM,mBAAA,CAAyC;IAArCN,KAAK,EAAC;EAAY,GAAC,eAAa,GACpCM,mBAAA,CAA0D;IAApDN,KAAK,EAAC;EAAe,GAAC,yBAAuB,E,0BAKzDK,mBAAA,wBAA2B,EAC3BC,mBAAA,CAmDM,OAnDNe,UAmDM,GAlDJf,mBAAA,CAiDK,MAjDLgB,UAiDK,GAhDHhB,mBAAA,CAWK,MAXLiB,UAWK,GAVHjB,mBAAA,CASS;IARPN,KAAK,EAAAwB,eAAA,EAAC,UAAU;MAAAC,MAAA,EACEC,MAAA,CAAAC,UAAU;IAAA;IAC3BZ,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,MAAA,CAAAC,gBAAgB;IACxBlB,IAAI,EAAC,UAAU;IACd,cAAY,EAAEyB,MAAA,CAAAC,UAAU,4BAA4BC;kCAErDtB,mBAAA,CAA8C;IAA3CN,KAAK,EAAC,aAAa;IAAC,aAAW,EAAC;8BACnCM,mBAAA,CAAsB,cAAhB,WAAS,oB,yCAGnBA,mBAAA,CAWK,MAXLuB,UAWK,GAVHvB,mBAAA,CASS;IARPN,KAAK,EAAAwB,eAAA,EAAC,UAAU;MAAAC,MAAA,EACEC,MAAA,CAAAC,UAAU;IAAA;IAC3BZ,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,MAAA,CAAAC,gBAAgB;IACxBlB,IAAI,EAAC,UAAU;IACd,cAAY,EAAEyB,MAAA,CAAAC,UAAU,2BAA2BC;kCAEpDtB,mBAAA,CAAkD;IAA/CN,KAAK,EAAC,iBAAiB;IAAC,aAAW,EAAC;8BACvCM,mBAAA,CAAqB,cAAf,UAAQ,oB,0CAGlBA,mBAAA,CAWK,MAXLwB,WAWK,GAVHxB,mBAAA,CASS;IARPN,KAAK,EAAAwB,eAAA,EAAC,UAAU;MAAAC,MAAA,EACEC,MAAA,CAAAC,UAAU;IAAA;IAC3BZ,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,MAAA,CAAAC,gBAAgB;IACxBlB,IAAI,EAAC,UAAU;IACd,cAAY,EAAEyB,MAAA,CAAAC,UAAU,2BAA2BC;kCAEpDtB,mBAAA,CAA+C;IAA5CN,KAAK,EAAC,cAAc;IAAC,aAAW,EAAC;8BACpCM,mBAAA,CAAwB,cAAlB,aAAW,oB,0CAGrBA,mBAAA,CAWK,MAXLyB,WAWK,GAVHzB,mBAAA,CASS;IARPN,KAAK,EAAAwB,eAAA,EAAC,UAAU;MAAAC,MAAA,EACEC,MAAA,CAAAC,UAAU;IAAA;IAC3BZ,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,MAAA,CAAAC,gBAAgB;IACxBlB,IAAI,EAAC,UAAU;IACd,cAAY,EAAEyB,MAAA,CAAAC,UAAU,uBAAuBC;kCAEhDtB,mBAAA,CAAyD;IAAtDN,KAAK,EAAC,wBAAwB;IAAC,aAAW,EAAC;8BAC9CM,mBAAA,CAAiB,cAAX,MAAI,oB,8CAMlBD,mBAAA,oBAAuB,EACvBC,mBAAA,CAiLM,OAjLN0B,WAiLM,GAhLJ3B,mBAAA,YAAe,EACfC,mBAAA,CAwCM,OAxCN2B,WAwCM,GAvCJ3B,mBAAA,CASS;IARPN,KAAK,EAAC,eAAe;IACpBe,OAAK,EAAEG,MAAA,CAAAgB,YAAY;IACnB,YAAU,EAAEhB,MAAA,CAAAiB,UAAU;IACtB,eAAa,EAAEjB,MAAA,CAAAiB,UAAU;IAC1Bf,IAAI,EAAC;kCAELd,mBAAA,CAAgD;IAA7CN,KAAK,EAAC,eAAe;IAAC,aAAW,EAAC;8BACrCM,mBAAA,CAAmC;IAA7BN,KAAK,EAAC;EAAS,GAAC,QAAM,oB,gCAG9BoC,YAAA,CA2BaC,WAAA;IA3BDC,IAAI,EAAC;EAAc;sBAC7B,MAyBM,CAzBKpB,MAAA,CAAAiB,UAAU,I,cAArBhC,mBAAA,CAyBM,OAzBNoC,WAyBM,G,4BAxBJjC,mBAAA,CAAgF;MAAzEkC,GAAG,EAAC,eAAe;MAACxC,KAAK,EAAC;OAAU,+BAA6B,sB,gBACxEM,mBAAA,CAWE;MAVAmC,EAAE,EAAC,eAAe;MAClBvC,GAAG,EAAC,aAAa;MACjBkB,IAAI,EAAC,QAAQ;MACbsB,WAAW,EAAC,+BAA+B;MAC3C1C,KAAK,EAAC,cAAc;iEACXkB,MAAA,CAAAyB,WAAW,GAAA1B,MAAA;MACnB2B,OAAK,G,UAAQ1B,MAAA,CAAA2B,aAAa,c,UACZ3B,MAAA,CAAA4B,WAAW,c;MAC1BC,YAAY,EAAC,KAAK;MAClB,kBAAgB,EAAC;mEAJR7B,MAAA,CAAAyB,WAAW,E,+BAMtBrC,mBAAA,CAEO;MAFDmC,EAAE,EAAC,aAAa;MAACzC,KAAK,EAAC;OAAU,0CAEvC,sBACAM,mBAAA,CAOS;MANPN,KAAK,EAAC,eAAe;MACpBe,OAAK,EAAEG,MAAA,CAAA2B,aAAa;MACrB,YAAU,EAAC,eAAe;MAC1BzB,IAAI,EAAC;oCAELd,mBAAA,CAAgD;MAA7CN,KAAK,EAAC,eAAe;MAAC,aAAW,EAAC;;;QAM7CK,mBAAA,mBAAsB,EACtB+B,YAAA,CAIElB,MAAA;IAHC8B,iBAAgB,EAAE9B,MAAA,CAAA+B,qBAAqB;IACvCC,mBAAkB,EAAEhC,MAAA,CAAAiC,uBAAuB;IAC3CC,OAAK,EAAElC,MAAA,CAAAmC;MAGVhD,mBAAA,uBAA0B,EAC1BC,mBAAA,CA+GM,OA/GNgD,WA+GM,GA9GJhD,mBAAA,CA2BS;IA1BPN,KAAK,EAAC,aAAa;IAClBe,OAAK,EAAEG,MAAA,CAAAqC,kBAAkB;IACzB,YAAU,mBAAmB7B,MAAA,CAAA8B,QAAQ;IACrC,eAAa,EAAE9B,MAAA,CAAA+B,gBAAgB;IAChCrC,IAAI,EAAC;MAELd,mBAAA,CAUM,OAVNoD,WAUM,GARIhC,MAAA,CAAAiC,UAAU,I,cADlBxD,mBAAA,CAOE;;IALCK,GAAG,EAAEkB,MAAA,CAAAiC,UAAU;IACflD,GAAG,KAAKiB,MAAA,CAAA8B,QAAQ;IACjBxD,KAAK,EAAC,cAAc;IACpBU,KAAK,EAAC,IAAI;IACVC,MAAM,EAAC;yDAETR,mBAAA,CAAwE,KAAxEyD,WAAwE,G,GAE1EtD,mBAAA,CAGM,OAHNuD,WAGM,GAFJvD,mBAAA,CAA6C,QAA7CwD,WAA6C,EAAAC,gBAAA,CAAlBrC,MAAA,CAAA8B,QAAQ,kB,4BACnClD,mBAAA,CAA4C;IAAtCN,KAAK,EAAC;EAAW,GAAC,eAAa,qB,GAEvCM,mBAAA,CAIK;IAHHN,KAAK,EAAAwB,eAAA,EAAC,oCAAoC;MAAAwC,OAAA,EACvBtC,MAAA,CAAA+B;IAAgB;IACnC,aAAW,EAAC;yDAIhBrB,YAAA,CAgFaC,WAAA;IAhFDC,IAAI,EAAC;EAAe;sBAC9B,MA8EM,CA7EEZ,MAAA,CAAA+B,gBAAgB,I,cADxBtD,mBAAA,CA8EM,OA9EN8D,WA8EM,GAxEJ3D,mBAAA,CAKM,OALN4D,WAKM,GAJJ5D,mBAAA,CAGM,OAHN6D,WAGM,GAFJ7D,mBAAA,CAAyD,UAAzD8D,WAAyD,EAAAL,gBAAA,CAApBrC,MAAA,CAAA8B,QAAQ,kBAC7ClD,mBAAA,CAA+C,QAA/C+D,WAA+C,EAAAN,gBAAA,CAAnBrC,MAAA,CAAA4C,SAAS,iB,iCAIzChE,mBAAA,CAAqD;MAAhDN,KAAK,EAAC,kBAAkB;MAACC,IAAI,EAAC;iCAEnCK,mBAAA,CAQS;MAPPN,KAAK,EAAC,eAAe;MACpBe,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,MAAA,CAAAC,gBAAgB;MACxBlB,IAAI,EAAC,UAAU;MACfmB,IAAI,EAAC;oCAELd,mBAAA,CAA8C;MAA3CN,KAAK,EAAC,aAAa;MAAC,aAAW,EAAC;gCACnCM,mBAAA,CAAuB,cAAjB,YAAU,oB,IAGlBA,mBAAA,CAQS;MAPPN,KAAK,EAAC,eAAe;MACpBe,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,MAAA,CAAAC,gBAAgB;MACxBlB,IAAI,EAAC,UAAU;MACfmB,IAAI,EAAC;oCAELd,mBAAA,CAA6C;MAA1CN,KAAK,EAAC,YAAY;MAAC,aAAW,EAAC;gCAClCM,mBAAA,CAA6B,cAAvB,kBAAgB,oB,IAGxBA,mBAAA,CAQS;MAPPN,KAAK,EAAC,eAAe;MACpBe,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,MAAA,CAAAC,gBAAgB;MACxBlB,IAAI,EAAC,UAAU;MACfmB,IAAI,EAAC;oCAELd,mBAAA,CAAgD;MAA7CN,KAAK,EAAC,eAAe;MAAC,aAAW,EAAC;gCACrCM,mBAAA,CAAyB,cAAnB,cAAY,oB,IAGpBA,mBAAA,CAQS;MAPPN,KAAK,EAAC,eAAe;MACpBe,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,MAAA,CAAAC,gBAAgB;MACxBlB,IAAI,EAAC,UAAU;MACfmB,IAAI,EAAC;oCAELd,mBAAA,CAAiD;MAA9CN,KAAK,EAAC,gBAAgB;MAAC,aAAW,EAAC;gCACtCM,mBAAA,CAA4B,cAAtB,iBAAe,oB,gCAGvBA,mBAAA,CAAqD;MAAhDN,KAAK,EAAC,kBAAkB;MAACC,IAAI,EAAC;iCAEnCK,mBAAA,CAQS;MAPPN,KAAK,EAAC,eAAe;MACpBe,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,MAAA,CAAAC,gBAAgB;MACxBlB,IAAI,EAAC,UAAU;MACfmB,IAAI,EAAC;oCAELd,mBAAA,CAAyD;MAAtDN,KAAK,EAAC,wBAAwB;MAAC,aAAW,EAAC;gCAC9CM,mBAAA,CAA2B,cAArB,gBAAc,oB,gCAGtBA,mBAAA,CAAqD;MAAhDN,KAAK,EAAC,kBAAkB;MAACC,IAAI,EAAC;iCAEnCK,mBAAA,CAQS;MAPPN,KAAK,EAAC,2BAA2B;MAChCe,OAAK,EAAEG,MAAA,CAAAqD,YAAY;MACpBtE,IAAI,EAAC,UAAU;MACfmB,IAAI,EAAC;oCAELd,mBAAA,CAAsD;MAAnDN,KAAK,EAAC,qBAAqB;MAAC,aAAW,EAAC;gCAC3CM,mBAAA,CAAqB,cAAf,UAAQ,oB;;8BAMtBD,mBAAA,wBAA2B,EAC3BC,mBAAA,CAUS;IATPN,KAAK,EAAC,oBAAoB;IACzBe,OAAK,EAAEG,MAAA,CAAAsD,gBAAgB;IACvB,YAAU,EAAEtD,MAAA,CAAAuD,cAAc;IAC1B,eAAa,EAAEvD,MAAA,CAAAuD,cAAc;IAC9BrD,IAAI,EAAC;MAELd,mBAAA,CAAwE;IAAlEN,KAAK,EAAAwB,eAAA,EAAC,gBAAgB;MAAAC,MAAA,EAAmBP,MAAA,CAAAuD;IAAc;2BAC7DnE,mBAAA,CAAwE;IAAlEN,KAAK,EAAAwB,eAAA,EAAC,gBAAgB;MAAAC,MAAA,EAAmBP,MAAA,CAAAuD;IAAc;2BAC7DnE,mBAAA,CAAwE;IAAlEN,KAAK,EAAAwB,eAAA,EAAC,gBAAgB;MAAAC,MAAA,EAAmBP,MAAA,CAAAuD;IAAc;+DAMrEpE,mBAAA,4BAA+B,EAC/B+B,YAAA,CAyDaC,WAAA;IAzDDC,IAAI,EAAC;EAAmB;sBAClC,MAuDM,CAvDKpB,MAAA,CAAAuD,cAAc,I,cAAzBtE,mBAAA,CAuDM,OAvDNuE,WAuDM,GAtDJpE,mBAAA,CAqDM,OArDNqE,WAqDM,GApDJrE,mBAAA,CAmDM,OAnDNsE,WAmDM,GAlDJtE,mBAAA,CAiDK,MAjDLuE,WAiDK,GAhDHvE,mBAAA,CAWK,MAXLwE,WAWK,GAVHxE,mBAAA,CASS;MARPN,KAAK,EAAAwB,eAAA,EAAC,iBAAiB;QAAAC,MAAA,EACLC,MAAA,CAAAC,UAAU;MAAA;MAC3BZ,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,MAAA,CAAA6D,sBAAsB;MAC9B9E,IAAI,EAAC,UAAU;MACfmB,IAAI,EAAC;oCAELd,mBAAA,CAA8C;MAA3CN,KAAK,EAAC,aAAa;MAAC,aAAW,EAAC;gCACnCM,mBAAA,CAAsB,cAAhB,WAAS,oB,qBAGnBA,mBAAA,CAWK,MAXL0E,WAWK,GAVH1E,mBAAA,CASS;MARPN,KAAK,EAAAwB,eAAA,EAAC,iBAAiB;QAAAC,MAAA,EACLC,MAAA,CAAAC,UAAU;MAAA;MAC3BZ,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,MAAA,CAAA6D,sBAAsB;MAC9B9E,IAAI,EAAC,UAAU;MACfmB,IAAI,EAAC;oCAELd,mBAAA,CAAkD;MAA/CN,KAAK,EAAC,iBAAiB;MAAC,aAAW,EAAC;gCACvCM,mBAAA,CAAqB,cAAf,UAAQ,oB,qBAGlBA,mBAAA,CAWK,MAXL2E,WAWK,GAVH3E,mBAAA,CASS;MARPN,KAAK,EAAAwB,eAAA,EAAC,iBAAiB;QAAAC,MAAA,EACLC,MAAA,CAAAC,UAAU;MAAA;MAC3BZ,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,MAAA,CAAA6D,sBAAsB;MAC9B9E,IAAI,EAAC,UAAU;MACfmB,IAAI,EAAC;oCAELd,mBAAA,CAA+C;MAA5CN,KAAK,EAAC,cAAc;MAAC,aAAW,EAAC;gCACpCM,mBAAA,CAAwB,cAAlB,aAAW,oB,qBAGrBA,mBAAA,CAWK,MAXL4E,WAWK,GAVH5E,mBAAA,CASS;MARPN,KAAK,EAAAwB,eAAA,EAAC,iBAAiB;QAAAC,MAAA,EACLC,MAAA,CAAAC,UAAU;MAAA;MAC3BZ,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,MAAA,CAAA6D,sBAAsB;MAC9B9E,IAAI,EAAC,UAAU;MACfmB,IAAI,EAAC;oCAELd,mBAAA,CAAyD;MAAtDN,KAAK,EAAC,wBAAwB;MAAC,aAAW,EAAC;gCAC9CM,mBAAA,CAAiB,cAAX,MAAI,oB;;MASxBD,mBAAA,2BAA8B,EACnBqB,MAAA,CAAAyD,eAAe,I,cAA1BhF,mBAAA,CAoBM,OApBNiF,WAoBM,GAnBJ9E,mBAAA,CAkBM,OAlBN+E,WAkBM,GAjBJ/E,mBAAA,CAgBM,OAhBNgF,WAgBM,GAfJhF,mBAAA,CAcK,MAdLiF,WAcK,GAbHjF,mBAAA,CASK,MATLkF,WASK,GARHlF,mBAAA,CAOS;IANPN,KAAK,EAAC,iBAAiB;IACtBe,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,MAAA,CAAAC,gBAAgB;IACxBC,IAAI,EAAC;kCAELd,mBAAA,CAA8C;IAA3CN,KAAK,EAAC,aAAa;IAAC,aAAW,EAAC;8BACnCM,mBAAA,CAAsB,cAAhB,WAAS,oB,MAGnBA,mBAAA,CAEK,MAFLmF,WAEK,GADHnF,mBAAA,CAAiC,cAAAyD,gBAAA,CAAxB7C,MAAA,CAAAwE,YAAY,mB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}