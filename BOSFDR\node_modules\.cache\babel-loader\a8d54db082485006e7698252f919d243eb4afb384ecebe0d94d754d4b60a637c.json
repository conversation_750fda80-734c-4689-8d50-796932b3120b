{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport documentRequestService from '@/services/documentRequestService';\nimport ClientHeader from './ClientHeader.vue';\nimport unifiedAuthService from '@/services/unifiedAuthService';\nexport default {\n  name: 'NewDocumentRequest',\n  components: {\n    ClientHeader\n  },\n  data() {\n    return {\n      documentTypes: [],\n      loading: true,\n      error: null,\n      // Header state\n      showUserDropdown: false,\n      sidebarCollapsed: false,\n      activeMenu: 'dashboard',\n      // User data\n      userName: 'User',\n      firstName: 'User',\n      totalRequests: 0,\n      pendingRequests: 0\n    };\n  },\n  async mounted() {\n    await this.loadUserData();\n    await this.loadDocumentTypes();\n    await this.loadUserStats();\n  },\n  methods: {\n    async loadUserData() {\n      try {\n        const currentUser = unifiedAuthService.getCurrentUser();\n        if (currentUser) {\n          this.userName = currentUser.username || 'User';\n          this.firstName = currentUser.first_name || currentUser.username || 'User';\n        }\n      } catch (error) {\n        console.error('Error loading user data:', error);\n      }\n    },\n    async loadUserStats() {\n      try {\n        // TODO: Implement API call to get user statistics\n        // For now, using placeholder data\n        this.totalRequests = 5;\n        this.pendingRequests = 2;\n      } catch (error) {\n        console.error('Error loading user stats:', error);\n      }\n    },\n    async loadDocumentTypes() {\n      try {\n        this.loading = true;\n        this.error = null;\n        const response = await documentRequestService.getDocumentTypes();\n        this.documentTypes = response.data || [];\n      } catch (error) {\n        console.error('Error loading document types:', error);\n        this.error = error.response?.data?.message || 'Failed to load available services';\n      } finally {\n        this.loading = false;\n      }\n    },\n    selectDocumentType(documentType) {\n      if (!documentType.is_active) return;\n\n      // Navigate to specific document request form\n      const routeName = this.getRouteForDocumentType(documentType.type_name);\n      if (routeName) {\n        this.$router.push({\n          name: routeName,\n          params: {\n            documentTypeId: documentType.id\n          }\n        });\n      }\n    },\n    getRouteForDocumentType(typeName) {\n      const routes = {\n        'Barangay Clearance': 'BarangayClearanceRequest',\n        'Cedula': 'CedulaRequest'\n      };\n      return routes[typeName];\n    },\n    getDocumentIcon(typeName) {\n      const icons = {\n        'Barangay Clearance': 'fas fa-certificate',\n        'Cedula': 'fas fa-id-card'\n      };\n      return icons[typeName] || 'fas fa-file-alt';\n    },\n    getProcessingTime(typeName) {\n      const times = {\n        'Barangay Clearance': '3-5 business days',\n        'Cedula': '1-2 business days'\n      };\n      return times[typeName] || '3-5 business days';\n    },\n    formatCurrency(amount) {\n      return parseFloat(amount).toFixed(2);\n    },\n    // Header event handlers\n    handleSidebarToggle() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n    },\n    handleUserDropdownToggle() {\n      this.showUserDropdown = !this.showUserDropdown;\n    },\n    handleMenuAction(action) {\n      console.log('Menu action:', action);\n      switch (action) {\n        case 'profile':\n          // TODO: Navigate to profile page\n          break;\n        case 'settings':\n          // TODO: Navigate to settings page\n          break;\n        case 'account':\n          // TODO: Navigate to account page\n          break;\n      }\n    },\n    handleLogout() {\n      try {\n        unifiedAuthService.logout();\n        this.$router.push({\n          name: 'WelcomePage'\n        });\n      } catch (error) {\n        console.error('Logout error:', error);\n      }\n    },\n    handleError(error) {\n      console.error('Header error:', error);\n    },\n    // Navigation methods\n    scrollToServices() {\n      this.$refs.servicesSection?.scrollIntoView({\n        behavior: 'smooth'\n      });\n    },\n    goToMyRequests() {\n      this.$router.push({\n        name: 'MyRequests'\n      });\n    },\n    goToProfile() {\n      // TODO: Navigate to profile page\n      console.log('Navigate to profile');\n    },\n    goBack() {\n      this.$router.push({\n        name: 'ClientDashboard'\n      });\n    },\n    openHelp() {\n      // TODO: Implement help/FAQ modal or page\n      console.log('Opening help...');\n    },\n    contactSupport() {\n      // TODO: Implement contact support functionality\n      console.log('Contacting support...');\n    }\n  }\n};", "map": {"version": 3, "names": ["documentRequestService", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unifiedAuthService", "name", "components", "data", "documentTypes", "loading", "error", "showUserDropdown", "sidebarCollapsed", "activeMenu", "userName", "firstName", "totalRequests", "pendingRequests", "mounted", "loadUserData", "loadDocumentTypes", "loadUserStats", "methods", "currentUser", "getCurrentUser", "username", "first_name", "console", "response", "getDocumentTypes", "message", "selectDocumentType", "documentType", "is_active", "routeName", "getRouteForDocumentType", "type_name", "$router", "push", "params", "documentTypeId", "id", "typeName", "routes", "getDocumentIcon", "icons", "getProcessingTime", "times", "formatCurrency", "amount", "parseFloat", "toFixed", "handleSidebarToggle", "handleUserDropdownToggle", "handleMenuAction", "action", "log", "handleLogout", "logout", "handleError", "scrollToServices", "$refs", "servicesSection", "scrollIntoView", "behavior", "goToMyRequests", "goToProfile", "goBack", "openHelp", "contactSupport"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\client\\NewDocumentRequest.vue"], "sourcesContent": ["<template>\n  <div class=\"client-dashboard\">\n    <!-- Background -->\n    <div class=\"background-container\">\n      <div class=\"background-image\"></div>\n      <div class=\"background-overlay\"></div>\n    </div>\n\n    <!-- Client Header with Navigation -->\n    <ClientHeader\n      :userName=\"userName\"\n      :showUserDropdown=\"showUserDropdown\"\n      :sidebarCollapsed=\"sidebarCollapsed\"\n      :activeMenu=\"activeMenu\"\n      @sidebar-toggle=\"handleSidebarToggle\"\n      @user-dropdown-toggle=\"handleUserDropdownToggle\"\n      @menu-action=\"handleMenuAction\"\n      @logout=\"handleLogout\"\n      @error=\"handleError\"\n    />\n\n    <!-- Main Content -->\n    <main class=\"main-content\" :class=\"{ 'sidebar-collapsed': sidebarCollapsed }\">\n      <!-- Welcome Section -->\n      <section class=\"welcome-section\">\n        <div class=\"container\">\n          <div class=\"welcome-header\">\n            <div class=\"welcome-text\">\n              <h1 class=\"welcome-title\">Welcome back, {{ firstName }}!</h1>\n              <p class=\"welcome-subtitle\">Request official documents quickly and securely through our digital platform.</p>\n            </div>\n            <div class=\"welcome-stats\">\n              <div class=\"stat-card\">\n                <div class=\"stat-icon\">\n                  <i class=\"fas fa-file-alt\"></i>\n                </div>\n                <div class=\"stat-content\">\n                  <div class=\"stat-number\">{{ totalRequests }}</div>\n                  <div class=\"stat-label\">Total Requests</div>\n                </div>\n              </div>\n              <div class=\"stat-card\">\n                <div class=\"stat-icon\">\n                  <i class=\"fas fa-clock\"></i>\n                </div>\n                <div class=\"stat-content\">\n                  <div class=\"stat-number\">{{ pendingRequests }}</div>\n                  <div class=\"stat-label\">Pending</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Quick Actions Section -->\n      <section class=\"quick-actions-section\">\n        <div class=\"container\">\n          <div class=\"section-header\">\n            <h2 class=\"section-title\">Quick Actions</h2>\n            <p class=\"section-description\">Start a new document request or manage existing ones</p>\n          </div>\n\n          <div class=\"quick-actions-grid\">\n            <div class=\"action-card primary\" @click=\"scrollToServices\">\n              <div class=\"action-icon\">\n                <i class=\"fas fa-plus-circle\"></i>\n              </div>\n              <div class=\"action-content\">\n                <h3>New Request</h3>\n                <p>Start a new document request</p>\n              </div>\n              <div class=\"action-arrow\">\n                <i class=\"fas fa-arrow-right\"></i>\n              </div>\n            </div>\n\n            <div class=\"action-card\" @click=\"goToMyRequests\">\n              <div class=\"action-icon\">\n                <i class=\"fas fa-list-alt\"></i>\n              </div>\n              <div class=\"action-content\">\n                <h3>My Requests</h3>\n                <p>View and track your requests</p>\n              </div>\n              <div class=\"action-arrow\">\n                <i class=\"fas fa-arrow-right\"></i>\n              </div>\n            </div>\n\n            <div class=\"action-card\" @click=\"goToProfile\">\n              <div class=\"action-icon\">\n                <i class=\"fas fa-user-edit\"></i>\n              </div>\n              <div class=\"action-content\">\n                <h3>Update Profile</h3>\n                <p>Keep your information current</p>\n              </div>\n              <div class=\"action-arrow\">\n                <i class=\"fas fa-arrow-right\"></i>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Document Services Section -->\n      <section class=\"services-section\" ref=\"servicesSection\">\n        <div class=\"container\">\n          <div class=\"section-header\">\n            <h2 class=\"section-title\">Available Document Services</h2>\n            <p class=\"section-description\">Choose the type of document you want to request</p>\n          </div>\n\n          <!-- Loading State -->\n          <div v-if=\"loading\" class=\"loading-container\">\n            <div class=\"loading-spinner\">\n              <i class=\"fas fa-spinner fa-spin\"></i>\n            </div>\n            <p>Loading available services...</p>\n          </div>\n\n          <!-- Error State -->\n          <div v-else-if=\"error\" class=\"error-container\">\n            <div class=\"error-content\">\n              <i class=\"fas fa-exclamation-triangle\"></i>\n              <h3>Unable to Load Services</h3>\n              <p>{{ error }}</p>\n              <button class=\"retry-btn\" @click=\"loadDocumentTypes\">\n                <i class=\"fas fa-redo\"></i>\n                Try Again\n              </button>\n            </div>\n          </div>\n\n          <!-- Document Types Grid -->\n          <div v-else class=\"document-types-grid\">\n            <div\n              v-for=\"documentType in documentTypes\"\n              :key=\"documentType.id\"\n              class=\"document-card\"\n              @click=\"selectDocumentType(documentType)\"\n              :class=\"{ 'disabled': !documentType.is_active }\"\n            >\n              <div class=\"document-icon\">\n                <i :class=\"getDocumentIcon(documentType.type_name)\"></i>\n              </div>\n\n              <div class=\"document-content\">\n                <h3 class=\"document-title\">{{ documentType.type_name }}</h3>\n                <p class=\"document-description\">{{ documentType.description }}</p>\n\n                <div class=\"document-details\">\n                  <div class=\"fee-info\">\n                    <span class=\"fee-label\">Base Fee:</span>\n                    <span class=\"fee-amount\">₱{{ formatCurrency(documentType.base_fee) }}</span>\n                  </div>\n\n                  <div class=\"processing-time\">\n                    <i class=\"fas fa-clock\"></i>\n                    <span>{{ getProcessingTime(documentType.type_name) }}</span>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"document-action\">\n                <span v-if=\"!documentType.is_active\" class=\"status-badge unavailable\">\n                  Unavailable\n                </span>\n                <i v-else class=\"fas fa-chevron-right\"></i>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Information Section -->\n      <section class=\"info-section\">\n        <div class=\"container\">\n          <div class=\"info-grid\">\n            <div class=\"info-card\">\n              <div class=\"info-header\">\n                <i class=\"fas fa-info-circle\"></i>\n                <h3>Important Information</h3>\n              </div>\n              <div class=\"info-content\">\n                <ul class=\"info-list\">\n                  <li>\n                    <i class=\"fas fa-check\"></i>\n                    Ensure your profile information is complete and accurate\n                  </li>\n                  <li>\n                    <i class=\"fas fa-check\"></i>\n                    Have your valid ID and supporting documents ready\n                  </li>\n                  <li>\n                    <i class=\"fas fa-check\"></i>\n                    Processing time may vary depending on document verification\n                  </li>\n                  <li>\n                    <i class=\"fas fa-check\"></i>\n                    You can pay online using various payment methods\n                  </li>\n                </ul>\n              </div>\n            </div>\n\n            <div class=\"help-card\">\n              <div class=\"help-header\">\n                <i class=\"fas fa-headset\"></i>\n                <h3>Need Help?</h3>\n              </div>\n              <div class=\"help-content\">\n                <p>If you have questions about document requirements or the application process:</p>\n                <div class=\"help-actions\">\n                  <button class=\"help-btn\" @click=\"openHelp\">\n                    <i class=\"fas fa-question-circle\"></i>\n                    View FAQ\n                  </button>\n                  <button class=\"contact-btn\" @click=\"contactSupport\">\n                    <i class=\"fas fa-phone\"></i>\n                    Contact Support\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n    </main>\n  </div>\n</template>\n\n<script>\nimport documentRequestService from '@/services/documentRequestService';\nimport ClientHeader from './ClientHeader.vue';\nimport unifiedAuthService from '@/services/unifiedAuthService';\n\nexport default {\n  name: 'NewDocumentRequest',\n  components: {\n    ClientHeader\n  },\n  data() {\n    return {\n      documentTypes: [],\n      loading: true,\n      error: null,\n      // Header state\n      showUserDropdown: false,\n      sidebarCollapsed: false,\n      activeMenu: 'dashboard',\n      // User data\n      userName: 'User',\n      firstName: 'User',\n      totalRequests: 0,\n      pendingRequests: 0\n    };\n  },\n  async mounted() {\n    await this.loadUserData();\n    await this.loadDocumentTypes();\n    await this.loadUserStats();\n  },\n  methods: {\n    async loadUserData() {\n      try {\n        const currentUser = unifiedAuthService.getCurrentUser();\n        if (currentUser) {\n          this.userName = currentUser.username || 'User';\n          this.firstName = currentUser.first_name || currentUser.username || 'User';\n        }\n      } catch (error) {\n        console.error('Error loading user data:', error);\n      }\n    },\n\n    async loadUserStats() {\n      try {\n        // TODO: Implement API call to get user statistics\n        // For now, using placeholder data\n        this.totalRequests = 5;\n        this.pendingRequests = 2;\n      } catch (error) {\n        console.error('Error loading user stats:', error);\n      }\n    },\n\n    async loadDocumentTypes() {\n      try {\n        this.loading = true;\n        this.error = null;\n\n        const response = await documentRequestService.getDocumentTypes();\n        this.documentTypes = response.data || [];\n\n      } catch (error) {\n        console.error('Error loading document types:', error);\n        this.error = error.response?.data?.message || 'Failed to load available services';\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    selectDocumentType(documentType) {\n      if (!documentType.is_active) return;\n\n      // Navigate to specific document request form\n      const routeName = this.getRouteForDocumentType(documentType.type_name);\n      if (routeName) {\n        this.$router.push({ \n          name: routeName,\n          params: { documentTypeId: documentType.id }\n        });\n      }\n    },\n\n    getRouteForDocumentType(typeName) {\n      const routes = {\n        'Barangay Clearance': 'BarangayClearanceRequest',\n        'Cedula': 'CedulaRequest'\n      };\n      return routes[typeName];\n    },\n\n    getDocumentIcon(typeName) {\n      const icons = {\n        'Barangay Clearance': 'fas fa-certificate',\n        'Cedula': 'fas fa-id-card'\n      };\n      return icons[typeName] || 'fas fa-file-alt';\n    },\n\n    getProcessingTime(typeName) {\n      const times = {\n        'Barangay Clearance': '3-5 business days',\n        'Cedula': '1-2 business days'\n      };\n      return times[typeName] || '3-5 business days';\n    },\n\n    formatCurrency(amount) {\n      return parseFloat(amount).toFixed(2);\n    },\n\n    // Header event handlers\n    handleSidebarToggle() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n    },\n\n    handleUserDropdownToggle() {\n      this.showUserDropdown = !this.showUserDropdown;\n    },\n\n    handleMenuAction(action) {\n      console.log('Menu action:', action);\n      switch (action) {\n        case 'profile':\n          // TODO: Navigate to profile page\n          break;\n        case 'settings':\n          // TODO: Navigate to settings page\n          break;\n        case 'account':\n          // TODO: Navigate to account page\n          break;\n      }\n    },\n\n    handleLogout() {\n      try {\n        unifiedAuthService.logout();\n        this.$router.push({ name: 'WelcomePage' });\n      } catch (error) {\n        console.error('Logout error:', error);\n      }\n    },\n\n    handleError(error) {\n      console.error('Header error:', error);\n    },\n\n    // Navigation methods\n    scrollToServices() {\n      this.$refs.servicesSection?.scrollIntoView({ behavior: 'smooth' });\n    },\n\n    goToMyRequests() {\n      this.$router.push({ name: 'MyRequests' });\n    },\n\n    goToProfile() {\n      // TODO: Navigate to profile page\n      console.log('Navigate to profile');\n    },\n\n    goBack() {\n      this.$router.push({ name: 'ClientDashboard' });\n    },\n\n    openHelp() {\n      // TODO: Implement help/FAQ modal or page\n      console.log('Opening help...');\n    },\n\n    contactSupport() {\n      // TODO: Implement contact support functionality\n      console.log('Contacting support...');\n    }\n  }\n};\n</script>\n\n<style scoped>\n/* Reset and base styles */\n* {\n  box-sizing: border-box;\n}\n\n.client-dashboard {\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n  line-height: 1.6;\n  color: #333;\n  background-color: #f8f9fa;\n  min-height: 100vh;\n}\n\n/* Background Setup */\n.background-container {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: -2;\n}\n\n.background-image {\n  width: 100%;\n  height: 100%;\n  background-image: url('@/assets/bula-request-background-pic.png');\n  background-size: cover;\n  background-position: center;\n  background-repeat: no-repeat;\n  background-attachment: fixed;\n}\n\n.background-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    135deg,\n    rgba(30, 58, 138, 0.85) 0%,\n    rgba(30, 64, 175, 0.9) 100%\n  );\n  z-index: -1;\n}\n\n/* Container utility */\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 1rem;\n}\n\n/* Main Content */\n.main-content {\n  margin-top: 80px; /* Account for fixed header */\n  transition: margin-left 0.3s ease;\n}\n\n.main-content.sidebar-collapsed {\n  margin-left: 0;\n}\n\n/* Welcome Section */\n.welcome-section {\n  padding: 2rem 0;\n  position: relative;\n  z-index: 1;\n}\n\n.welcome-header {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 2rem;\n  align-items: center;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: 1rem;\n  padding: 2rem;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.welcome-text {\n  color: #1e3a8a;\n}\n\n.welcome-title {\n  font-size: clamp(1.75rem, 4vw, 2.5rem);\n  font-weight: 700;\n  margin-bottom: 0.75rem;\n  color: #1e3a8a;\n}\n\n.welcome-subtitle {\n  font-size: 1.125rem;\n  color: #6b7280;\n  margin: 0;\n  line-height: 1.6;\n}\n\n.welcome-stats {\n  display: flex;\n  gap: 1rem;\n}\n\n.stat-card {\n  background: linear-gradient(135deg, #1e3a8a, #1e40af);\n  border-radius: 1rem;\n  padding: 1.5rem;\n  color: white;\n  text-align: center;\n  flex: 1;\n  box-shadow: 0 4px 20px rgba(30, 58, 138, 0.3);\n}\n\n.stat-icon {\n  font-size: 2rem;\n  color: #fbbf24;\n  margin-bottom: 0.75rem;\n}\n\n.stat-number {\n  font-size: 2rem;\n  font-weight: 700;\n  margin-bottom: 0.25rem;\n  color: #fbbf24;\n}\n\n.stat-label {\n  font-size: 0.875rem;\n  opacity: 0.9;\n}\n\n/* Quick Actions Section */\n.quick-actions-section {\n  padding: 2rem 0;\n  position: relative;\n  z-index: 1;\n}\n\n.section-header {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n\n.section-title {\n  font-size: clamp(1.75rem, 4vw, 2.25rem);\n  font-weight: 700;\n  color: white;\n  margin-bottom: 0.75rem;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n}\n\n.section-description {\n  font-size: 1.125rem;\n  color: rgba(255, 255, 255, 0.9);\n  margin: 0;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\n}\n\n.quick-actions-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: 1.5rem;\n}\n\n.action-card {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: 1rem;\n  padding: 2rem;\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n}\n\n.action-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);\n  border-color: #1e3a8a;\n}\n\n.action-card.primary {\n  background: linear-gradient(135deg, #1e3a8a, #1e40af);\n  color: white;\n}\n\n.action-card.primary:hover {\n  border-color: #fbbf24;\n}\n\n.action-icon {\n  width: 4rem;\n  height: 4rem;\n  background: linear-gradient(135deg, #fbbf24, #f59e0b);\n  border-radius: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.5rem;\n  color: #1e3a8a;\n  flex-shrink: 0;\n}\n\n.action-card.primary .action-icon {\n  background: rgba(251, 191, 36, 0.2);\n  color: #fbbf24;\n}\n\n.action-content {\n  flex: 1;\n}\n\n.action-content h3 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  color: inherit;\n}\n\n.action-content p {\n  margin: 0;\n  opacity: 0.8;\n  line-height: 1.5;\n}\n\n.action-arrow {\n  color: #6b7280;\n  font-size: 1.25rem;\n}\n\n.action-card.primary .action-arrow {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n/* Services Section */\n.services-section {\n  padding: 2rem 0;\n  position: relative;\n  z-index: 1;\n}\n\n.services-section .section-title {\n  color: white;\n}\n\n.services-section .section-description {\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.loading-container, .error-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n  text-align: center;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: 1rem;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.loading-spinner i {\n  font-size: 3rem;\n  color: #1e3a8a;\n  margin-bottom: 1rem;\n}\n\n.error-content {\n  max-width: 400px;\n}\n\n.error-content i {\n  font-size: 3rem;\n  color: #dc2626;\n  margin-bottom: 1rem;\n}\n\n.retry-btn {\n  background: linear-gradient(135deg, #1e3a8a, #1e40af);\n  color: white;\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 0.75rem;\n  cursor: pointer;\n  margin-top: 1rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);\n}\n\n.retry-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.4);\n}\n\n.document-types-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n  gap: 1.5rem;\n}\n\n.document-card {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border: 2px solid rgba(30, 58, 138, 0.2);\n  border-radius: 1rem;\n  padding: 2rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n}\n\n.document-card:hover:not(.disabled) {\n  border-color: #1e3a8a;\n  transform: translateY(-5px);\n  box-shadow: 0 12px 30px rgba(30, 58, 138, 0.2);\n  background: rgba(255, 255, 255, 1);\n}\n\n.document-card.disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.document-icon {\n  flex-shrink: 0;\n  width: 4rem;\n  height: 4rem;\n  background: linear-gradient(135deg, #1e3a8a, #1e40af);\n  border-radius: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 1.5rem;\n  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);\n}\n\n.document-content {\n  flex: 1;\n}\n\n.document-title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1e3a8a;\n  margin-bottom: 0.5rem;\n}\n\n.document-description {\n  color: #6b7280;\n  margin-bottom: 1rem;\n  line-height: 1.5;\n}\n\n.document-details {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.fee-info {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.fee-label {\n  color: #9ca3af;\n  font-size: 0.9rem;\n}\n\n.fee-amount {\n  font-weight: 600;\n  color: #059669;\n  font-size: 1.1rem;\n}\n\n.processing-time {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #9ca3af;\n  font-size: 0.9rem;\n}\n\n.document-action {\n  flex-shrink: 0;\n  display: flex;\n  align-items: center;\n}\n\n.document-action i {\n  color: #d1d5db;\n  font-size: 1.25rem;\n}\n\n.status-badge.unavailable {\n  background: #fecaca;\n  color: #dc2626;\n  padding: 0.25rem 0.75rem;\n  border-radius: 1rem;\n  font-size: 0.8rem;\n  font-weight: 500;\n}\n\n/* Info Section */\n.info-section {\n  padding: 2rem 0;\n  position: relative;\n  z-index: 1;\n}\n\n.info-grid {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 2rem;\n}\n\n.info-card, .help-card {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(30, 58, 138, 0.2);\n  border-radius: 1rem;\n  padding: 2rem;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n}\n\n.info-card:hover, .help-card:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 8px 30px rgba(30, 58, 138, 0.15);\n  background: rgba(255, 255, 255, 1);\n  border-color: #1e3a8a;\n}\n\n.info-header, .help-header {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  margin-bottom: 1.5rem;\n}\n\n.info-header h3, .help-header h3 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1e3a8a;\n  margin: 0;\n}\n\n.info-header i {\n  color: #1e3a8a;\n  font-size: 1.25rem;\n}\n\n.help-header i {\n  color: #059669;\n  font-size: 1.25rem;\n}\n\n.info-list {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.info-list li {\n  display: flex;\n  align-items: flex-start;\n  gap: 0.75rem;\n  margin-bottom: 1rem;\n  color: #6b7280;\n  line-height: 1.6;\n}\n\n.info-list i {\n  color: #059669;\n  margin-top: 0.125rem;\n  flex-shrink: 0;\n}\n\n.help-content p {\n  color: #6b7280;\n  margin-bottom: 1.5rem;\n  line-height: 1.6;\n}\n\n.help-actions {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.help-btn, .contact-btn {\n  background: rgba(255, 255, 255, 0.9);\n  backdrop-filter: blur(5px);\n  border: 2px solid rgba(30, 58, 138, 0.3);\n  padding: 0.875rem 1.25rem;\n  border-radius: 0.75rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  color: #6b7280;\n  font-weight: 500;\n  font-size: 0.95rem;\n}\n\n.help-btn:hover {\n  border-color: #1e3a8a;\n  color: #1e3a8a;\n  background: rgba(30, 58, 138, 0.1);\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.2);\n}\n\n.contact-btn:hover {\n  border-color: #059669;\n  color: #059669;\n  background: rgba(5, 150, 105, 0.1);\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(5, 150, 105, 0.2);\n}\n\n@media (max-width: 768px) {\n  .page-header {\n    margin: 1rem;\n    padding: 1.5rem;\n  }\n\n  .content-wrapper {\n    margin: 1rem;\n    padding: 1.5rem;\n  }\n\n  .header-content {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 1.5rem;\n  }\n\n  .header-logo {\n    width: 60px;\n    height: 60px;\n  }\n\n  .page-title {\n    font-size: 1.5rem;\n  }\n\n  .document-types-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n\n  .document-card {\n    flex-direction: column;\n    text-align: center;\n    padding: 1.5rem;\n  }\n\n  .info-section {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n\n  .loading-container, .error-container {\n    margin: 1rem;\n    padding: 3rem 1.5rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .page-header {\n    margin: 0.5rem;\n    padding: 1rem;\n  }\n\n  .content-wrapper {\n    margin: 0.5rem;\n    padding: 1rem;\n  }\n\n  .header-logo {\n    width: 50px;\n    height: 50px;\n  }\n\n  .page-title {\n    font-size: 1.25rem;\n  }\n\n  .document-card {\n    padding: 1rem;\n  }\n\n  .info-card, .help-card {\n    padding: 1rem;\n  }\n\n  .loading-container, .error-container {\n    margin: 0.5rem;\n    padding: 2rem 1rem;\n  }\n}\n</style>\n"], "mappings": ";AA0OA,OAAOA,sBAAqB,MAAO,mCAAmC;AACtE,OAAOC,YAAW,MAAO,oBAAoB;AAC7C,OAAOC,kBAAiB,MAAO,+BAA+B;AAE9D,eAAe;EACbC,IAAI,EAAE,oBAAoB;EAC1BC,UAAU,EAAE;IACVH;EACF,CAAC;EACDI,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,aAAa,EAAE,EAAE;MACjBC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,IAAI;MACX;MACAC,gBAAgB,EAAE,KAAK;MACvBC,gBAAgB,EAAE,KAAK;MACvBC,UAAU,EAAE,WAAW;MACvB;MACAC,QAAQ,EAAE,MAAM;MAChBC,SAAS,EAAE,MAAM;MACjBC,aAAa,EAAE,CAAC;MAChBC,eAAe,EAAE;IACnB,CAAC;EACH,CAAC;EACD,MAAMC,OAAOA,CAAA,EAAG;IACd,MAAM,IAAI,CAACC,YAAY,CAAC,CAAC;IACzB,MAAM,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAC9B,MAAM,IAAI,CAACC,aAAa,CAAC,CAAC;EAC5B,CAAC;EACDC,OAAO,EAAE;IACP,MAAMH,YAAYA,CAAA,EAAG;MACnB,IAAI;QACF,MAAMI,WAAU,GAAInB,kBAAkB,CAACoB,cAAc,CAAC,CAAC;QACvD,IAAID,WAAW,EAAE;UACf,IAAI,CAACT,QAAO,GAAIS,WAAW,CAACE,QAAO,IAAK,MAAM;UAC9C,IAAI,CAACV,SAAQ,GAAIQ,WAAW,CAACG,UAAS,IAAKH,WAAW,CAACE,QAAO,IAAK,MAAM;QAC3E;MACF,EAAE,OAAOf,KAAK,EAAE;QACdiB,OAAO,CAACjB,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;IACF,CAAC;IAED,MAAMW,aAAaA,CAAA,EAAG;MACpB,IAAI;QACF;QACA;QACA,IAAI,CAACL,aAAY,GAAI,CAAC;QACtB,IAAI,CAACC,eAAc,GAAI,CAAC;MAC1B,EAAE,OAAOP,KAAK,EAAE;QACdiB,OAAO,CAACjB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;IACF,CAAC;IAED,MAAMU,iBAAiBA,CAAA,EAAG;MACxB,IAAI;QACF,IAAI,CAACX,OAAM,GAAI,IAAI;QACnB,IAAI,CAACC,KAAI,GAAI,IAAI;QAEjB,MAAMkB,QAAO,GAAI,MAAM1B,sBAAsB,CAAC2B,gBAAgB,CAAC,CAAC;QAChE,IAAI,CAACrB,aAAY,GAAIoB,QAAQ,CAACrB,IAAG,IAAK,EAAE;MAE1C,EAAE,OAAOG,KAAK,EAAE;QACdiB,OAAO,CAACjB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,IAAI,CAACA,KAAI,GAAIA,KAAK,CAACkB,QAAQ,EAAErB,IAAI,EAAEuB,OAAM,IAAK,mCAAmC;MACnF,UAAU;QACR,IAAI,CAACrB,OAAM,GAAI,KAAK;MACtB;IACF,CAAC;IAEDsB,kBAAkBA,CAACC,YAAY,EAAE;MAC/B,IAAI,CAACA,YAAY,CAACC,SAAS,EAAE;;MAE7B;MACA,MAAMC,SAAQ,GAAI,IAAI,CAACC,uBAAuB,CAACH,YAAY,CAACI,SAAS,CAAC;MACtE,IAAIF,SAAS,EAAE;QACb,IAAI,CAACG,OAAO,CAACC,IAAI,CAAC;UAChBjC,IAAI,EAAE6B,SAAS;UACfK,MAAM,EAAE;YAAEC,cAAc,EAAER,YAAY,CAACS;UAAG;QAC5C,CAAC,CAAC;MACJ;IACF,CAAC;IAEDN,uBAAuBA,CAACO,QAAQ,EAAE;MAChC,MAAMC,MAAK,GAAI;QACb,oBAAoB,EAAE,0BAA0B;QAChD,QAAQ,EAAE;MACZ,CAAC;MACD,OAAOA,MAAM,CAACD,QAAQ,CAAC;IACzB,CAAC;IAEDE,eAAeA,CAACF,QAAQ,EAAE;MACxB,MAAMG,KAAI,GAAI;QACZ,oBAAoB,EAAE,oBAAoB;QAC1C,QAAQ,EAAE;MACZ,CAAC;MACD,OAAOA,KAAK,CAACH,QAAQ,KAAK,iBAAiB;IAC7C,CAAC;IAEDI,iBAAiBA,CAACJ,QAAQ,EAAE;MAC1B,MAAMK,KAAI,GAAI;QACZ,oBAAoB,EAAE,mBAAmB;QACzC,QAAQ,EAAE;MACZ,CAAC;MACD,OAAOA,KAAK,CAACL,QAAQ,KAAK,mBAAmB;IAC/C,CAAC;IAEDM,cAAcA,CAACC,MAAM,EAAE;MACrB,OAAOC,UAAU,CAACD,MAAM,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC;IACtC,CAAC;IAED;IACAC,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAACxC,gBAAe,GAAI,CAAC,IAAI,CAACA,gBAAgB;IAChD,CAAC;IAEDyC,wBAAwBA,CAAA,EAAG;MACzB,IAAI,CAAC1C,gBAAe,GAAI,CAAC,IAAI,CAACA,gBAAgB;IAChD,CAAC;IAED2C,gBAAgBA,CAACC,MAAM,EAAE;MACvB5B,OAAO,CAAC6B,GAAG,CAAC,cAAc,EAAED,MAAM,CAAC;MACnC,QAAQA,MAAM;QACZ,KAAK,SAAS;UACZ;UACA;QACF,KAAK,UAAU;UACb;UACA;QACF,KAAK,SAAS;UACZ;UACA;MACJ;IACF,CAAC;IAEDE,YAAYA,CAAA,EAAG;MACb,IAAI;QACFrD,kBAAkB,CAACsD,MAAM,CAAC,CAAC;QAC3B,IAAI,CAACrB,OAAO,CAACC,IAAI,CAAC;UAAEjC,IAAI,EAAE;QAAc,CAAC,CAAC;MAC5C,EAAE,OAAOK,KAAK,EAAE;QACdiB,OAAO,CAACjB,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACvC;IACF,CAAC;IAEDiD,WAAWA,CAACjD,KAAK,EAAE;MACjBiB,OAAO,CAACjB,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC;IAED;IACAkD,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAACC,KAAK,CAACC,eAAe,EAAEC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IACpE,CAAC;IAEDC,cAAcA,CAAA,EAAG;MACf,IAAI,CAAC5B,OAAO,CAACC,IAAI,CAAC;QAAEjC,IAAI,EAAE;MAAa,CAAC,CAAC;IAC3C,CAAC;IAED6D,WAAWA,CAAA,EAAG;MACZ;MACAvC,OAAO,CAAC6B,GAAG,CAAC,qBAAqB,CAAC;IACpC,CAAC;IAEDW,MAAMA,CAAA,EAAG;MACP,IAAI,CAAC9B,OAAO,CAACC,IAAI,CAAC;QAAEjC,IAAI,EAAE;MAAkB,CAAC,CAAC;IAChD,CAAC;IAED+D,QAAQA,CAAA,EAAG;MACT;MACAzC,OAAO,CAAC6B,GAAG,CAAC,iBAAiB,CAAC;IAChC,CAAC;IAEDa,cAAcA,CAAA,EAAG;MACf;MACA1C,OAAO,CAAC6B,GAAG,CAAC,uBAAuB,CAAC;IACtC;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}