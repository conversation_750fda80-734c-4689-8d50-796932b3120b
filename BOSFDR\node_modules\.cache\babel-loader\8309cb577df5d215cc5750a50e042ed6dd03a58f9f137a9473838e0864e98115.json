{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"client-dashboard\"\n};\nconst _hoisted_2 = {\n  id: \"main-content\",\n  class: \"main-content\"\n};\nexport function render(_ctx, _cache) {\n  const _component_ClientHeader = _resolveComponent(\"ClientHeader\");\n  const _component_HeroSection = _resolveComponent(\"HeroSection\");\n  const _component_QuickActionsSection = _resolveComponent(\"QuickActionsSection\");\n  const _component_DocumentServicesSection = _resolveComponent(\"DocumentServicesSection\");\n  const _component_InformationSection = _resolveComponent(\"InformationSection\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" Background \"), _cache[0] || (_cache[0] = _createElementVNode(\"div\", {\n    class: \"background-container\"\n  }, [_createElementVNode(\"div\", {\n    class: \"background-image\"\n  }), _createElementVNode(\"div\", {\n    class: \"background-overlay\"\n  })], -1 /* HOISTED */)), _createCommentVNode(\" Client Header with Navigation \"), _createVNode(_component_ClientHeader, {\n    userName: _ctx.userName,\n    userEmail: _ctx.userEmail,\n    userAvatar: _ctx.userAvatar,\n    showUserDropdown: _ctx.showUserDropdown,\n    showBreadcrumbs: true,\n    onUserDropdownToggle: _ctx.handleUserDropdownToggle,\n    onMenuAction: _ctx.handleMenuAction,\n    onLogout: _ctx.handleLogout,\n    onError: _ctx.handleError,\n    onSearch: _ctx.handleSearch\n  }, null, 8 /* PROPS */, [\"userName\", \"userEmail\", \"userAvatar\", \"showUserDropdown\", \"onUserDropdownToggle\", \"onMenuAction\", \"onLogout\", \"onError\", \"onSearch\"]), _createCommentVNode(\" Main Content \"), _createElementVNode(\"main\", _hoisted_2, [_createCommentVNode(\" Hero Section \"), _createVNode(_component_HeroSection, {\n    firstName: _ctx.firstName,\n    totalRequests: _ctx.totalRequests,\n    pendingRequests: _ctx.pendingRequests,\n    onStartNewRequest: _ctx.scrollToServices,\n    onViewRequests: _ctx.goToMyRequests\n  }, null, 8 /* PROPS */, [\"firstName\", \"totalRequests\", \"pendingRequests\", \"onStartNewRequest\", \"onViewRequests\"]), _createCommentVNode(\" Quick Actions Section \"), _createVNode(_component_QuickActionsSection, {\n    onStartNewRequest: _ctx.scrollToServices,\n    onViewRequests: _ctx.goToMyRequests,\n    onViewDocuments: _ctx.goToMyDocuments,\n    onGetHelp: _ctx.openHelp\n  }, null, 8 /* PROPS */, [\"onStartNewRequest\", \"onViewRequests\", \"onViewDocuments\", \"onGetHelp\"]), _createCommentVNode(\" Document Services Section \"), _createVNode(_component_DocumentServicesSection, {\n    ref: \"servicesSection\",\n    documentTypes: _ctx.documentTypes,\n    loading: _ctx.loading,\n    error: _ctx.error,\n    onSelectDocumentType: _ctx.selectDocumentType,\n    onRetry: _ctx.loadDocumentTypes\n  }, null, 8 /* PROPS */, [\"documentTypes\", \"loading\", \"error\", \"onSelectDocumentType\", \"onRetry\"]), _createCommentVNode(\" Information and Help Section \"), _createVNode(_component_InformationSection, {\n    onOpenHelp: _ctx.openHelp,\n    onContactSupport: _ctx.contactSupport\n  }, null, 8 /* PROPS */, [\"onOpenHelp\", \"onContactSupport\"])])]);\n}", "map": {"version": 3, "names": ["class", "id", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_createVNode", "_component_ClientHeader", "userName", "_ctx", "userEmail", "userAvatar", "showUserDropdown", "showBreadcrumbs", "onUserDropdownToggle", "handleUserDropdownToggle", "onMenuAction", "handleMenuAction", "onLogout", "handleLogout", "onError", "handleError", "onSearch", "handleSearch", "_hoisted_2", "_component_HeroSection", "firstName", "totalRequests", "pendingRequests", "onStartNewRequest", "scrollToServices", "onViewRequests", "goToMyRequests", "_component_QuickActionsSection", "onViewDocuments", "goToMyDocuments", "onGetHelp", "openHelp", "_component_DocumentServicesSection", "ref", "documentTypes", "loading", "error", "onSelectDocumentType", "selectDocumentType", "onRetry", "loadDocumentTypes", "_component_InformationSection", "onOpenHelp", "onContactSupport", "contactSupport"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\client\\NewDocumentRequest.vue"], "sourcesContent": ["<template>\n  <div class=\"client-dashboard\">\n    <!-- Background -->\n    <div class=\"background-container\">\n      <div class=\"background-image\"></div>\n      <div class=\"background-overlay\"></div>\n    </div>\n\n    <!-- Client Header with Navigation -->\n    <ClientHeader\n      :userName=\"userName\"\n      :userEmail=\"userEmail\"\n      :userAvatar=\"userAvatar\"\n      :showUserDropdown=\"showUserDropdown\"\n      :showBreadcrumbs=\"true\"\n      @user-dropdown-toggle=\"handleUserDropdownToggle\"\n      @menu-action=\"handleMenuAction\"\n      @logout=\"handleLogout\"\n      @error=\"handleError\"\n      @search=\"handleSearch\"\n    />\n\n    <!-- Main Content -->\n    <main id=\"main-content\" class=\"main-content\">\n      <!-- Hero Section -->\n      <HeroSection\n        :firstName=\"firstName\"\n        :totalRequests=\"totalRequests\"\n        :pendingRequests=\"pendingRequests\"\n        @start-new-request=\"scrollToServices\"\n        @view-requests=\"goToMyRequests\"\n      />\n\n      <!-- Quick Actions Section -->\n      <QuickActionsSection\n        @start-new-request=\"scrollToServices\"\n        @view-requests=\"goToMyRequests\"\n        @view-documents=\"goToMyDocuments\"\n        @get-help=\"openHelp\"\n      />\n\n      <!-- Document Services Section -->\n      <DocumentServicesSection\n        ref=\"servicesSection\"\n        :documentTypes=\"documentTypes\"\n        :loading=\"loading\"\n        :error=\"error\"\n        @select-document-type=\"selectDocumentType\"\n        @retry=\"loadDocumentTypes\"\n      />\n\n      <!-- Information and Help Section -->\n      <InformationSection\n        @open-help=\"openHelp\"\n        @contact-support=\"contactSupport\"\n      />\n    </main>\n  </div>\n</template>\n\n<script>\nimport { ref, onMounted, onBeforeUnmount, nextTick } from 'vue';\nimport documentRequestService from '@/services/documentRequestService';\nimport ClientHeader from './ClientHeader.vue';\nimport HeroSection from './sections/HeroSection.vue';\nimport QuickActionsSection from './sections/QuickActionsSection.vue';\nimport DocumentServicesSection from './sections/DocumentServicesSection.vue';\nimport InformationSection from './sections/InformationSection.vue';\nimport unifiedAuthService from '@/services/unifiedAuthService';\n\nexport default {\n  name: 'NewDocumentRequest',\n  components: {\n    ClientHeader,\n    HeroSection,\n    QuickActionsSection,\n    DocumentServicesSection,\n    InformationSection\n  },\n  setup() {\n    // Reactive state\n    const documentTypes = ref([]);\n    const loading = ref(true);\n    const error = ref(null);\n\n    // Header state\n    const showUserDropdown = ref(false);\n\n    // User data\n    const userName = ref('User');\n    const userEmail = ref('<EMAIL>');\n    const userAvatar = ref(null);\n    const firstName = ref('User');\n    const totalRequests = ref(0);\n    const pendingRequests = ref(0);\n\n    // Template refs\n    const servicesSection = ref(null);\n    // Methods\n    const loadUserData = async () => {\n      try {\n        const currentUser = unifiedAuthService.getCurrentUser();\n        if (currentUser) {\n          userName.value = currentUser.username || 'User';\n          userEmail.value = currentUser.email || '<EMAIL>';\n          firstName.value = currentUser.first_name || currentUser.username || 'User';\n          userAvatar.value = currentUser.avatar || null;\n        }\n      } catch (error) {\n        console.error('Error loading user data:', error);\n      }\n    };\n\n    const loadUserStats = async () => {\n      try {\n        // TODO: Implement API call to get user statistics\n        // For now, using placeholder data\n        totalRequests.value = 5;\n        pendingRequests.value = 2;\n      } catch (error) {\n        console.error('Error loading user stats:', error);\n      }\n    };\n\n    const loadDocumentTypes = async () => {\n      try {\n        loading.value = true;\n        error.value = null;\n\n        const response = await documentRequestService.getDocumentTypes();\n        documentTypes.value = response.data || [];\n\n      } catch (err) {\n        console.error('Error loading document types:', err);\n        error.value = err.response?.data?.message || 'Failed to load available services';\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    const selectDocumentType = (documentType) => {\n      if (!documentType.is_active) return;\n\n      // Navigate to specific document request form\n      const routeName = getRouteForDocumentType(documentType.type_name);\n      if (routeName) {\n        // Note: router will be available via getCurrentInstance() in real implementation\n        console.log('Navigate to:', routeName, 'with ID:', documentType.id);\n      }\n    };\n\n    const getRouteForDocumentType = (typeName) => {\n      const routes = {\n        'Barangay Clearance': 'BarangayClearanceRequest',\n        'Cedula': 'CedulaRequest'\n      };\n      return routes[typeName];\n    };\n\n    const selectDocumentType = (documentType) => {\n      if (!documentType.is_active) return;\n\n      const routeName = getRouteForDocumentType(documentType.type_name);\n      if (routeName) {\n        console.log('Navigate to:', routeName, 'with ID:', documentType.id);\n      }\n    };\n\n    const getRouteForDocumentType = (typeName) => {\n      const routes = {\n        'Barangay Clearance': 'BarangayClearanceRequest',\n        'Cedula': 'CedulaRequest'\n      };\n      return routes[typeName];\n    };\n\n    // Header event handlers\n    const handleUserDropdownToggle = () => {\n      showUserDropdown.value = !showUserDropdown.value;\n    };\n\n    const handleMenuAction = (action) => {\n      console.log('Menu action:', action);\n    };\n\n    const handleLogout = () => {\n      try {\n        unifiedAuthService.logout();\n        console.log('Logout and navigate to WelcomePage');\n      } catch (err) {\n        console.error('Logout error:', err);\n      }\n    };\n\n    const handleError = (err) => {\n      console.error('Header error:', err);\n    };\n\n    const handleSearch = (query) => {\n      console.log('Search query:', query);\n    };\n\n    // Navigation methods\n    const scrollToServices = () => {\n      servicesSection.value?.$el?.scrollIntoView({ behavior: 'smooth' });\n    };\n\n    const goToMyRequests = () => {\n      console.log('Navigate to MyRequests');\n    };\n\n    const goToMyDocuments = () => {\n      console.log('Navigate to MyDocuments');\n    };\n\n    const openHelp = () => {\n      console.log('Opening help...');\n    };\n\n    const contactSupport = () => {\n      console.log('Contacting support...');\n    };\n\n    // Lifecycle\n    onMounted(async () => {\n      await loadUserData();\n      await loadDocumentTypes();\n      await loadUserStats();\n    });\n\n    // Return reactive state and methods for template\n    return {\n      // State\n      documentTypes,\n      loading,\n      error,\n      showUserDropdown,\n      userName,\n      userEmail,\n      userAvatar,\n      firstName,\n      totalRequests,\n      pendingRequests,\n      servicesSection,\n\n      // Methods\n      selectDocumentType,\n      loadDocumentTypes,\n      handleUserDropdownToggle,\n      handleMenuAction,\n      handleLogout,\n      handleError,\n      handleSearch,\n      scrollToServices,\n      goToMyRequests,\n      goToMyDocuments,\n      openHelp,\n      contactSupport\n    };\n  }\n};\n</script>\n\n<style scoped>\n/* Import design tokens from ClientHeader */\n@import './css/clientHeader.css';\n\n/* Client Dashboard Layout */\n.client-dashboard {\n  min-height: 100vh;\n  background: var(--color-bg-primary);\n  position: relative;\n/* Background Setup */\n.background-container {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: -2;\n}\n\n.background-image {\n  width: 100%;\n  height: 100%;\n  background-image: url('@/assets/bula-request-background-pic.png');\n  background-size: cover;\n  background-position: center;\n  background-repeat: no-repeat;\n  background-attachment: fixed;\n  opacity: 0.03;\n}\n\n.background-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    135deg,\n    rgba(0, 94, 162, 0.02) 0%,\n    rgba(35, 120, 195, 0.03) 100%\n  );\n  z-index: -1;\n}\n\n/* Main Content */\n.main-content {\n  margin-top: 140px; /* Account for fixed header */\n  padding-bottom: var(--spacing-16);\n  position: relative;\n  z-index: 1;\n}\n\n/* Modern Button Styles */\n.btn {\n  display: inline-flex;\n  align-items: center;\n  gap: var(--spacing-2);\n  padding: var(--spacing-3) var(--spacing-6);\n  font-family: var(--font-family-sans);\n  font-size: 0.875rem;\n  font-weight: 600;\n  line-height: 1.2;\n  text-decoration: none;\n  border: 2px solid transparent;\n  border-radius: var(--border-radius-md);\n  cursor: pointer;\n  transition: all var(--transition-fast);\n  white-space: nowrap;\n}\n\n.btn:focus {\n  outline: 2px solid var(--gov-yellow);\n  outline-offset: 2px;\n}\n\n.btn-primary {\n  background-color: var(--gov-blue);\n  color: var(--text-white);\n  border-color: var(--gov-blue);\n}\n\n.btn-primary:hover {\n  background-color: var(--gov-blue-dark);\n  border-color: var(--gov-blue-dark);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-3);\n}\n\n.btn-secondary {\n  background-color: var(--bg-white);\n  color: var(--gov-blue);\n  border-color: var(--gov-blue);\n}\n\n.btn-secondary:hover {\n  background-color: var(--gov-blue);\n  color: var(--text-white);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-3);\n}\n\n.btn-outline {\n  background-color: transparent;\n  color: var(--gov-blue);\n  border-color: var(--gov-blue);\n}\n\n.btn-outline:hover {\n  background-color: var(--gov-blue);\n  color: var(--text-white);\n}\n\n/* Hero Section */\n.hero-section {\n  padding: var(--spacing-8) 0;\n  position: relative;\n  z-index: 1;\n  background: var(--bg-white);\n  margin: var(--spacing-4);\n  border-radius: var(--border-radius-xl);\n  box-shadow: var(--shadow-2);\n}\n\n.hero-content {\n  display: grid;\n  grid-template-columns: 1fr auto;\n  gap: var(--spacing-8);\n  align-items: start;\n  padding: var(--spacing-8);\n}\n\n.hero-text {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-4);\n}\n\n.hero-title {\n  font-size: clamp(1.75rem, 4vw, 2.5rem);\n  font-weight: 700;\n  color: var(--gov-blue);\n  margin: 0;\n  line-height: 1.2;\n  font-family: var(--font-family-serif);\n}\n\n.hero-subtitle {\n  font-size: 1.125rem;\n  color: var(--text-secondary);\n  margin: 0;\n  line-height: 1.5;\n  max-width: 600px;\n}\n\n.hero-actions {\n  display: flex;\n  gap: var(--spacing-3);\n  flex-wrap: wrap;\n  margin-top: var(--spacing-2);\n}\n\n.hero-stats {\n  display: flex;\n  justify-content: center;\n  min-width: 280px;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: var(--spacing-3);\n  width: 100%;\n}\n\n.stat-card {\n  background: linear-gradient(135deg, var(--gov-blue), var(--gov-blue-light));\n  border-radius: var(--border-radius-lg);\n  padding: var(--spacing-4);\n  color: var(--text-white);\n  text-align: center;\n  flex: 1;\n  box-shadow: var(--shadow-2);\n  transition: all var(--transition-fast);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  min-height: 120px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n}\n\n.stat-card:hover {\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-3);\n}\n\n.stat-icon {\n  font-size: 1.5rem;\n  color: var(--gov-yellow);\n  margin-bottom: var(--spacing-2);\n  display: block;\n}\n\n.stat-number {\n  font-size: 1.75rem;\n  font-weight: 700;\n  margin-bottom: var(--spacing-1);\n  color: var(--gov-yellow);\n  font-family: var(--font-family-serif);\n  line-height: 1;\n}\n\n.stat-label {\n  font-size: 0.75rem;\n  opacity: 0.9;\n  font-weight: 500;\n  line-height: 1.2;\n}\n\n/* Quick Actions Section */\n.quick-actions-section {\n  padding: var(--spacing-8) 0;\n  position: relative;\n  z-index: 1;\n  background: var(--bg-gray-5);\n  margin: var(--spacing-4);\n  border-radius: var(--border-radius-xl);\n}\n\n.section-header {\n  text-align: center;\n  margin-bottom: var(--spacing-8);\n  padding: 0 var(--spacing-4);\n}\n\n.section-title {\n  font-size: clamp(1.75rem, 4vw, 2.25rem);\n  font-weight: 700;\n  color: var(--gov-blue);\n  margin-bottom: var(--spacing-3);\n  font-family: var(--font-family-serif);\n}\n\n.section-description {\n  font-size: 1rem;\n  color: var(--text-secondary);\n  margin: 0;\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n.quick-actions-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));\n  gap: var(--spacing-4);\n  padding: 0 var(--spacing-4);\n}\n\n.action-card {\n  background: var(--bg-white);\n  border-radius: var(--border-radius-lg);\n  padding: var(--spacing-6);\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-4);\n  cursor: pointer;\n  transition: all var(--transition-fast);\n  border: 2px solid var(--bg-gray-20);\n  box-shadow: var(--shadow-1);\n}\n\n.action-card:hover,\n.action-card:focus {\n  transform: translateY(-2px);\n  box-shadow: var(--shadow-3);\n  border-color: var(--gov-blue);\n  outline: none;\n}\n\n.action-card.primary {\n  background: linear-gradient(135deg, var(--gov-blue), var(--gov-blue-light));\n  color: var(--text-white);\n  border-color: var(--gov-blue);\n}\n\n.action-card.primary:hover,\n.action-card.primary:focus {\n  border-color: var(--gov-yellow);\n  box-shadow: var(--shadow-4);\n}\n\n.action-icon {\n  width: 4rem;\n  height: 4rem;\n  background: linear-gradient(135deg, #fbbf24, #f59e0b);\n  border-radius: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.5rem;\n  color: #1e3a8a;\n  flex-shrink: 0;\n}\n\n.action-card.primary .action-icon {\n  background: rgba(251, 191, 36, 0.2);\n  color: #fbbf24;\n}\n\n.action-content {\n  flex: 1;\n}\n\n.action-content h3 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  color: inherit;\n}\n\n.action-content p {\n  margin: 0;\n  opacity: 0.8;\n  line-height: 1.5;\n}\n\n.action-arrow {\n  color: #6b7280;\n  font-size: 1.25rem;\n}\n\n.action-card.primary .action-arrow {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n/* Services Section */\n.services-section {\n  padding: 2rem 0;\n  position: relative;\n  z-index: 1;\n}\n\n.services-section .section-title {\n  color: white;\n}\n\n.services-section .section-description {\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.loading-container, .error-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n  text-align: center;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: 1rem;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.loading-spinner i {\n  font-size: 3rem;\n  color: #1e3a8a;\n  margin-bottom: 1rem;\n}\n\n.error-content {\n  max-width: 400px;\n}\n\n.error-content i {\n  font-size: 3rem;\n  color: #dc2626;\n  margin-bottom: 1rem;\n}\n\n.retry-btn {\n  background: linear-gradient(135deg, #1e3a8a, #1e40af);\n  color: white;\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 0.75rem;\n  cursor: pointer;\n  margin-top: 1rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);\n}\n\n.retry-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.4);\n}\n\n.document-types-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n  gap: 1.5rem;\n}\n\n.document-card {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border: 2px solid rgba(30, 58, 138, 0.2);\n  border-radius: 1rem;\n  padding: 2rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n}\n\n.document-card:hover:not(.disabled) {\n  border-color: #1e3a8a;\n  transform: translateY(-5px);\n  box-shadow: 0 12px 30px rgba(30, 58, 138, 0.2);\n  background: rgba(255, 255, 255, 1);\n}\n\n.document-card.disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.document-icon {\n  flex-shrink: 0;\n  width: 4rem;\n  height: 4rem;\n  background: linear-gradient(135deg, #1e3a8a, #1e40af);\n  border-radius: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 1.5rem;\n  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);\n}\n\n.document-content {\n  flex: 1;\n}\n\n.document-title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1e3a8a;\n  margin-bottom: 0.5rem;\n}\n\n.document-description {\n  color: #6b7280;\n  margin-bottom: 1rem;\n  line-height: 1.5;\n}\n\n.document-details {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.fee-info {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.fee-label {\n  color: #9ca3af;\n  font-size: 0.9rem;\n}\n\n.fee-amount {\n  font-weight: 600;\n  color: #059669;\n  font-size: 1.1rem;\n}\n\n.processing-time {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #9ca3af;\n  font-size: 0.9rem;\n}\n\n.document-action {\n  flex-shrink: 0;\n  display: flex;\n  align-items: center;\n}\n\n.document-action i {\n  color: #d1d5db;\n  font-size: 1.25rem;\n}\n\n.status-badge.unavailable {\n  background: #fecaca;\n  color: #dc2626;\n  padding: 0.25rem 0.75rem;\n  border-radius: 1rem;\n  font-size: 0.8rem;\n  font-weight: 500;\n}\n\n/* Info Section */\n.info-section {\n  padding: 2rem 0;\n  position: relative;\n  z-index: 1;\n}\n\n.info-grid {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 2rem;\n}\n\n.info-card, .help-card {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(30, 58, 138, 0.2);\n  border-radius: 1rem;\n  padding: 2rem;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n}\n\n.info-card:hover, .help-card:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 8px 30px rgba(30, 58, 138, 0.15);\n  background: rgba(255, 255, 255, 1);\n  border-color: #1e3a8a;\n}\n\n.info-header, .help-header {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  margin-bottom: 1.5rem;\n}\n\n.info-header h3, .help-header h3 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1e3a8a;\n  margin: 0;\n}\n\n.info-header i {\n  color: #1e3a8a;\n  font-size: 1.25rem;\n}\n\n.help-header i {\n  color: #059669;\n  font-size: 1.25rem;\n}\n\n.info-list {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.info-list li {\n  display: flex;\n  align-items: flex-start;\n  gap: 0.75rem;\n  margin-bottom: 1rem;\n  color: #6b7280;\n  line-height: 1.6;\n}\n\n.info-list i {\n  color: #059669;\n  margin-top: 0.125rem;\n  flex-shrink: 0;\n}\n\n.help-content p {\n  color: #6b7280;\n  margin-bottom: 1.5rem;\n  line-height: 1.6;\n}\n\n.help-actions {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.help-btn, .contact-btn {\n  background: rgba(255, 255, 255, 0.9);\n  backdrop-filter: blur(5px);\n  border: 2px solid rgba(30, 58, 138, 0.3);\n  padding: 0.875rem 1.25rem;\n  border-radius: 0.75rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  color: #6b7280;\n  font-weight: 500;\n  font-size: 0.95rem;\n}\n\n.help-btn:hover {\n  border-color: #1e3a8a;\n  color: #1e3a8a;\n  background: rgba(30, 58, 138, 0.1);\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.2);\n}\n\n.contact-btn:hover {\n  border-color: #059669;\n  color: #059669;\n  background: rgba(5, 150, 105, 0.1);\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(5, 150, 105, 0.2);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .container {\n    padding: 0 0.75rem;\n  }\n\n  .welcome-header {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n    padding: 1.5rem;\n  }\n\n  .welcome-stats {\n    justify-content: center;\n  }\n\n  .stat-card {\n    min-width: 120px;\n  }\n\n  .quick-actions-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n\n  .action-card {\n    padding: 1.5rem;\n  }\n\n  .document-types-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n\n  .document-card {\n    flex-direction: column;\n    text-align: center;\n    padding: 1.5rem;\n  }\n\n  .info-grid {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n\n  .info-card, .help-card {\n    padding: 1.5rem;\n  }\n\n  .loading-container, .error-container {\n    padding: 3rem 1.5rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .container {\n    padding: 0 0.5rem;\n  }\n\n  .welcome-section,\n  .quick-actions-section,\n  .services-section,\n  .info-section {\n    padding: 1.5rem 0;\n  }\n\n  .welcome-header {\n    padding: 1rem;\n  }\n\n  .stat-card {\n    padding: 1rem;\n  }\n\n  .stat-number {\n    font-size: 1.5rem;\n  }\n\n  .action-card {\n    padding: 1rem;\n    flex-direction: column;\n    text-align: center;\n    gap: 1rem;\n  }\n\n  .action-icon {\n    width: 3rem;\n    height: 3rem;\n  }\n\n  .document-card {\n    padding: 1rem;\n  }\n\n  .info-card, .help-card {\n    padding: 1rem;\n  }\n\n  .loading-container, .error-container {\n    padding: 2rem 1rem;\n  }\n}\n\n/* Animations */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.welcome-section,\n.quick-actions-section,\n.services-section,\n.info-section {\n  animation: fadeInUp 0.8s ease-out;\n}\n\n.quick-actions-section {\n  animation-delay: 0.2s;\n}\n\n.services-section {\n  animation-delay: 0.4s;\n}\n\n.info-section {\n  animation-delay: 0.6s;\n}\n\n/* Accessibility */\n@media (prefers-reduced-motion: reduce) {\n  * {\n    animation: none !important;\n    transition: none !important;\n  }\n\n  .action-card:hover,\n  .document-card:hover,\n  .info-card:hover,\n  .help-card:hover {\n    transform: none;\n  }\n}\n\n/* Focus styles */\n.action-card:focus,\n.document-card:focus,\n.help-btn:focus,\n.contact-btn:focus,\n.retry-btn:focus {\n  outline: 3px solid #fbbf24;\n  outline-offset: 2px;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAkB;;EAsBrBC,EAAE,EAAC,cAAc;EAACD,KAAK,EAAC;;;;;;;;uBAtBhCE,mBAAA,CAwDM,OAxDNC,UAwDM,GAvDJC,mBAAA,gBAAmB,E,0BACnBC,mBAAA,CAGM;IAHDL,KAAK,EAAC;EAAsB,IAC/BK,mBAAA,CAAoC;IAA/BL,KAAK,EAAC;EAAkB,IAC7BK,mBAAA,CAAsC;IAAjCL,KAAK,EAAC;EAAoB,G,sBAGjCI,mBAAA,mCAAsC,EACtCE,YAAA,CAWEC,uBAAA;IAVCC,QAAQ,EAAEC,IAAA,CAAAD,QAAQ;IAClBE,SAAS,EAAED,IAAA,CAAAC,SAAS;IACpBC,UAAU,EAAEF,IAAA,CAAAE,UAAU;IACtBC,gBAAgB,EAAEH,IAAA,CAAAG,gBAAgB;IAClCC,eAAe,EAAE,IAAI;IACrBC,oBAAoB,EAAEL,IAAA,CAAAM,wBAAwB;IAC9CC,YAAW,EAAEP,IAAA,CAAAQ,gBAAgB;IAC7BC,QAAM,EAAET,IAAA,CAAAU,YAAY;IACpBC,OAAK,EAAEX,IAAA,CAAAY,WAAW;IAClBC,QAAM,EAAEb,IAAA,CAAAc;mKAGXnB,mBAAA,kBAAqB,EACrBC,mBAAA,CAiCO,QAjCPmB,UAiCO,GAhCLpB,mBAAA,kBAAqB,EACrBE,YAAA,CAMEmB,sBAAA;IALCC,SAAS,EAAEjB,IAAA,CAAAiB,SAAS;IACpBC,aAAa,EAAElB,IAAA,CAAAkB,aAAa;IAC5BC,eAAe,EAAEnB,IAAA,CAAAmB,eAAe;IAChCC,iBAAiB,EAAEpB,IAAA,CAAAqB,gBAAgB;IACnCC,cAAa,EAAEtB,IAAA,CAAAuB;qHAGlB5B,mBAAA,2BAA8B,EAC9BE,YAAA,CAKE2B,8BAAA;IAJCJ,iBAAiB,EAAEpB,IAAA,CAAAqB,gBAAgB;IACnCC,cAAa,EAAEtB,IAAA,CAAAuB,cAAc;IAC7BE,eAAc,EAAEzB,IAAA,CAAA0B,eAAe;IAC/BC,SAAQ,EAAE3B,IAAA,CAAA4B;oGAGbjC,mBAAA,+BAAkC,EAClCE,YAAA,CAOEgC,kCAAA;IANAC,GAAG,EAAC,iBAAiB;IACpBC,aAAa,EAAE/B,IAAA,CAAA+B,aAAa;IAC5BC,OAAO,EAAEhC,IAAA,CAAAgC,OAAO;IAChBC,KAAK,EAAEjC,IAAA,CAAAiC,KAAK;IACZC,oBAAoB,EAAElC,IAAA,CAAAmC,kBAAkB;IACxCC,OAAK,EAAEpC,IAAA,CAAAqC;qGAGV1C,mBAAA,kCAAqC,EACrCE,YAAA,CAGEyC,6BAAA;IAFCC,UAAS,EAAEvC,IAAA,CAAA4B,QAAQ;IACnBY,gBAAe,EAAExC,IAAA,CAAAyC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}