{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode, Fragment as _Fragment, renderList as _renderList, normalizeClass as _normalizeClass, createStaticVNode as _createStaticVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"client-dashboard\"\n};\nconst _hoisted_2 = {\n  class: \"welcome-section\"\n};\nconst _hoisted_3 = {\n  class: \"container\"\n};\nconst _hoisted_4 = {\n  class: \"welcome-header\"\n};\nconst _hoisted_5 = {\n  class: \"welcome-text\"\n};\nconst _hoisted_6 = {\n  class: \"welcome-title\"\n};\nconst _hoisted_7 = {\n  class: \"welcome-stats\"\n};\nconst _hoisted_8 = {\n  class: \"stat-card\"\n};\nconst _hoisted_9 = {\n  class: \"stat-content\"\n};\nconst _hoisted_10 = {\n  class: \"stat-number\"\n};\nconst _hoisted_11 = {\n  class: \"stat-card\"\n};\nconst _hoisted_12 = {\n  class: \"stat-content\"\n};\nconst _hoisted_13 = {\n  class: \"stat-number\"\n};\nconst _hoisted_14 = {\n  class: \"quick-actions-section\"\n};\nconst _hoisted_15 = {\n  class: \"container\"\n};\nconst _hoisted_16 = {\n  class: \"quick-actions-grid\"\n};\nconst _hoisted_17 = {\n  class: \"services-section\",\n  ref: \"servicesSection\"\n};\nconst _hoisted_18 = {\n  class: \"container\"\n};\nconst _hoisted_19 = {\n  key: 0,\n  class: \"loading-container\"\n};\nconst _hoisted_20 = {\n  class: \"error-container\"\n};\nconst _hoisted_21 = {\n  class: \"error-content\"\n};\nconst _hoisted_22 = {\n  class: \"document-types-grid\"\n};\nconst _hoisted_23 = [\"onClick\"];\nconst _hoisted_24 = {\n  class: \"document-icon\"\n};\nconst _hoisted_25 = {\n  class: \"document-content\"\n};\nconst _hoisted_26 = {\n  class: \"document-title\"\n};\nconst _hoisted_27 = {\n  class: \"document-description\"\n};\nconst _hoisted_28 = {\n  class: \"document-details\"\n};\nconst _hoisted_29 = {\n  class: \"fee-info\"\n};\nconst _hoisted_30 = {\n  class: \"fee-amount\"\n};\nconst _hoisted_31 = {\n  class: \"processing-time\"\n};\nconst _hoisted_32 = {\n  class: \"document-action\"\n};\nconst _hoisted_33 = {\n  key: 0,\n  class: \"status-badge unavailable\"\n};\nconst _hoisted_34 = {\n  key: 1,\n  class: \"fas fa-chevron-right\"\n};\nconst _hoisted_35 = {\n  class: \"info-section\"\n};\nconst _hoisted_36 = {\n  class: \"container\"\n};\nconst _hoisted_37 = {\n  class: \"info-grid\"\n};\nconst _hoisted_38 = {\n  class: \"help-card\"\n};\nconst _hoisted_39 = {\n  class: \"help-content\"\n};\nconst _hoisted_40 = {\n  class: \"help-actions\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_ClientHeader = _resolveComponent(\"ClientHeader\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" Background \"), _cache[27] || (_cache[27] = _createElementVNode(\"div\", {\n    class: \"background-container\"\n  }, [_createElementVNode(\"div\", {\n    class: \"background-image\"\n  }), _createElementVNode(\"div\", {\n    class: \"background-overlay\"\n  })], -1 /* HOISTED */)), _createCommentVNode(\" Client Header with Navigation \"), _createVNode(_component_ClientHeader, {\n    userName: $data.userName,\n    showUserDropdown: $data.showUserDropdown,\n    sidebarCollapsed: $data.sidebarCollapsed,\n    activeMenu: $data.activeMenu,\n    onSidebarToggle: $options.handleSidebarToggle,\n    onUserDropdownToggle: $options.handleUserDropdownToggle,\n    onMenuAction: $options.handleMenuAction,\n    onLogout: $options.handleLogout,\n    onError: $options.handleError\n  }, null, 8 /* PROPS */, [\"userName\", \"showUserDropdown\", \"sidebarCollapsed\", \"activeMenu\", \"onSidebarToggle\", \"onUserDropdownToggle\", \"onMenuAction\", \"onLogout\", \"onError\"]), _createCommentVNode(\" Main Content \"), _createElementVNode(\"main\", {\n    class: _normalizeClass([\"main-content\", {\n      'sidebar-collapsed': $data.sidebarCollapsed\n    }])\n  }, [_createCommentVNode(\" Welcome Section \"), _createElementVNode(\"section\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"h1\", _hoisted_6, \"Welcome back, \" + _toDisplayString($data.firstName) + \"!\", 1 /* TEXT */), _cache[6] || (_cache[6] = _createElementVNode(\"p\", {\n    class: \"welcome-subtitle\"\n  }, \"Request official documents quickly and securely through our digital platform.\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_cache[8] || (_cache[8] = _createElementVNode(\"div\", {\n    class: \"stat-icon\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-file-alt\"\n  })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, _toDisplayString($data.totalRequests), 1 /* TEXT */), _cache[7] || (_cache[7] = _createElementVNode(\"div\", {\n    class: \"stat-label\"\n  }, \"Total Requests\", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_11, [_cache[10] || (_cache[10] = _createElementVNode(\"div\", {\n    class: \"stat-icon\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-clock\"\n  })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, _toDisplayString($data.pendingRequests), 1 /* TEXT */), _cache[9] || (_cache[9] = _createElementVNode(\"div\", {\n    class: \"stat-label\"\n  }, \"Pending\", -1 /* HOISTED */))])])])])])]), _createCommentVNode(\" Quick Actions Section \"), _createElementVNode(\"section\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [_cache[14] || (_cache[14] = _createElementVNode(\"div\", {\n    class: \"section-header\"\n  }, [_createElementVNode(\"h2\", {\n    class: \"section-title\"\n  }, \"Quick Actions\"), _createElementVNode(\"p\", {\n    class: \"section-description\"\n  }, \"Start a new document request or manage existing ones\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"div\", {\n    class: \"action-card primary\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.scrollToServices && $options.scrollToServices(...args))\n  }, _cache[11] || (_cache[11] = [_createStaticVNode(\"<div class=\\\"action-icon\\\" data-v-b2da9790><i class=\\\"fas fa-plus-circle\\\" data-v-b2da9790></i></div><div class=\\\"action-content\\\" data-v-b2da9790><h3 data-v-b2da9790>New Request</h3><p data-v-b2da9790>Start a new document request</p></div><div class=\\\"action-arrow\\\" data-v-b2da9790><i class=\\\"fas fa-arrow-right\\\" data-v-b2da9790></i></div>\", 3)])), _createElementVNode(\"div\", {\n    class: \"action-card\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.goToMyRequests && $options.goToMyRequests(...args))\n  }, _cache[12] || (_cache[12] = [_createStaticVNode(\"<div class=\\\"action-icon\\\" data-v-b2da9790><i class=\\\"fas fa-list-alt\\\" data-v-b2da9790></i></div><div class=\\\"action-content\\\" data-v-b2da9790><h3 data-v-b2da9790>My Requests</h3><p data-v-b2da9790>View and track your requests</p></div><div class=\\\"action-arrow\\\" data-v-b2da9790><i class=\\\"fas fa-arrow-right\\\" data-v-b2da9790></i></div>\", 3)])), _createElementVNode(\"div\", {\n    class: \"action-card\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.goToProfile && $options.goToProfile(...args))\n  }, _cache[13] || (_cache[13] = [_createStaticVNode(\"<div class=\\\"action-icon\\\" data-v-b2da9790><i class=\\\"fas fa-user-edit\\\" data-v-b2da9790></i></div><div class=\\\"action-content\\\" data-v-b2da9790><h3 data-v-b2da9790>Update Profile</h3><p data-v-b2da9790>Keep your information current</p></div><div class=\\\"action-arrow\\\" data-v-b2da9790><i class=\\\"fas fa-arrow-right\\\" data-v-b2da9790></i></div>\", 3)]))])])]), _createCommentVNode(\" Document Services Section \"), _createElementVNode(\"section\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_cache[21] || (_cache[21] = _createElementVNode(\"div\", {\n    class: \"section-header\"\n  }, [_createElementVNode(\"h2\", {\n    class: \"section-title\"\n  }, \"Available Document Services\"), _createElementVNode(\"p\", {\n    class: \"section-description\"\n  }, \"Choose the type of document you want to request\")], -1 /* HOISTED */)), _createCommentVNode(\" Loading State \"), $data.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_19, _cache[15] || (_cache[15] = [_createElementVNode(\"div\", {\n    class: \"loading-spinner\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-spinner fa-spin\"\n  })], -1 /* HOISTED */), _createElementVNode(\"p\", null, \"Loading available services...\", -1 /* HOISTED */)]))) : $data.error ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" Error State \"), _createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, [_cache[17] || (_cache[17] = _createElementVNode(\"i\", {\n    class: \"fas fa-exclamation-triangle\"\n  }, null, -1 /* HOISTED */)), _cache[18] || (_cache[18] = _createElementVNode(\"h3\", null, \"Unable to Load Services\", -1 /* HOISTED */)), _createElementVNode(\"p\", null, _toDisplayString($data.error), 1 /* TEXT */), _createElementVNode(\"button\", {\n    class: \"retry-btn\",\n    onClick: _cache[3] || (_cache[3] = (...args) => $options.loadDocumentTypes && $options.loadDocumentTypes(...args))\n  }, _cache[16] || (_cache[16] = [_createElementVNode(\"i\", {\n    class: \"fas fa-redo\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Try Again \")]))])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 2\n  }, [_createCommentVNode(\" Document Types Grid \"), _createElementVNode(\"div\", _hoisted_22, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.documentTypes, documentType => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: documentType.id,\n      class: _normalizeClass([\"document-card\", {\n        'disabled': !documentType.is_active\n      }]),\n      onClick: $event => $options.selectDocumentType(documentType)\n    }, [_createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"i\", {\n      class: _normalizeClass($options.getDocumentIcon(documentType.type_name))\n    }, null, 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"h3\", _hoisted_26, _toDisplayString(documentType.type_name), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_27, _toDisplayString(documentType.description), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"div\", _hoisted_29, [_cache[19] || (_cache[19] = _createElementVNode(\"span\", {\n      class: \"fee-label\"\n    }, \"Base Fee:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_30, \"₱\" + _toDisplayString($options.formatCurrency(documentType.base_fee)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_31, [_cache[20] || (_cache[20] = _createElementVNode(\"i\", {\n      class: \"fas fa-clock\"\n    }, null, -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($options.getProcessingTime(documentType.type_name)), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_32, [!documentType.is_active ? (_openBlock(), _createElementBlock(\"span\", _hoisted_33, \" Unavailable \")) : (_openBlock(), _createElementBlock(\"i\", _hoisted_34))])], 10 /* CLASS, PROPS */, _hoisted_23);\n  }), 128 /* KEYED_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))])], 512 /* NEED_PATCH */), _createCommentVNode(\" Information Section \"), _createElementVNode(\"section\", _hoisted_35, [_createElementVNode(\"div\", _hoisted_36, [_createElementVNode(\"div\", _hoisted_37, [_cache[26] || (_cache[26] = _createStaticVNode(\"<div class=\\\"info-card\\\" data-v-b2da9790><div class=\\\"info-header\\\" data-v-b2da9790><i class=\\\"fas fa-info-circle\\\" data-v-b2da9790></i><h3 data-v-b2da9790>Important Information</h3></div><div class=\\\"info-content\\\" data-v-b2da9790><ul class=\\\"info-list\\\" data-v-b2da9790><li data-v-b2da9790><i class=\\\"fas fa-check\\\" data-v-b2da9790></i> Ensure your profile information is complete and accurate </li><li data-v-b2da9790><i class=\\\"fas fa-check\\\" data-v-b2da9790></i> Have your valid ID and supporting documents ready </li><li data-v-b2da9790><i class=\\\"fas fa-check\\\" data-v-b2da9790></i> Processing time may vary depending on document verification </li><li data-v-b2da9790><i class=\\\"fas fa-check\\\" data-v-b2da9790></i> You can pay online using various payment methods </li></ul></div></div>\", 1)), _createElementVNode(\"div\", _hoisted_38, [_cache[25] || (_cache[25] = _createElementVNode(\"div\", {\n    class: \"help-header\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-headset\"\n  }), _createElementVNode(\"h3\", null, \"Need Help?\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_39, [_cache[24] || (_cache[24] = _createElementVNode(\"p\", null, \"If you have questions about document requirements or the application process:\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_40, [_createElementVNode(\"button\", {\n    class: \"help-btn\",\n    onClick: _cache[4] || (_cache[4] = (...args) => $options.openHelp && $options.openHelp(...args))\n  }, _cache[22] || (_cache[22] = [_createElementVNode(\"i\", {\n    class: \"fas fa-question-circle\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" View FAQ \")])), _createElementVNode(\"button\", {\n    class: \"contact-btn\",\n    onClick: _cache[5] || (_cache[5] = (...args) => $options.contactSupport && $options.contactSupport(...args))\n  }, _cache[23] || (_cache[23] = [_createElementVNode(\"i\", {\n    class: \"fas fa-phone\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Contact Support \")]))])])])])])])], 2 /* CLASS */)]);\n}", "map": {"version": 3, "names": ["class", "ref", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_createVNode", "_component_ClientHeader", "userName", "$data", "showUserDropdown", "sidebarCollapsed", "activeMenu", "onSidebarToggle", "$options", "handleSidebarToggle", "onUserDropdownToggle", "handleUserDropdownToggle", "onMenuAction", "handleMenuAction", "onLogout", "handleLogout", "onError", "handleError", "_normalizeClass", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_toDisplayString", "firstName", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "totalRequests", "_hoisted_11", "_hoisted_12", "_hoisted_13", "pendingRequests", "_hoisted_14", "_hoisted_15", "_hoisted_16", "onClick", "_cache", "args", "scrollToServices", "goToMyRequests", "goToProfile", "_hoisted_17", "_hoisted_18", "loading", "_hoisted_19", "error", "_Fragment", "key", "_hoisted_20", "_hoisted_21", "loadDocumentTypes", "_hoisted_22", "_renderList", "documentTypes", "documentType", "id", "is_active", "$event", "selectDocumentType", "_hoisted_24", "getDocumentIcon", "type_name", "_hoisted_25", "_hoisted_26", "_hoisted_27", "description", "_hoisted_28", "_hoisted_29", "_hoisted_30", "formatCurrency", "base_fee", "_hoisted_31", "getProcessingTime", "_hoisted_32", "_hoisted_33", "_hoisted_34", "_hoisted_35", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_hoisted_39", "_hoisted_40", "openHelp", "contactSupport"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\client\\NewDocumentRequest.vue"], "sourcesContent": ["<template>\n  <div class=\"client-dashboard\">\n    <!-- Background -->\n    <div class=\"background-container\">\n      <div class=\"background-image\"></div>\n      <div class=\"background-overlay\"></div>\n    </div>\n\n    <!-- Client Header with Navigation -->\n    <ClientHeader\n      :userName=\"userName\"\n      :showUserDropdown=\"showUserDropdown\"\n      :sidebarCollapsed=\"sidebarCollapsed\"\n      :activeMenu=\"activeMenu\"\n      @sidebar-toggle=\"handleSidebarToggle\"\n      @user-dropdown-toggle=\"handleUserDropdownToggle\"\n      @menu-action=\"handleMenuAction\"\n      @logout=\"handleLogout\"\n      @error=\"handleError\"\n    />\n\n    <!-- Main Content -->\n    <main class=\"main-content\" :class=\"{ 'sidebar-collapsed': sidebarCollapsed }\">\n      <!-- Welcome Section -->\n      <section class=\"welcome-section\">\n        <div class=\"container\">\n          <div class=\"welcome-header\">\n            <div class=\"welcome-text\">\n              <h1 class=\"welcome-title\">Welcome back, {{ firstName }}!</h1>\n              <p class=\"welcome-subtitle\">Request official documents quickly and securely through our digital platform.</p>\n            </div>\n            <div class=\"welcome-stats\">\n              <div class=\"stat-card\">\n                <div class=\"stat-icon\">\n                  <i class=\"fas fa-file-alt\"></i>\n                </div>\n                <div class=\"stat-content\">\n                  <div class=\"stat-number\">{{ totalRequests }}</div>\n                  <div class=\"stat-label\">Total Requests</div>\n                </div>\n              </div>\n              <div class=\"stat-card\">\n                <div class=\"stat-icon\">\n                  <i class=\"fas fa-clock\"></i>\n                </div>\n                <div class=\"stat-content\">\n                  <div class=\"stat-number\">{{ pendingRequests }}</div>\n                  <div class=\"stat-label\">Pending</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Quick Actions Section -->\n      <section class=\"quick-actions-section\">\n        <div class=\"container\">\n          <div class=\"section-header\">\n            <h2 class=\"section-title\">Quick Actions</h2>\n            <p class=\"section-description\">Start a new document request or manage existing ones</p>\n          </div>\n\n          <div class=\"quick-actions-grid\">\n            <div class=\"action-card primary\" @click=\"scrollToServices\">\n              <div class=\"action-icon\">\n                <i class=\"fas fa-plus-circle\"></i>\n              </div>\n              <div class=\"action-content\">\n                <h3>New Request</h3>\n                <p>Start a new document request</p>\n              </div>\n              <div class=\"action-arrow\">\n                <i class=\"fas fa-arrow-right\"></i>\n              </div>\n            </div>\n\n            <div class=\"action-card\" @click=\"goToMyRequests\">\n              <div class=\"action-icon\">\n                <i class=\"fas fa-list-alt\"></i>\n              </div>\n              <div class=\"action-content\">\n                <h3>My Requests</h3>\n                <p>View and track your requests</p>\n              </div>\n              <div class=\"action-arrow\">\n                <i class=\"fas fa-arrow-right\"></i>\n              </div>\n            </div>\n\n            <div class=\"action-card\" @click=\"goToProfile\">\n              <div class=\"action-icon\">\n                <i class=\"fas fa-user-edit\"></i>\n              </div>\n              <div class=\"action-content\">\n                <h3>Update Profile</h3>\n                <p>Keep your information current</p>\n              </div>\n              <div class=\"action-arrow\">\n                <i class=\"fas fa-arrow-right\"></i>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Document Services Section -->\n      <section class=\"services-section\" ref=\"servicesSection\">\n        <div class=\"container\">\n          <div class=\"section-header\">\n            <h2 class=\"section-title\">Available Document Services</h2>\n            <p class=\"section-description\">Choose the type of document you want to request</p>\n          </div>\n\n          <!-- Loading State -->\n          <div v-if=\"loading\" class=\"loading-container\">\n            <div class=\"loading-spinner\">\n              <i class=\"fas fa-spinner fa-spin\"></i>\n            </div>\n            <p>Loading available services...</p>\n          </div>\n\n          <!-- Error State -->\n          <div v-else-if=\"error\" class=\"error-container\">\n            <div class=\"error-content\">\n              <i class=\"fas fa-exclamation-triangle\"></i>\n              <h3>Unable to Load Services</h3>\n              <p>{{ error }}</p>\n              <button class=\"retry-btn\" @click=\"loadDocumentTypes\">\n                <i class=\"fas fa-redo\"></i>\n                Try Again\n              </button>\n            </div>\n          </div>\n\n          <!-- Document Types Grid -->\n          <div v-else class=\"document-types-grid\">\n            <div\n              v-for=\"documentType in documentTypes\"\n              :key=\"documentType.id\"\n              class=\"document-card\"\n              @click=\"selectDocumentType(documentType)\"\n              :class=\"{ 'disabled': !documentType.is_active }\"\n            >\n              <div class=\"document-icon\">\n                <i :class=\"getDocumentIcon(documentType.type_name)\"></i>\n              </div>\n\n              <div class=\"document-content\">\n                <h3 class=\"document-title\">{{ documentType.type_name }}</h3>\n                <p class=\"document-description\">{{ documentType.description }}</p>\n\n                <div class=\"document-details\">\n                  <div class=\"fee-info\">\n                    <span class=\"fee-label\">Base Fee:</span>\n                    <span class=\"fee-amount\">₱{{ formatCurrency(documentType.base_fee) }}</span>\n                  </div>\n\n                  <div class=\"processing-time\">\n                    <i class=\"fas fa-clock\"></i>\n                    <span>{{ getProcessingTime(documentType.type_name) }}</span>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"document-action\">\n                <span v-if=\"!documentType.is_active\" class=\"status-badge unavailable\">\n                  Unavailable\n                </span>\n                <i v-else class=\"fas fa-chevron-right\"></i>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Information Section -->\n      <section class=\"info-section\">\n        <div class=\"container\">\n          <div class=\"info-grid\">\n            <div class=\"info-card\">\n              <div class=\"info-header\">\n                <i class=\"fas fa-info-circle\"></i>\n                <h3>Important Information</h3>\n              </div>\n              <div class=\"info-content\">\n                <ul class=\"info-list\">\n                  <li>\n                    <i class=\"fas fa-check\"></i>\n                    Ensure your profile information is complete and accurate\n                  </li>\n                  <li>\n                    <i class=\"fas fa-check\"></i>\n                    Have your valid ID and supporting documents ready\n                  </li>\n                  <li>\n                    <i class=\"fas fa-check\"></i>\n                    Processing time may vary depending on document verification\n                  </li>\n                  <li>\n                    <i class=\"fas fa-check\"></i>\n                    You can pay online using various payment methods\n                  </li>\n                </ul>\n              </div>\n            </div>\n\n            <div class=\"help-card\">\n              <div class=\"help-header\">\n                <i class=\"fas fa-headset\"></i>\n                <h3>Need Help?</h3>\n              </div>\n              <div class=\"help-content\">\n                <p>If you have questions about document requirements or the application process:</p>\n                <div class=\"help-actions\">\n                  <button class=\"help-btn\" @click=\"openHelp\">\n                    <i class=\"fas fa-question-circle\"></i>\n                    View FAQ\n                  </button>\n                  <button class=\"contact-btn\" @click=\"contactSupport\">\n                    <i class=\"fas fa-phone\"></i>\n                    Contact Support\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n    </main>\n  </div>\n</template>\n\n<script>\nimport documentRequestService from '@/services/documentRequestService';\nimport ClientHeader from './ClientHeader.vue';\nimport unifiedAuthService from '@/services/unifiedAuthService';\n\nexport default {\n  name: 'NewDocumentRequest',\n  components: {\n    ClientHeader\n  },\n  data() {\n    return {\n      documentTypes: [],\n      loading: true,\n      error: null,\n      // Header state\n      showUserDropdown: false,\n      sidebarCollapsed: false,\n      activeMenu: 'dashboard',\n      // User data\n      userName: 'User',\n      firstName: 'User',\n      totalRequests: 0,\n      pendingRequests: 0\n    };\n  },\n  async mounted() {\n    await this.loadUserData();\n    await this.loadDocumentTypes();\n    await this.loadUserStats();\n  },\n  methods: {\n    async loadUserData() {\n      try {\n        const currentUser = unifiedAuthService.getCurrentUser();\n        if (currentUser) {\n          this.userName = currentUser.username || 'User';\n          this.firstName = currentUser.first_name || currentUser.username || 'User';\n        }\n      } catch (error) {\n        console.error('Error loading user data:', error);\n      }\n    },\n\n    async loadUserStats() {\n      try {\n        // TODO: Implement API call to get user statistics\n        // For now, using placeholder data\n        this.totalRequests = 5;\n        this.pendingRequests = 2;\n      } catch (error) {\n        console.error('Error loading user stats:', error);\n      }\n    },\n\n    async loadDocumentTypes() {\n      try {\n        this.loading = true;\n        this.error = null;\n\n        const response = await documentRequestService.getDocumentTypes();\n        this.documentTypes = response.data || [];\n\n      } catch (error) {\n        console.error('Error loading document types:', error);\n        this.error = error.response?.data?.message || 'Failed to load available services';\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    selectDocumentType(documentType) {\n      if (!documentType.is_active) return;\n\n      // Navigate to specific document request form\n      const routeName = this.getRouteForDocumentType(documentType.type_name);\n      if (routeName) {\n        this.$router.push({ \n          name: routeName,\n          params: { documentTypeId: documentType.id }\n        });\n      }\n    },\n\n    getRouteForDocumentType(typeName) {\n      const routes = {\n        'Barangay Clearance': 'BarangayClearanceRequest',\n        'Cedula': 'CedulaRequest'\n      };\n      return routes[typeName];\n    },\n\n    getDocumentIcon(typeName) {\n      const icons = {\n        'Barangay Clearance': 'fas fa-certificate',\n        'Cedula': 'fas fa-id-card'\n      };\n      return icons[typeName] || 'fas fa-file-alt';\n    },\n\n    getProcessingTime(typeName) {\n      const times = {\n        'Barangay Clearance': '3-5 business days',\n        'Cedula': '1-2 business days'\n      };\n      return times[typeName] || '3-5 business days';\n    },\n\n    formatCurrency(amount) {\n      return parseFloat(amount).toFixed(2);\n    },\n\n    // Header event handlers\n    handleSidebarToggle() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n    },\n\n    handleUserDropdownToggle() {\n      this.showUserDropdown = !this.showUserDropdown;\n    },\n\n    handleMenuAction(action) {\n      console.log('Menu action:', action);\n      switch (action) {\n        case 'profile':\n          // TODO: Navigate to profile page\n          break;\n        case 'settings':\n          // TODO: Navigate to settings page\n          break;\n        case 'account':\n          // TODO: Navigate to account page\n          break;\n      }\n    },\n\n    handleLogout() {\n      try {\n        unifiedAuthService.logout();\n        this.$router.push({ name: 'WelcomePage' });\n      } catch (error) {\n        console.error('Logout error:', error);\n      }\n    },\n\n    handleError(error) {\n      console.error('Header error:', error);\n    },\n\n    // Navigation methods\n    scrollToServices() {\n      this.$refs.servicesSection?.scrollIntoView({ behavior: 'smooth' });\n    },\n\n    goToMyRequests() {\n      this.$router.push({ name: 'MyRequests' });\n    },\n\n    goToProfile() {\n      // TODO: Navigate to profile page\n      console.log('Navigate to profile');\n    },\n\n    goBack() {\n      this.$router.push({ name: 'ClientDashboard' });\n    },\n\n    openHelp() {\n      // TODO: Implement help/FAQ modal or page\n      console.log('Opening help...');\n    },\n\n    contactSupport() {\n      // TODO: Implement contact support functionality\n      console.log('Contacting support...');\n    }\n  }\n};\n</script>\n\n<style scoped>\n.new-request-page {\n  position: relative;\n  min-height: 100vh;\n  overflow-x: hidden;\n}\n\n/* Background Setup */\n.background-container {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: -2;\n}\n\n.background-image {\n  width: 100%;\n  height: 100%;\n  background-image: url('@/assets/bula-request-background-pic.png');\n  background-size: cover;\n  background-position: center;\n  background-repeat: no-repeat;\n}\n\n.background-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    135deg,\n    rgba(0, 123, 191, 0.75) 0%,\n    rgba(0, 86, 134, 0.85) 50%,\n    rgba(26, 54, 93, 0.9) 100%\n  );\n  z-index: -1;\n}\n\n.page-header {\n  position: relative;\n  z-index: 1;\n  margin-bottom: 2rem;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: 1rem;\n  padding: 2rem;\n  margin: 2rem;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  gap: 2rem;\n}\n\n.header-main {\n  flex: 1;\n}\n\n.logo-section {\n  margin-bottom: 1.5rem;\n}\n\n.header-logo {\n  width: 80px;\n  height: 80px;\n  border-radius: 50%;\n  border: 3px solid rgba(0, 123, 191, 0.3);\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\n}\n\n.page-title {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #1a365d;\n  margin-bottom: 0.5rem;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.page-title i {\n  color: #007bbf;\n}\n\n.page-description {\n  font-size: 1.1rem;\n  color: #4a5568;\n  margin: 0;\n  line-height: 1.5;\n}\n\n.back-btn {\n  background: rgba(0, 123, 191, 0.1);\n  border: 2px solid #007bbf;\n  padding: 0.75rem 1.5rem;\n  border-radius: 0.75rem;\n  color: #007bbf;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  backdrop-filter: blur(5px);\n}\n\n.back-btn:hover {\n  background: #007bbf;\n  color: white;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(0, 123, 191, 0.3);\n}\n\n.loading-container, .error-container {\n  position: relative;\n  z-index: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n  text-align: center;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: 1rem;\n  margin: 2rem;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.loading-spinner i {\n  font-size: 3rem;\n  color: #007bbf;\n  margin-bottom: 1rem;\n}\n\n.error-content {\n  max-width: 400px;\n}\n\n.error-content i {\n  font-size: 3rem;\n  color: #e53e3e;\n  margin-bottom: 1rem;\n}\n\n.retry-btn {\n  background: linear-gradient(135deg, #007bbf, #005a86);\n  color: white;\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 0.75rem;\n  cursor: pointer;\n  margin-top: 1rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 15px rgba(0, 123, 191, 0.3);\n}\n\n.retry-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(0, 123, 191, 0.4);\n}\n\n.document-types-container {\n  position: relative;\n  z-index: 1;\n}\n\n.content-wrapper {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: 1rem;\n  padding: 2rem;\n  margin: 2rem;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.document-types-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n  gap: 1.5rem;\n  margin-bottom: 3rem;\n}\n\n.document-card {\n  background: rgba(255, 255, 255, 0.9);\n  backdrop-filter: blur(5px);\n  border: 2px solid rgba(0, 123, 191, 0.2);\n  border-radius: 1rem;\n  padding: 2rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);\n}\n\n.document-card:hover:not(.disabled) {\n  border-color: #007bbf;\n  transform: translateY(-5px);\n  box-shadow: 0 12px 30px rgba(0, 123, 191, 0.2);\n  background: rgba(255, 255, 255, 0.95);\n}\n\n.document-card.disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.document-icon {\n  flex-shrink: 0;\n  width: 4rem;\n  height: 4rem;\n  background: linear-gradient(135deg, #007bbf, #005a86);\n  border-radius: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 1.5rem;\n  box-shadow: 0 4px 15px rgba(0, 123, 191, 0.3);\n}\n\n.document-content {\n  flex: 1;\n}\n\n.document-title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 0.5rem;\n}\n\n.document-description {\n  color: #4a5568;\n  margin-bottom: 1rem;\n  line-height: 1.5;\n}\n\n.document-details {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.fee-info {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.fee-label {\n  color: #718096;\n  font-size: 0.9rem;\n}\n\n.fee-amount {\n  font-weight: 600;\n  color: #38a169;\n  font-size: 1.1rem;\n}\n\n.processing-time {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #718096;\n  font-size: 0.9rem;\n}\n\n.document-action {\n  flex-shrink: 0;\n  display: flex;\n  align-items: center;\n}\n\n.document-action i {\n  color: #a0aec0;\n  font-size: 1.25rem;\n}\n\n.status-badge.unavailable {\n  background: #fed7d7;\n  color: #c53030;\n  padding: 0.25rem 0.75rem;\n  border-radius: 1rem;\n  font-size: 0.8rem;\n  font-weight: 500;\n}\n\n.info-section {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 2rem;\n}\n\n.info-card, .help-card {\n  background: rgba(255, 255, 255, 0.9);\n  backdrop-filter: blur(5px);\n  border: 1px solid rgba(0, 123, 191, 0.2);\n  border-radius: 0.75rem;\n  padding: 1.5rem;\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);\n  transition: all 0.3s ease;\n}\n\n.info-card:hover, .help-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(0, 123, 191, 0.1);\n  background: rgba(255, 255, 255, 0.95);\n}\n\n.info-header, .help-header {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  margin-bottom: 1rem;\n}\n\n.info-header h3, .help-header h3 {\n  font-size: 1.1rem;\n  font-weight: 600;\n  color: #1a365d;\n  margin: 0;\n}\n\n.info-header i {\n  color: #007bbf;\n}\n\n.help-header i {\n  color: #38a169;\n}\n\n.info-list {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.info-list li {\n  display: flex;\n  align-items: flex-start;\n  gap: 0.75rem;\n  margin-bottom: 0.75rem;\n  color: #4a5568;\n  line-height: 1.5;\n}\n\n.info-list i {\n  color: #38a169;\n  margin-top: 0.125rem;\n  flex-shrink: 0;\n}\n\n.help-content p {\n  color: #4a5568;\n  margin-bottom: 1rem;\n  line-height: 1.5;\n}\n\n.help-actions {\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n}\n\n.help-btn, .contact-btn {\n  background: rgba(255, 255, 255, 0.8);\n  backdrop-filter: blur(5px);\n  border: 2px solid rgba(0, 123, 191, 0.3);\n  padding: 0.75rem 1rem;\n  border-radius: 0.75rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #4a5568;\n  font-weight: 500;\n}\n\n.help-btn:hover {\n  border-color: #007bbf;\n  color: #007bbf;\n  background: rgba(0, 123, 191, 0.1);\n  transform: translateY(-1px);\n  box-shadow: 0 4px 10px rgba(0, 123, 191, 0.2);\n}\n\n.contact-btn:hover {\n  border-color: #38a169;\n  color: #38a169;\n  background: rgba(56, 161, 105, 0.1);\n  transform: translateY(-1px);\n  box-shadow: 0 4px 10px rgba(56, 161, 105, 0.2);\n}\n\n@media (max-width: 768px) {\n  .page-header {\n    margin: 1rem;\n    padding: 1.5rem;\n  }\n\n  .content-wrapper {\n    margin: 1rem;\n    padding: 1.5rem;\n  }\n\n  .header-content {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 1.5rem;\n  }\n\n  .header-logo {\n    width: 60px;\n    height: 60px;\n  }\n\n  .page-title {\n    font-size: 1.5rem;\n  }\n\n  .document-types-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n\n  .document-card {\n    flex-direction: column;\n    text-align: center;\n    padding: 1.5rem;\n  }\n\n  .info-section {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n\n  .loading-container, .error-container {\n    margin: 1rem;\n    padding: 3rem 1.5rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .page-header {\n    margin: 0.5rem;\n    padding: 1rem;\n  }\n\n  .content-wrapper {\n    margin: 0.5rem;\n    padding: 1rem;\n  }\n\n  .header-logo {\n    width: 50px;\n    height: 50px;\n  }\n\n  .page-title {\n    font-size: 1.25rem;\n  }\n\n  .document-card {\n    padding: 1rem;\n  }\n\n  .info-card, .help-card {\n    padding: 1rem;\n  }\n\n  .loading-container, .error-container {\n    margin: 0.5rem;\n    padding: 2rem 1rem;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAkB;;EAuBhBA,KAAK,EAAC;AAAiB;;EACzBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAc;;EACnBA,KAAK,EAAC;AAAe;;EAGtBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAW;;EAIfA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAa;;EAIvBA,KAAK,EAAC;AAAW;;EAIfA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAa;;EAU3BA,KAAK,EAAC;AAAuB;;EAC/BA,KAAK,EAAC;AAAW;;EAMfA,KAAK,EAAC;AAAoB;;EA4C1BA,KAAK,EAAC,kBAAkB;EAACC,GAAG,EAAC;;;EAC/BD,KAAK,EAAC;AAAW;;;EAOAA,KAAK,EAAC;;;EAQHA,KAAK,EAAC;AAAiB;;EACvCA,KAAK,EAAC;AAAe;;EAYhBA,KAAK,EAAC;AAAqB;;;EAQ9BA,KAAK,EAAC;AAAe;;EAIrBA,KAAK,EAAC;AAAkB;;EACvBA,KAAK,EAAC;AAAgB;;EACvBA,KAAK,EAAC;AAAsB;;EAE1BA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAU;;EAEbA,KAAK,EAAC;AAAY;;EAGrBA,KAAK,EAAC;AAAiB;;EAO3BA,KAAK,EAAC;AAAiB;;;EACWA,KAAK,EAAC;;;;EAGjCA,KAAK,EAAC;;;EAQjBA,KAAK,EAAC;AAAc;;EACtBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAW;;EA4BfA,KAAK,EAAC;AAAW;;EAKfA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAc;;;uBArNvCE,mBAAA,CAqOM,OArONC,UAqOM,GApOJC,mBAAA,gBAAmB,E,4BACnBC,mBAAA,CAGM;IAHDL,KAAK,EAAC;EAAsB,IAC/BK,mBAAA,CAAoC;IAA/BL,KAAK,EAAC;EAAkB,IAC7BK,mBAAA,CAAsC;IAAjCL,KAAK,EAAC;EAAoB,G,sBAGjCI,mBAAA,mCAAsC,EACtCE,YAAA,CAUEC,uBAAA;IATCC,QAAQ,EAAEC,KAAA,CAAAD,QAAQ;IAClBE,gBAAgB,EAAED,KAAA,CAAAC,gBAAgB;IAClCC,gBAAgB,EAAEF,KAAA,CAAAE,gBAAgB;IAClCC,UAAU,EAAEH,KAAA,CAAAG,UAAU;IACtBC,eAAc,EAAEC,QAAA,CAAAC,mBAAmB;IACnCC,oBAAoB,EAAEF,QAAA,CAAAG,wBAAwB;IAC9CC,YAAW,EAAEJ,QAAA,CAAAK,gBAAgB;IAC7BC,QAAM,EAAEN,QAAA,CAAAO,YAAY;IACpBC,OAAK,EAAER,QAAA,CAAAS;iLAGVnB,mBAAA,kBAAqB,EACrBC,mBAAA,CA+MO;IA/MDL,KAAK,EAAAwB,eAAA,EAAC,cAAc;MAAA,qBAAgCf,KAAA,CAAAE;IAAgB;MACxEP,mBAAA,qBAAwB,EACxBC,mBAAA,CA6BU,WA7BVoB,UA6BU,GA5BRpB,mBAAA,CA2BM,OA3BNqB,UA2BM,GA1BJrB,mBAAA,CAyBM,OAzBNsB,UAyBM,GAxBJtB,mBAAA,CAGM,OAHNuB,UAGM,GAFJvB,mBAAA,CAA6D,MAA7DwB,UAA6D,EAAnC,gBAAc,GAAAC,gBAAA,CAAGrB,KAAA,CAAAsB,SAAS,IAAG,GAAC,iB,0BACxD1B,mBAAA,CAA6G;IAA1GL,KAAK,EAAC;EAAkB,GAAC,+EAA6E,qB,GAE3GK,mBAAA,CAmBM,OAnBN2B,UAmBM,GAlBJ3B,mBAAA,CAQM,OARN4B,UAQM,G,0BAPJ5B,mBAAA,CAEM;IAFDL,KAAK,EAAC;EAAW,IACpBK,mBAAA,CAA+B;IAA5BL,KAAK,EAAC;EAAiB,G,sBAE5BK,mBAAA,CAGM,OAHN6B,UAGM,GAFJ7B,mBAAA,CAAkD,OAAlD8B,WAAkD,EAAAL,gBAAA,CAAtBrB,KAAA,CAAA2B,aAAa,kB,0BACzC/B,mBAAA,CAA4C;IAAvCL,KAAK,EAAC;EAAY,GAAC,gBAAc,qB,KAG1CK,mBAAA,CAQM,OARNgC,WAQM,G,4BAPJhC,mBAAA,CAEM;IAFDL,KAAK,EAAC;EAAW,IACpBK,mBAAA,CAA4B;IAAzBL,KAAK,EAAC;EAAc,G,sBAEzBK,mBAAA,CAGM,OAHNiC,WAGM,GAFJjC,mBAAA,CAAoD,OAApDkC,WAAoD,EAAAT,gBAAA,CAAxBrB,KAAA,CAAA+B,eAAe,kB,0BAC3CnC,mBAAA,CAAqC;IAAhCL,KAAK,EAAC;EAAY,GAAC,SAAO,qB,aAQ3CI,mBAAA,2BAA8B,EAC9BC,mBAAA,CAgDU,WAhDVoC,WAgDU,GA/CRpC,mBAAA,CA8CM,OA9CNqC,WA8CM,G,4BA7CJrC,mBAAA,CAGM;IAHDL,KAAK,EAAC;EAAgB,IACzBK,mBAAA,CAA4C;IAAxCL,KAAK,EAAC;EAAe,GAAC,eAAa,GACvCK,mBAAA,CAAuF;IAApFL,KAAK,EAAC;EAAqB,GAAC,sDAAoD,E,sBAGrFK,mBAAA,CAuCM,OAvCNsC,WAuCM,GAtCJtC,mBAAA,CAWM;IAXDL,KAAK,EAAC,qBAAqB;IAAE4C,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhC,QAAA,CAAAiC,gBAAA,IAAAjC,QAAA,CAAAiC,gBAAA,IAAAD,IAAA,CAAgB;sZAazDzC,mBAAA,CAWM;IAXDL,KAAK,EAAC,aAAa;IAAE4C,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhC,QAAA,CAAAkC,cAAA,IAAAlC,QAAA,CAAAkC,cAAA,IAAAF,IAAA,CAAc;mZAa/CzC,mBAAA,CAWM;IAXDL,KAAK,EAAC,aAAa;IAAE4C,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhC,QAAA,CAAAmC,WAAA,IAAAnC,QAAA,CAAAmC,WAAA,IAAAH,IAAA,CAAW;8ZAgBlD1C,mBAAA,+BAAkC,EAClCC,mBAAA,CAmEU,WAnEV6C,WAmEU,GAlER7C,mBAAA,CAiEM,OAjEN8C,WAiEM,G,4BAhEJ9C,mBAAA,CAGM;IAHDL,KAAK,EAAC;EAAgB,IACzBK,mBAAA,CAA0D;IAAtDL,KAAK,EAAC;EAAe,GAAC,6BAA2B,GACrDK,mBAAA,CAAkF;IAA/EL,KAAK,EAAC;EAAqB,GAAC,iDAA+C,E,sBAGhFI,mBAAA,mBAAsB,EACXK,KAAA,CAAA2C,OAAO,I,cAAlBlD,mBAAA,CAKM,OALNmD,WAKM,EAAAR,MAAA,SAAAA,MAAA,QAJJxC,mBAAA,CAEM;IAFDL,KAAK,EAAC;EAAiB,IAC1BK,mBAAA,CAAsC;IAAnCL,KAAK,EAAC;EAAwB,G,qBAEnCK,mBAAA,CAAoC,WAAjC,+BAA6B,oB,MAIlBI,KAAA,CAAA6C,KAAK,I,cAArBpD,mBAAA,CAUMqD,SAAA;IAAAC,GAAA;EAAA,IAXNpD,mBAAA,iBAAoB,EACpBC,mBAAA,CAUM,OAVNoD,WAUM,GATJpD,mBAAA,CAQM,OARNqD,WAQM,G,4BAPJrD,mBAAA,CAA2C;IAAxCL,KAAK,EAAC;EAA6B,6B,4BACtCK,mBAAA,CAAgC,YAA5B,yBAAuB,sBAC3BA,mBAAA,CAAkB,WAAAyB,gBAAA,CAAZrB,KAAA,CAAA6C,KAAK,kBACXjD,mBAAA,CAGS;IAHDL,KAAK,EAAC,WAAW;IAAE4C,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhC,QAAA,CAAA6C,iBAAA,IAAA7C,QAAA,CAAA6C,iBAAA,IAAAb,IAAA,CAAiB;kCACjDzC,mBAAA,CAA2B;IAAxBL,KAAK,EAAC;EAAa,4B,iBAAK,aAE7B,E,0EAKJE,mBAAA,CAoCMqD,SAAA;IAAAC,GAAA;EAAA,IArCNpD,mBAAA,yBAA4B,EAC5BC,mBAAA,CAoCM,OApCNuD,WAoCM,I,kBAnCJ1D,mBAAA,CAkCMqD,SAAA,QAAAM,WAAA,CAjCmBpD,KAAA,CAAAqD,aAAa,EAA7BC,YAAY;yBADrB7D,mBAAA,CAkCM;MAhCHsD,GAAG,EAAEO,YAAY,CAACC,EAAE;MACrBhE,KAAK,EAAAwB,eAAA,EAAC,eAAe;QAAA,aAEEuC,YAAY,CAACE;MAAS;MAD5CrB,OAAK,EAAAsB,MAAA,IAAEpD,QAAA,CAAAqD,kBAAkB,CAACJ,YAAY;QAGvC1D,mBAAA,CAEM,OAFN+D,WAEM,GADJ/D,mBAAA,CAAwD;MAApDL,KAAK,EAAAwB,eAAA,CAAEV,QAAA,CAAAuD,eAAe,CAACN,YAAY,CAACO,SAAS;+BAGnDjE,mBAAA,CAeM,OAfNkE,WAeM,GAdJlE,mBAAA,CAA4D,MAA5DmE,WAA4D,EAAA1C,gBAAA,CAA9BiC,YAAY,CAACO,SAAS,kBACpDjE,mBAAA,CAAkE,KAAlEoE,WAAkE,EAAA3C,gBAAA,CAA/BiC,YAAY,CAACW,WAAW,kBAE3DrE,mBAAA,CAUM,OAVNsE,WAUM,GATJtE,mBAAA,CAGM,OAHNuE,WAGM,G,4BAFJvE,mBAAA,CAAwC;MAAlCL,KAAK,EAAC;IAAW,GAAC,WAAS,sBACjCK,mBAAA,CAA4E,QAA5EwE,WAA4E,EAAnD,GAAC,GAAA/C,gBAAA,CAAGhB,QAAA,CAAAgE,cAAc,CAACf,YAAY,CAACgB,QAAQ,kB,GAGnE1E,mBAAA,CAGM,OAHN2E,WAGM,G,4BAFJ3E,mBAAA,CAA4B;MAAzBL,KAAK,EAAC;IAAc,6BACvBK,mBAAA,CAA4D,cAAAyB,gBAAA,CAAnDhB,QAAA,CAAAmE,iBAAiB,CAAClB,YAAY,CAACO,SAAS,kB,OAKvDjE,mBAAA,CAKM,OALN6E,WAKM,G,CAJSnB,YAAY,CAACE,SAAS,I,cAAnC/D,mBAAA,CAEO,QAFPiF,WAEO,EAF+D,eAEtE,M,cACAjF,mBAAA,CAA2C,KAA3CkF,WAA2C,G;gHAOrDhF,mBAAA,yBAA4B,EAC5BC,mBAAA,CAmDU,WAnDVgF,WAmDU,GAlDRhF,mBAAA,CAiDM,OAjDNiF,WAiDM,GAhDJjF,mBAAA,CA+CM,OA/CNkF,WA+CM,G,i1BAnBJlF,mBAAA,CAkBM,OAlBNmF,WAkBM,G,4BAjBJnF,mBAAA,CAGM;IAHDL,KAAK,EAAC;EAAa,IACtBK,mBAAA,CAA8B;IAA3BL,KAAK,EAAC;EAAgB,IACzBK,mBAAA,CAAmB,YAAf,YAAU,E,sBAEhBA,mBAAA,CAYM,OAZNoF,WAYM,G,4BAXJpF,mBAAA,CAAoF,WAAjF,+EAA6E,sBAChFA,mBAAA,CASM,OATNqF,WASM,GARJrF,mBAAA,CAGS;IAHDL,KAAK,EAAC,UAAU;IAAE4C,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhC,QAAA,CAAA6E,QAAA,IAAA7E,QAAA,CAAA6E,QAAA,IAAA7C,IAAA,CAAQ;kCACvCzC,mBAAA,CAAsC;IAAnCL,KAAK,EAAC;EAAwB,4B,iBAAK,YAExC,E,IACAK,mBAAA,CAGS;IAHDL,KAAK,EAAC,aAAa;IAAE4C,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhC,QAAA,CAAA8E,cAAA,IAAA9E,QAAA,CAAA8E,cAAA,IAAA9C,IAAA,CAAc;kCAChDzC,mBAAA,CAA4B;IAAzBL,KAAK,EAAC;EAAc,4B,iBAAK,mBAE9B,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}