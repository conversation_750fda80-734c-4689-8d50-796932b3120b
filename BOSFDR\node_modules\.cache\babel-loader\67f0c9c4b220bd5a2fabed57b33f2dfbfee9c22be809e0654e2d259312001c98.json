{"ast": null, "code": "import { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"hero-section\",\n  \"aria-labelledby\": \"hero-title\"\n};\nconst _hoisted_2 = {\n  class: \"container\"\n};\nconst _hoisted_3 = {\n  class: \"hero-content\"\n};\nconst _hoisted_4 = {\n  class: \"hero-text\"\n};\nconst _hoisted_5 = {\n  id: \"hero-title\",\n  class: \"hero-title\"\n};\nconst _hoisted_6 = {\n  class: \"hero-actions\"\n};\nconst _hoisted_7 = {\n  class: \"hero-stats\"\n};\nconst _hoisted_8 = {\n  class: \"stats-grid\"\n};\nconst _hoisted_9 = {\n  class: \"stat-card\"\n};\nconst _hoisted_10 = {\n  class: \"stat-content\"\n};\nconst _hoisted_11 = {\n  class: \"stat-number\"\n};\nconst _hoisted_12 = {\n  class: \"stat-card\"\n};\nconst _hoisted_13 = {\n  class: \"stat-content\"\n};\nconst _hoisted_14 = {\n  class: \"stat-number\"\n};\nconst _hoisted_15 = {\n  class: \"stat-card\"\n};\nconst _hoisted_16 = {\n  class: \"stat-content\"\n};\nconst _hoisted_17 = {\n  class: \"stat-number\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"section\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"h1\", _hoisted_5, \" Welcome back, \" + _toDisplayString($props.firstName) + \"! \", 1 /* TEXT */), _cache[4] || (_cache[4] = _createElementVNode(\"p\", {\n    class: \"hero-subtitle\"\n  }, \" Access government services and request official documents through our secure digital platform. \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"button\", {\n    class: \"btn btn-primary\",\n    onClick: _cache[0] || (_cache[0] = $event => _ctx.$emit('start-new-request')),\n    type: \"button\"\n  }, _cache[2] || (_cache[2] = [_createElementVNode(\"svg\", {\n    class: \"btn-icon\",\n    \"aria-hidden\": \"true\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z\"\n  })], -1 /* HOISTED */), _createTextVNode(\" Start New Request \")])), _createElementVNode(\"button\", {\n    class: \"btn btn-secondary\",\n    onClick: _cache[1] || (_cache[1] = $event => _ctx.$emit('view-requests')),\n    type: \"button\"\n  }, _cache[3] || (_cache[3] = [_createElementVNode(\"svg\", {\n    class: \"btn-icon\",\n    \"aria-hidden\": \"true\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M3,5H9V11H3V5M5,7V9H7V7H5M11,7H21V9H11V7M11,15H21V17H11V15M5,20L1.5,16.5L2.91,15.09L5,17.17L9.59,12.59L11,14L5,20Z\"\n  })], -1 /* HOISTED */), _createTextVNode(\" View My Requests \")]))])]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n    class: \"stat-icon\"\n  }, [_createElementVNode(\"svg\", {\n    \"aria-hidden\": \"true\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n  })])], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, _toDisplayString($props.totalRequests), 1 /* TEXT */), _cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n    class: \"stat-label\"\n  }, \"Total Requests\", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_12, [_cache[8] || (_cache[8] = _createElementVNode(\"div\", {\n    class: \"stat-icon\"\n  }, [_createElementVNode(\"svg\", {\n    \"aria-hidden\": \"true\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z\"\n  })])], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, _toDisplayString($props.pendingRequests), 1 /* TEXT */), _cache[7] || (_cache[7] = _createElementVNode(\"div\", {\n    class: \"stat-label\"\n  }, \"Pending\", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_15, [_cache[10] || (_cache[10] = _createElementVNode(\"div\", {\n    class: \"stat-icon\"\n  }, [_createElementVNode(\"svg\", {\n    \"aria-hidden\": \"true\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2M10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z\"\n  })])], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, _toDisplayString($setup.completedRequests), 1 /* TEXT */), _cache[9] || (_cache[9] = _createElementVNode(\"div\", {\n    class: \"stat-label\"\n  }, \"Completed\", -1 /* HOISTED */))])])])])])])]);\n}", "map": {"version": 3, "names": ["class", "id", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_toDisplayString", "$props", "firstName", "_hoisted_6", "onClick", "_cache", "$event", "_ctx", "$emit", "type", "viewBox", "fill", "d", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "totalRequests", "_hoisted_12", "_hoisted_13", "_hoisted_14", "pendingRequests", "_hoisted_15", "_hoisted_16", "_hoisted_17", "$setup", "completedRequests"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\client\\sections\\HeroSection.vue"], "sourcesContent": ["<template>\n  <section class=\"hero-section\" aria-labelledby=\"hero-title\">\n    <div class=\"container\">\n      <div class=\"hero-content\">\n        <div class=\"hero-text\">\n          <h1 id=\"hero-title\" class=\"hero-title\">\n            Welcome back, {{ firstName }}!\n          </h1>\n          <p class=\"hero-subtitle\">\n            Access government services and request official documents through our secure digital platform.\n          </p>\n          <div class=\"hero-actions\">\n            <button \n              class=\"btn btn-primary\" \n              @click=\"$emit('start-new-request')\"\n              type=\"button\"\n            >\n              <svg class=\"btn-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z\"/>\n              </svg>\n              Start New Request\n            </button>\n            <button \n              class=\"btn btn-secondary\" \n              @click=\"$emit('view-requests')\"\n              type=\"button\"\n            >\n              <svg class=\"btn-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M3,5H9V11H3V5M5,7V9H7V7H5M11,7H21V9H11V7M11,15H21V17H11V15M5,20L1.5,16.5L2.91,15.09L5,17.17L9.59,12.59L11,14L5,20Z\"/>\n              </svg>\n              View My Requests\n            </button>\n          </div>\n        </div>\n        \n        <div class=\"hero-stats\">\n          <div class=\"stats-grid\">\n            <div class=\"stat-card\">\n              <div class=\"stat-icon\">\n                <svg aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"/>\n                </svg>\n              </div>\n              <div class=\"stat-content\">\n                <div class=\"stat-number\">{{ totalRequests }}</div>\n                <div class=\"stat-label\">Total Requests</div>\n              </div>\n            </div>\n            \n            <div class=\"stat-card\">\n              <div class=\"stat-icon\">\n                <svg aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z\"/>\n                </svg>\n              </div>\n              <div class=\"stat-content\">\n                <div class=\"stat-number\">{{ pendingRequests }}</div>\n                <div class=\"stat-label\">Pending</div>\n              </div>\n            </div>\n            \n            <div class=\"stat-card\">\n              <div class=\"stat-icon\">\n                <svg aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2M10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z\"/>\n                </svg>\n              </div>\n              <div class=\"stat-content\">\n                <div class=\"stat-number\">{{ completedRequests }}</div>\n                <div class=\"stat-label\">Completed</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </section>\n</template>\n\n<script>\nimport { computed } from 'vue';\n\nexport default {\n  name: 'HeroSection',\n  props: {\n    firstName: {\n      type: String,\n      required: true\n    },\n    totalRequests: {\n      type: Number,\n      default: 0\n    },\n    pendingRequests: {\n      type: Number,\n      default: 0\n    }\n  },\n  emits: [\n    'start-new-request',\n    'view-requests'\n  ],\n  setup(props) {\n    const completedRequests = computed(() => {\n      return Math.max(0, props.totalRequests - props.pendingRequests);\n    });\n\n    return {\n      completedRequests\n    };\n  }\n};\n</script>\n\n<style scoped>\n/* Hero Section Styles */\n.hero-section {\n  padding: var(--spacing-12) 0 var(--spacing-10);\n  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);\n  color: var(--color-text-inverse);\n  position: relative;\n  overflow: hidden;\n}\n\n.hero-section::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(45deg, rgba(0, 94, 162, 0.05) 25%, transparent 25%),\n              linear-gradient(-45deg, rgba(0, 94, 162, 0.05) 25%, transparent 25%),\n              linear-gradient(45deg, transparent 75%, rgba(0, 94, 162, 0.05) 75%),\n              linear-gradient(-45deg, transparent 75%, rgba(0, 94, 162, 0.05) 75%);\n  background-size: 20px 20px;\n  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;\n  opacity: 0.1;\n  z-index: 1;\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 var(--spacing-4);\n  position: relative;\n  z-index: 2;\n}\n\n.hero-content {\n  display: grid;\n  grid-template-columns: 1fr auto;\n  gap: var(--spacing-10);\n  align-items: center;\n}\n\n.hero-title {\n  font-size: var(--font-size-3xl);\n  font-weight: 700;\n  line-height: var(--line-height-2);\n  margin: 0 0 var(--spacing-4);\n  color: var(--color-text-inverse);\n}\n\n.hero-subtitle {\n  font-size: var(--font-size-lg);\n  line-height: var(--line-height-5);\n  margin: 0 0 var(--spacing-8);\n  opacity: 0.9;\n  max-width: 600px;\n}\n\n.hero-actions {\n  display: flex;\n  gap: var(--spacing-4);\n  flex-wrap: wrap;\n}\n\n.btn {\n  display: inline-flex;\n  align-items: center;\n  gap: var(--spacing-2);\n  padding: var(--spacing-3) var(--spacing-6);\n  border: 2px solid transparent;\n  border-radius: var(--radius-lg);\n  font-size: var(--font-size-md);\n  font-weight: 600;\n  text-decoration: none;\n  cursor: pointer;\n  transition: all var(--duration-base) var(--easing-standard);\n  min-height: 48px; /* WCAG touch target */\n}\n\n.btn-icon {\n  width: 20px;\n  height: 20px;\n}\n\n.btn-primary {\n  background: var(--color-text-inverse);\n  color: var(--color-primary);\n  border-color: var(--color-text-inverse);\n}\n\n.btn-primary:hover,\n.btn-primary:focus {\n  background: var(--color-bg-secondary);\n  transform: translateY(-2px);\n  box-shadow: var(--shadow-3);\n}\n\n.btn-secondary {\n  background: transparent;\n  color: var(--color-text-inverse);\n  border-color: var(--color-text-inverse);\n}\n\n.btn-secondary:hover,\n.btn-secondary:focus {\n  background: var(--color-text-inverse);\n  color: var(--color-primary);\n}\n\n.btn:focus {\n  outline: var(--focus-ring-width) solid var(--color-accent);\n  outline-offset: var(--focus-ring-offset);\n}\n\n/* Stats Grid */\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: var(--spacing-4);\n  min-width: 300px;\n}\n\n.stat-card {\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: var(--radius-xl);\n  padding: var(--spacing-5);\n  text-align: center;\n  transition: transform var(--duration-base) var(--easing-standard);\n}\n\n.stat-card:hover {\n  transform: translateY(-4px);\n  background: rgba(255, 255, 255, 0.15);\n}\n\n.stat-icon {\n  width: 48px;\n  height: 48px;\n  margin: 0 auto var(--spacing-3);\n  color: var(--color-accent);\n}\n\n.stat-icon svg {\n  width: 100%;\n  height: 100%;\n}\n\n.stat-number {\n  font-size: var(--font-size-2xl);\n  font-weight: 700;\n  line-height: var(--line-height-1);\n  margin-bottom: var(--spacing-1);\n}\n\n.stat-label {\n  font-size: var(--font-size-sm);\n  opacity: 0.8;\n  font-weight: 500;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .hero-content {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-8);\n    text-align: center;\n  }\n  \n  .hero-title {\n    font-size: var(--font-size-2xl);\n  }\n  \n  .hero-actions {\n    justify-content: center;\n  }\n  \n  .stats-grid {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-3);\n  }\n}\n\n@media (max-width: 480px) {\n  .hero-actions {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .btn {\n    justify-content: center;\n  }\n}\n</style>\n"], "mappings": ";;EACWA,KAAK,EAAC,cAAc;EAAC,iBAAe,EAAC;;;EACvCA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAW;;EAChBC,EAAE,EAAC,YAAY;EAACD,KAAK,EAAC;;;EAMrBA,KAAK,EAAC;AAAc;;EAwBtBA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAW;;EAMfA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAa;;EAKvBA,KAAK,EAAC;AAAW;;EAMfA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAa;;EAKvBA,KAAK,EAAC;AAAW;;EAMfA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAa;;uBAnEtCE,mBAAA,CA2EU,WA3EVC,UA2EU,GA1ERC,mBAAA,CAyEM,OAzENC,UAyEM,GAxEJD,mBAAA,CAuEM,OAvENE,UAuEM,GAtEJF,mBAAA,CA6BM,OA7BNG,UA6BM,GA5BJH,mBAAA,CAEK,MAFLI,UAEK,EAFkC,iBACvB,GAAAC,gBAAA,CAAGC,MAAA,CAAAC,SAAS,IAAG,IAC/B,iB,0BACAP,mBAAA,CAEI;IAFDJ,KAAK,EAAC;EAAe,GAAC,kGAEzB,sBACAI,mBAAA,CAqBM,OArBNQ,UAqBM,GApBJR,mBAAA,CASS;IARPJ,KAAK,EAAC,iBAAiB;IACtBa,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,IAAA,CAAAC,KAAK;IACbC,IAAI,EAAC;gCAELd,mBAAA,CAEM;IAFDJ,KAAK,EAAC,UAAU;IAAC,aAAW,EAAC,MAAM;IAACmB,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MAChEhB,mBAAA,CAAqD;IAA/CiB,CAAC,EAAC;EAA2C,G,sCAC/C,qBAER,E,IACAjB,mBAAA,CASS;IARPJ,KAAK,EAAC,mBAAmB;IACxBa,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,IAAA,CAAAC,KAAK;IACbC,IAAI,EAAC;gCAELd,mBAAA,CAEM;IAFDJ,KAAK,EAAC,UAAU;IAAC,aAAW,EAAC,MAAM;IAACmB,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MAChEhB,mBAAA,CAA8H;IAAxHiB,CAAC,EAAC;EAAoH,G,sCACxH,oBAER,E,QAIJjB,mBAAA,CAsCM,OAtCNkB,UAsCM,GArCJlB,mBAAA,CAoCM,OApCNmB,UAoCM,GAnCJnB,mBAAA,CAUM,OAVNoB,UAUM,G,0BATJpB,mBAAA,CAIM;IAJDJ,KAAK,EAAC;EAAW,IACpBI,mBAAA,CAEM;IAFD,aAAW,EAAC,MAAM;IAACe,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MAC/ChB,mBAAA,CAAmG;IAA7FiB,CAAC,EAAC;EAAyF,G,wBAGrGjB,mBAAA,CAGM,OAHNqB,WAGM,GAFJrB,mBAAA,CAAkD,OAAlDsB,WAAkD,EAAAjB,gBAAA,CAAtBC,MAAA,CAAAiB,aAAa,kB,0BACzCvB,mBAAA,CAA4C;IAAvCJ,KAAK,EAAC;EAAY,GAAC,gBAAc,qB,KAI1CI,mBAAA,CAUM,OAVNwB,WAUM,G,0BATJxB,mBAAA,CAIM;IAJDJ,KAAK,EAAC;EAAW,IACpBI,mBAAA,CAEM;IAFD,aAAW,EAAC,MAAM;IAACe,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MAC/ChB,mBAAA,CAA8M;IAAxMiB,CAAC,EAAC;EAAoM,G,wBAGhNjB,mBAAA,CAGM,OAHNyB,WAGM,GAFJzB,mBAAA,CAAoD,OAApD0B,WAAoD,EAAArB,gBAAA,CAAxBC,MAAA,CAAAqB,eAAe,kB,0BAC3C3B,mBAAA,CAAqC;IAAhCJ,KAAK,EAAC;EAAY,GAAC,SAAO,qB,KAInCI,mBAAA,CAUM,OAVN4B,WAUM,G,4BATJ5B,mBAAA,CAIM;IAJDJ,KAAK,EAAC;EAAW,IACpBI,mBAAA,CAEM;IAFD,aAAW,EAAC,MAAM;IAACe,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MAC/ChB,mBAAA,CAA+H;IAAzHiB,CAAC,EAAC;EAAqH,G,wBAGjIjB,mBAAA,CAGM,OAHN6B,WAGM,GAFJ7B,mBAAA,CAAsD,OAAtD8B,WAAsD,EAAAzB,gBAAA,CAA1B0B,MAAA,CAAAC,iBAAiB,kB,0BAC7ChC,mBAAA,CAAuC;IAAlCJ,KAAK,EAAC;EAAY,GAAC,WAAS,qB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}