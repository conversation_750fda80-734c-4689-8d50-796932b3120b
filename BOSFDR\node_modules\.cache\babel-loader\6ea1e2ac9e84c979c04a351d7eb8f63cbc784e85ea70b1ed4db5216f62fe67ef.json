{"ast": null, "code": "import { createRouter, createWebHistory } from 'vue-router';\nimport unifiedAuthService from '@/services/unifiedAuthService';\n\n// Import components with error handling\nconst AdminRegistration = () => import('@/components/admin/AdminRegistration.vue');\nconst AdminDashboard = () => import('@/components/admin/AdminDashboard.vue');\nconst AdminTest = () => import('@/components/admin/AdminTest.vue');\nconst ClientRegistration = () => import('@/components/client/ClientRegistration.vue');\nconst ClientDashboard = () => import('@/components/client/ClientDashboard.vue');\nconst UnifiedLogin = () => import('@/components/UnifiedLogin.vue');\nconst WelcomePage = () => import('@/components/WelcomePage.vue');\nconst routes = [\n// Default route - redirect to welcome page\n{\n  path: '/',\n  redirect: '/welcome'\n},\n// Welcome page route\n{\n  path: '/welcome',\n  name: 'WelcomePage',\n  component: WelcomePage,\n  meta: {\n    title: 'Welcome to Barangay Bula',\n    requiresGuest: true // Only accessible when not logged in\n  }\n},\n// Unified login route\n{\n  path: '/login',\n  name: 'UnifiedLogin',\n  component: UnifiedLogin,\n  meta: {\n    title: 'Login',\n    requiresGuest: true // Only accessible when not logged in\n  }\n},\n// Client routes\n{\n  path: '/client/login',\n  redirect: '/login' // Redirect old client login to unified login\n}, {\n  path: '/client/register',\n  name: 'ClientRegistration',\n  component: ClientRegistration,\n  meta: {\n    title: 'Client Registration',\n    requiresGuest: true\n  }\n}, {\n  path: '/client/dashboard',\n  name: 'ClientDashboard',\n  component: ClientDashboard,\n  meta: {\n    title: 'Client Dashboard',\n    requiresAuth: true // Only accessible when logged in\n  }\n}, {\n  path: '/client/request/new',\n  name: 'NewDocumentRequest',\n  component: () => import('@/components/client/NewDocumentRequest.vue'),\n  meta: {\n    title: 'New Document Request',\n    requiresAuth: true\n  }\n}, {\n  path: '/client/request/barangay-clearance',\n  name: 'BarangayClearanceRequest',\n  component: () => import('@/components/client/BarangayClearanceRequest.vue'),\n  meta: {\n    title: 'Barangay Clearance Request',\n    requiresAuth: true\n  }\n}, {\n  path: '/client/request/cedula',\n  name: 'CedulaRequest',\n  component: () => import('@/components/client/CedulaRequest.vue'),\n  meta: {\n    title: 'Cedula Request',\n    requiresAuth: true\n  }\n}, {\n  path: '/client/requests',\n  name: 'MyRequests',\n  component: () => import('@/components/client/MyRequests.vue'),\n  meta: {\n    title: 'My Requests',\n    requiresAuth: true\n  }\n}, {\n  path: '/client/request/:id',\n  name: 'RequestDetails',\n  component: () => import('@/components/client/RequestDetails.vue'),\n  meta: {\n    title: 'Request Details',\n    requiresAuth: true\n  }\n},\n// Admin routes\n{\n  path: '/admin/test',\n  name: 'AdminTest',\n  component: AdminTest,\n  meta: {\n    title: 'Admin Test'\n  }\n}, {\n  path: '/admin/login',\n  redirect: '/login' // Redirect old admin login to unified login\n}, {\n  path: '/admin/register',\n  name: 'AdminRegistration',\n  component: AdminRegistration,\n  meta: {\n    title: 'Admin Registration',\n    requiresAdminGuest: true\n  }\n}, {\n  path: '/admin/dashboard',\n  name: 'AdminDashboard',\n  component: AdminDashboard,\n  meta: {\n    title: 'Admin Dashboard',\n    requiresAdminAuth: true // Only accessible when admin is logged in\n  }\n}, {\n  path: '/admin/users',\n  name: 'AdminUsers',\n  component: () => import('@/components/admin/AdminUsers.vue'),\n  meta: {\n    title: 'Manage Users',\n    requiresAdminAuth: true\n  }\n}, {\n  path: '/admin/requests',\n  name: 'AdminRequests',\n  component: () => import('@/components/admin/AdminRequests.vue'),\n  meta: {\n    title: 'View Requests',\n    requiresAdminAuth: true\n  }\n}, {\n  path: '/admin/reports',\n  name: 'AdminReports',\n  component: () => import('@/components/admin/AdminReports.vue'),\n  meta: {\n    title: 'Generate Reports',\n    requiresAdminAuth: true\n  }\n}, {\n  path: '/admin/settings',\n  name: 'AdminSettings',\n  component: () => import('@/components/admin/AdminSettings.vue'),\n  meta: {\n    title: 'System Settings',\n    requiresAdminAuth: true,\n    requiresRole: 'admin' // Only admin role can access settings\n  }\n}, {\n  path: '/admin/profile',\n  name: 'AdminProfile',\n  component: () => import('@/components/admin/AdminProfile.vue'),\n  meta: {\n    title: 'Admin Profile',\n    requiresAdminAuth: true\n  }\n}, {\n  path: '/admin/activity-logs',\n  name: 'AdminActivityLogs',\n  component: () => import('@/components/admin/AdminActivityLogs.vue'),\n  meta: {\n    title: 'Activity Logs',\n    requiresAdminAuth: true\n  }\n}, {\n  path: '/admin/audit-logs',\n  name: 'AdminAuditLogs',\n  component: () => import('@/components/admin/AdminActivityLogs.vue'),\n  // Reuse ActivityLogs for now\n  meta: {\n    title: 'Audit Logs',\n    requiresAdminAuth: true\n  }\n},\n// Catch-all route for 404\n{\n  path: '/:pathMatch(.*)*',\n  name: 'NotFound',\n  component: () => import('@/components/NotFound.vue'),\n  meta: {\n    title: 'Page Not Found'\n  }\n}];\nconst router = createRouter({\n  history: createWebHistory(),\n  routes,\n  scrollBehavior(_, __, savedPosition) {\n    if (savedPosition) {\n      return savedPosition;\n    } else {\n      return {\n        top: 0\n      };\n    }\n  }\n});\n\n// Navigation guards\nrouter.beforeEach((to, _, next) => {\n  try {\n    console.log('Navigating to:', to.path, to.name);\n\n    // Set page title\n    document.title = to.meta.title ? `${to.meta.title} - Barangay Bula` : 'Barangay Bula';\n\n    // Skip auth checks for public routes\n    if (!to.meta.requiresAuth && !to.meta.requiresAdminAuth && !to.meta.requiresGuest && !to.meta.requiresAdminGuest) {\n      console.log('Public route, skipping auth checks');\n      next();\n      return;\n    }\n\n    // Check unified authentication\n    let isLoggedIn = false;\n    let userType = null;\n    let userRole = null;\n    let currentUser = null;\n    try {\n      isLoggedIn = unifiedAuthService.isLoggedIn();\n      if (isLoggedIn) {\n        currentUser = unifiedAuthService.getCurrentUser();\n        userType = unifiedAuthService.getUserType();\n        userRole = unifiedAuthService.getUserRole();\n      }\n    } catch (error) {\n      console.warn('Unified auth check failed:', error);\n      isLoggedIn = false;\n    }\n    console.log('Navigation Guard Debug:');\n    console.log('- Route:', to.path);\n    console.log('- Logged in:', isLoggedIn);\n    console.log('- User type:', userType);\n    console.log('- User role:', userRole);\n    console.log('- Current user:', currentUser);\n    console.log('- Route meta:', to.meta);\n\n    // Admin authentication checks\n    if (to.meta.requiresAdminAuth) {\n      console.log('Admin auth required. Logged in:', isLoggedIn, 'User type:', userType);\n      if (!isLoggedIn || userType !== 'admin') {\n        console.log('Admin auth required but not logged in as admin, redirecting to login');\n        next('/login');\n        return;\n      }\n\n      // Admin role-based authorization checks\n      if (to.meta.requiresRole && userRole !== to.meta.requiresRole) {\n        console.log(`Admin role '${to.meta.requiresRole}' required but user has role '${userRole}', redirecting to dashboard`);\n        next('/admin/dashboard');\n        return;\n      }\n    }\n\n    // Client authentication checks\n    if (to.meta.requiresAuth) {\n      console.log('Client auth required. Logged in:', isLoggedIn, 'User type:', userType);\n      if (!isLoggedIn || userType !== 'client') {\n        console.log('Client auth required but not logged in as client, redirecting to login');\n        next('/login');\n        return;\n      }\n    }\n\n    // Guest route checks - redirect logged in users to appropriate dashboard\n    if (to.meta.requiresGuest || to.meta.requiresAdminGuest) {\n      if (isLoggedIn) {\n        console.log('Guest route but user is logged in, redirecting to appropriate dashboard');\n        const redirectUrl = unifiedAuthService.getRedirectUrl(unifiedAuthService.getCurrentUser());\n        next(redirectUrl);\n        return;\n      }\n    }\n    console.log('Navigation successful to:', to.path);\n    next();\n  } catch (error) {\n    console.error('Navigation guard error:', error);\n    // Continue navigation even if there's an error to prevent infinite loops\n    next();\n  }\n});\nexport default router;", "map": {"version": 3, "names": ["createRouter", "createWebHistory", "unifiedAuthService", "AdminRegistration", "AdminDashboard", "AdminTest", "ClientRegistration", "ClientDashboard", "UnifiedLogin", "WelcomePage", "routes", "path", "redirect", "name", "component", "meta", "title", "requiresGuest", "requiresAuth", "requiresAdminGuest", "requiresAdminAuth", "requiresRole", "router", "history", "scroll<PERSON>eh<PERSON>or", "_", "__", "savedPosition", "top", "beforeEach", "to", "next", "console", "log", "document", "isLoggedIn", "userType", "userRole", "currentUser", "getCurrentUser", "getUserType", "getUserRole", "error", "warn", "redirectUrl", "getRedirectUrl"], "sources": ["D:/cap2_rhai_front_and_back/BOSFDR/src/router/index.js"], "sourcesContent": ["import { createRouter, createWebHistory } from 'vue-router';\nimport unifiedAuthService from '@/services/unifiedAuthService';\n\n// Import components with error handling\nconst AdminRegistration = () => import('@/components/admin/AdminRegistration.vue');\nconst AdminDashboard = () => import('@/components/admin/AdminDashboard.vue');\nconst AdminTest = () => import('@/components/admin/AdminTest.vue');\nconst ClientRegistration = () => import('@/components/client/ClientRegistration.vue');\nconst ClientDashboard = () => import('@/components/client/ClientDashboard.vue');\nconst UnifiedLogin = () => import('@/components/UnifiedLogin.vue');\nconst WelcomePage = () => import('@/components/WelcomePage.vue');\n\nconst routes = [\n  // Default route - redirect to welcome page\n  {\n    path: '/',\n    redirect: '/welcome'\n  },\n\n  // Welcome page route\n  {\n    path: '/welcome',\n    name: 'WelcomePage',\n    component: WelcomePage,\n    meta: {\n      title: 'Welcome to Barangay Bula',\n      requiresGuest: true // Only accessible when not logged in\n    }\n  },\n\n  // Unified login route\n  {\n    path: '/login',\n    name: 'UnifiedLogin',\n    component: UnifiedLogin,\n    meta: {\n      title: 'Login',\n      requiresGuest: true // Only accessible when not logged in\n    }\n  },\n  \n  // Client routes\n  {\n    path: '/client/login',\n    redirect: '/login' // Redirect old client login to unified login\n  },\n  {\n    path: '/client/register',\n    name: 'ClientRegistration',\n    component: ClientRegistration,\n    meta: { \n      title: 'Client Registration',\n      requiresGuest: true\n    }\n  },\n  {\n    path: '/client/dashboard',\n    name: 'ClientDashboard',\n    component: ClientDashboard,\n    meta: {\n      title: 'Client Dashboard',\n      requiresAuth: true // Only accessible when logged in\n    }\n  },\n  {\n    path: '/client/request/new',\n    name: 'NewDocumentRequest',\n    component: () => import('@/components/client/NewDocumentRequest.vue'),\n    meta: {\n      title: 'New Document Request',\n      requiresAuth: true\n    }\n  },\n  {\n    path: '/client/request/barangay-clearance',\n    name: 'BarangayClearanceRequest',\n    component: () => import('@/components/client/BarangayClearanceRequest.vue'),\n    meta: {\n      title: 'Barangay Clearance Request',\n      requiresAuth: true\n    }\n  },\n  {\n    path: '/client/request/cedula',\n    name: 'CedulaRequest',\n    component: () => import('@/components/client/CedulaRequest.vue'),\n    meta: {\n      title: 'Cedula Request',\n      requiresAuth: true\n    }\n  },\n  {\n    path: '/client/requests',\n    name: 'MyRequests',\n    component: () => import('@/components/client/MyRequests.vue'),\n    meta: {\n      title: 'My Requests',\n      requiresAuth: true\n    }\n  },\n  {\n    path: '/client/request/:id',\n    name: 'RequestDetails',\n    component: () => import('@/components/client/RequestDetails.vue'),\n    meta: {\n      title: 'Request Details',\n      requiresAuth: true\n    }\n  },\n  \n  // Admin routes\n  {\n    path: '/admin/test',\n    name: 'AdminTest',\n    component: AdminTest,\n    meta: {\n      title: 'Admin Test'\n    }\n  },\n  {\n    path: '/admin/login',\n    redirect: '/login' // Redirect old admin login to unified login\n  },\n  {\n    path: '/admin/register',\n    name: 'AdminRegistration',\n    component: AdminRegistration,\n    meta: {\n      title: 'Admin Registration',\n      requiresAdminGuest: true\n    }\n  },\n  {\n    path: '/admin/dashboard',\n    name: 'AdminDashboard',\n    component: AdminDashboard,\n    meta: {\n      title: 'Admin Dashboard',\n      requiresAdminAuth: true // Only accessible when admin is logged in\n    }\n  },\n  {\n    path: '/admin/users',\n    name: 'AdminUsers',\n    component: () => import('@/components/admin/AdminUsers.vue'),\n    meta: {\n      title: 'Manage Users',\n      requiresAdminAuth: true\n    }\n  },\n  {\n    path: '/admin/requests',\n    name: 'AdminRequests',\n    component: () => import('@/components/admin/AdminRequests.vue'),\n    meta: {\n      title: 'View Requests',\n      requiresAdminAuth: true\n    }\n  },\n  {\n    path: '/admin/reports',\n    name: 'AdminReports',\n    component: () => import('@/components/admin/AdminReports.vue'),\n    meta: {\n      title: 'Generate Reports',\n      requiresAdminAuth: true\n    }\n  },\n  {\n    path: '/admin/settings',\n    name: 'AdminSettings',\n    component: () => import('@/components/admin/AdminSettings.vue'),\n    meta: {\n      title: 'System Settings',\n      requiresAdminAuth: true,\n      requiresRole: 'admin' // Only admin role can access settings\n    }\n  },\n  {\n    path: '/admin/profile',\n    name: 'AdminProfile',\n    component: () => import('@/components/admin/AdminProfile.vue'),\n    meta: {\n      title: 'Admin Profile',\n      requiresAdminAuth: true\n    }\n  },\n  {\n    path: '/admin/activity-logs',\n    name: 'AdminActivityLogs',\n    component: () => import('@/components/admin/AdminActivityLogs.vue'),\n    meta: {\n      title: 'Activity Logs',\n      requiresAdminAuth: true\n    }\n  },\n  {\n    path: '/admin/audit-logs',\n    name: 'AdminAuditLogs',\n    component: () => import('@/components/admin/AdminActivityLogs.vue'), // Reuse ActivityLogs for now\n    meta: {\n      title: 'Audit Logs',\n      requiresAdminAuth: true\n    }\n  },\n  \n  // Catch-all route for 404\n  {\n    path: '/:pathMatch(.*)*',\n    name: 'NotFound',\n    component: () => import('@/components/NotFound.vue'),\n    meta: { \n      title: 'Page Not Found'\n    }\n  }\n];\n\nconst router = createRouter({\n  history: createWebHistory(),\n  routes,\n  scrollBehavior(_, __, savedPosition) {\n    if (savedPosition) {\n      return savedPosition;\n    } else {\n      return { top: 0 };\n    }\n  }\n});\n\n// Navigation guards\nrouter.beforeEach((to, _, next) => {\n  try {\n    console.log('Navigating to:', to.path, to.name);\n\n    // Set page title\n    document.title = to.meta.title ? `${to.meta.title} - Barangay Bula` : 'Barangay Bula';\n\n    // Skip auth checks for public routes\n    if (!to.meta.requiresAuth && !to.meta.requiresAdminAuth && !to.meta.requiresGuest && !to.meta.requiresAdminGuest) {\n      console.log('Public route, skipping auth checks');\n      next();\n      return;\n    }\n\n    // Check unified authentication\n    let isLoggedIn = false;\n    let userType = null;\n    let userRole = null;\n    let currentUser = null;\n\n    try {\n      isLoggedIn = unifiedAuthService.isLoggedIn();\n      if (isLoggedIn) {\n        currentUser = unifiedAuthService.getCurrentUser();\n        userType = unifiedAuthService.getUserType();\n        userRole = unifiedAuthService.getUserRole();\n      }\n    } catch (error) {\n      console.warn('Unified auth check failed:', error);\n      isLoggedIn = false;\n    }\n\n    console.log('Navigation Guard Debug:');\n    console.log('- Route:', to.path);\n    console.log('- Logged in:', isLoggedIn);\n    console.log('- User type:', userType);\n    console.log('- User role:', userRole);\n    console.log('- Current user:', currentUser);\n    console.log('- Route meta:', to.meta);\n\n    // Admin authentication checks\n    if (to.meta.requiresAdminAuth) {\n      console.log('Admin auth required. Logged in:', isLoggedIn, 'User type:', userType);\n      if (!isLoggedIn || userType !== 'admin') {\n        console.log('Admin auth required but not logged in as admin, redirecting to login');\n        next('/login');\n        return;\n      }\n\n      // Admin role-based authorization checks\n      if (to.meta.requiresRole && userRole !== to.meta.requiresRole) {\n        console.log(`Admin role '${to.meta.requiresRole}' required but user has role '${userRole}', redirecting to dashboard`);\n        next('/admin/dashboard');\n        return;\n      }\n    }\n\n    // Client authentication checks\n    if (to.meta.requiresAuth) {\n      console.log('Client auth required. Logged in:', isLoggedIn, 'User type:', userType);\n      if (!isLoggedIn || userType !== 'client') {\n        console.log('Client auth required but not logged in as client, redirecting to login');\n        next('/login');\n        return;\n      }\n    }\n\n    // Guest route checks - redirect logged in users to appropriate dashboard\n    if (to.meta.requiresGuest || to.meta.requiresAdminGuest) {\n      if (isLoggedIn) {\n        console.log('Guest route but user is logged in, redirecting to appropriate dashboard');\n        const redirectUrl = unifiedAuthService.getRedirectUrl(unifiedAuthService.getCurrentUser());\n        next(redirectUrl);\n        return;\n      }\n    }\n\n    console.log('Navigation successful to:', to.path);\n    next();\n  } catch (error) {\n    console.error('Navigation guard error:', error);\n    // Continue navigation even if there's an error to prevent infinite loops\n    next();\n  }\n});\n\nexport default router;\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AAC3D,OAAOC,kBAAkB,MAAM,+BAA+B;;AAE9D;AACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM,MAAM,CAAC,0CAA0C,CAAC;AAClF,MAAMC,cAAc,GAAGA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC;AAC5E,MAAMC,SAAS,GAAGA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC;AAClE,MAAMC,kBAAkB,GAAGA,CAAA,KAAM,MAAM,CAAC,4CAA4C,CAAC;AACrF,MAAMC,eAAe,GAAGA,CAAA,KAAM,MAAM,CAAC,yCAAyC,CAAC;AAC/E,MAAMC,YAAY,GAAGA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC;AAClE,MAAMC,WAAW,GAAGA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC;AAEhE,MAAMC,MAAM,GAAG;AACb;AACA;EACEC,IAAI,EAAE,GAAG;EACTC,QAAQ,EAAE;AACZ,CAAC;AAED;AACA;EACED,IAAI,EAAE,UAAU;EAChBE,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEL,WAAW;EACtBM,IAAI,EAAE;IACJC,KAAK,EAAE,0BAA0B;IACjCC,aAAa,EAAE,IAAI,CAAC;EACtB;AACF,CAAC;AAED;AACA;EACEN,IAAI,EAAE,QAAQ;EACdE,IAAI,EAAE,cAAc;EACpBC,SAAS,EAAEN,YAAY;EACvBO,IAAI,EAAE;IACJC,KAAK,EAAE,OAAO;IACdC,aAAa,EAAE,IAAI,CAAC;EACtB;AACF,CAAC;AAED;AACA;EACEN,IAAI,EAAE,eAAe;EACrBC,QAAQ,EAAE,QAAQ,CAAC;AACrB,CAAC,EACD;EACED,IAAI,EAAE,kBAAkB;EACxBE,IAAI,EAAE,oBAAoB;EAC1BC,SAAS,EAAER,kBAAkB;EAC7BS,IAAI,EAAE;IACJC,KAAK,EAAE,qBAAqB;IAC5BC,aAAa,EAAE;EACjB;AACF,CAAC,EACD;EACEN,IAAI,EAAE,mBAAmB;EACzBE,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAEP,eAAe;EAC1BQ,IAAI,EAAE;IACJC,KAAK,EAAE,kBAAkB;IACzBE,YAAY,EAAE,IAAI,CAAC;EACrB;AACF,CAAC,EACD;EACEP,IAAI,EAAE,qBAAqB;EAC3BE,IAAI,EAAE,oBAAoB;EAC1BC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,4CAA4C,CAAC;EACrEC,IAAI,EAAE;IACJC,KAAK,EAAE,sBAAsB;IAC7BE,YAAY,EAAE;EAChB;AACF,CAAC,EACD;EACEP,IAAI,EAAE,oCAAoC;EAC1CE,IAAI,EAAE,0BAA0B;EAChCC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kDAAkD,CAAC;EAC3EC,IAAI,EAAE;IACJC,KAAK,EAAE,4BAA4B;IACnCE,YAAY,EAAE;EAChB;AACF,CAAC,EACD;EACEP,IAAI,EAAE,wBAAwB;EAC9BE,IAAI,EAAE,eAAe;EACrBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC;EAChEC,IAAI,EAAE;IACJC,KAAK,EAAE,gBAAgB;IACvBE,YAAY,EAAE;EAChB;AACF,CAAC,EACD;EACEP,IAAI,EAAE,kBAAkB;EACxBE,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC;EAC7DC,IAAI,EAAE;IACJC,KAAK,EAAE,aAAa;IACpBE,YAAY,EAAE;EAChB;AACF,CAAC,EACD;EACEP,IAAI,EAAE,qBAAqB;EAC3BE,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC;EACjEC,IAAI,EAAE;IACJC,KAAK,EAAE,iBAAiB;IACxBE,YAAY,EAAE;EAChB;AACF,CAAC;AAED;AACA;EACEP,IAAI,EAAE,aAAa;EACnBE,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAET,SAAS;EACpBU,IAAI,EAAE;IACJC,KAAK,EAAE;EACT;AACF,CAAC,EACD;EACEL,IAAI,EAAE,cAAc;EACpBC,QAAQ,EAAE,QAAQ,CAAC;AACrB,CAAC,EACD;EACED,IAAI,EAAE,iBAAiB;EACvBE,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEX,iBAAiB;EAC5BY,IAAI,EAAE;IACJC,KAAK,EAAE,oBAAoB;IAC3BG,kBAAkB,EAAE;EACtB;AACF,CAAC,EACD;EACER,IAAI,EAAE,kBAAkB;EACxBE,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEV,cAAc;EACzBW,IAAI,EAAE;IACJC,KAAK,EAAE,iBAAiB;IACxBI,iBAAiB,EAAE,IAAI,CAAC;EAC1B;AACF,CAAC,EACD;EACET,IAAI,EAAE,cAAc;EACpBE,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC;EAC5DC,IAAI,EAAE;IACJC,KAAK,EAAE,cAAc;IACrBI,iBAAiB,EAAE;EACrB;AACF,CAAC,EACD;EACET,IAAI,EAAE,iBAAiB;EACvBE,IAAI,EAAE,eAAe;EACrBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC;EAC/DC,IAAI,EAAE;IACJC,KAAK,EAAE,eAAe;IACtBI,iBAAiB,EAAE;EACrB;AACF,CAAC,EACD;EACET,IAAI,EAAE,gBAAgB;EACtBE,IAAI,EAAE,cAAc;EACpBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC;EAC9DC,IAAI,EAAE;IACJC,KAAK,EAAE,kBAAkB;IACzBI,iBAAiB,EAAE;EACrB;AACF,CAAC,EACD;EACET,IAAI,EAAE,iBAAiB;EACvBE,IAAI,EAAE,eAAe;EACrBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC;EAC/DC,IAAI,EAAE;IACJC,KAAK,EAAE,iBAAiB;IACxBI,iBAAiB,EAAE,IAAI;IACvBC,YAAY,EAAE,OAAO,CAAC;EACxB;AACF,CAAC,EACD;EACEV,IAAI,EAAE,gBAAgB;EACtBE,IAAI,EAAE,cAAc;EACpBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC;EAC9DC,IAAI,EAAE;IACJC,KAAK,EAAE,eAAe;IACtBI,iBAAiB,EAAE;EACrB;AACF,CAAC,EACD;EACET,IAAI,EAAE,sBAAsB;EAC5BE,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,0CAA0C,CAAC;EACnEC,IAAI,EAAE;IACJC,KAAK,EAAE,eAAe;IACtBI,iBAAiB,EAAE;EACrB;AACF,CAAC,EACD;EACET,IAAI,EAAE,mBAAmB;EACzBE,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,0CAA0C,CAAC;EAAE;EACrEC,IAAI,EAAE;IACJC,KAAK,EAAE,YAAY;IACnBI,iBAAiB,EAAE;EACrB;AACF,CAAC;AAED;AACA;EACET,IAAI,EAAE,kBAAkB;EACxBE,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC;EACpDC,IAAI,EAAE;IACJC,KAAK,EAAE;EACT;AACF,CAAC,CACF;AAED,MAAMM,MAAM,GAAGtB,YAAY,CAAC;EAC1BuB,OAAO,EAAEtB,gBAAgB,CAAC,CAAC;EAC3BS,MAAM;EACNc,cAAcA,CAACC,CAAC,EAAEC,EAAE,EAAEC,aAAa,EAAE;IACnC,IAAIA,aAAa,EAAE;MACjB,OAAOA,aAAa;IACtB,CAAC,MAAM;MACL,OAAO;QAAEC,GAAG,EAAE;MAAE,CAAC;IACnB;EACF;AACF,CAAC,CAAC;;AAEF;AACAN,MAAM,CAACO,UAAU,CAAC,CAACC,EAAE,EAAEL,CAAC,EAAEM,IAAI,KAAK;EACjC,IAAI;IACFC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEH,EAAE,CAACnB,IAAI,EAAEmB,EAAE,CAACjB,IAAI,CAAC;;IAE/C;IACAqB,QAAQ,CAAClB,KAAK,GAAGc,EAAE,CAACf,IAAI,CAACC,KAAK,GAAG,GAAGc,EAAE,CAACf,IAAI,CAACC,KAAK,kBAAkB,GAAG,eAAe;;IAErF;IACA,IAAI,CAACc,EAAE,CAACf,IAAI,CAACG,YAAY,IAAI,CAACY,EAAE,CAACf,IAAI,CAACK,iBAAiB,IAAI,CAACU,EAAE,CAACf,IAAI,CAACE,aAAa,IAAI,CAACa,EAAE,CAACf,IAAI,CAACI,kBAAkB,EAAE;MAChHa,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjDF,IAAI,CAAC,CAAC;MACN;IACF;;IAEA;IACA,IAAII,UAAU,GAAG,KAAK;IACtB,IAAIC,QAAQ,GAAG,IAAI;IACnB,IAAIC,QAAQ,GAAG,IAAI;IACnB,IAAIC,WAAW,GAAG,IAAI;IAEtB,IAAI;MACFH,UAAU,GAAGjC,kBAAkB,CAACiC,UAAU,CAAC,CAAC;MAC5C,IAAIA,UAAU,EAAE;QACdG,WAAW,GAAGpC,kBAAkB,CAACqC,cAAc,CAAC,CAAC;QACjDH,QAAQ,GAAGlC,kBAAkB,CAACsC,WAAW,CAAC,CAAC;QAC3CH,QAAQ,GAAGnC,kBAAkB,CAACuC,WAAW,CAAC,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdV,OAAO,CAACW,IAAI,CAAC,4BAA4B,EAAED,KAAK,CAAC;MACjDP,UAAU,GAAG,KAAK;IACpB;IAEAH,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACtCD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEH,EAAE,CAACnB,IAAI,CAAC;IAChCqB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEE,UAAU,CAAC;IACvCH,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEG,QAAQ,CAAC;IACrCJ,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEI,QAAQ,CAAC;IACrCL,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEK,WAAW,CAAC;IAC3CN,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEH,EAAE,CAACf,IAAI,CAAC;;IAErC;IACA,IAAIe,EAAE,CAACf,IAAI,CAACK,iBAAiB,EAAE;MAC7BY,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEE,UAAU,EAAE,YAAY,EAAEC,QAAQ,CAAC;MAClF,IAAI,CAACD,UAAU,IAAIC,QAAQ,KAAK,OAAO,EAAE;QACvCJ,OAAO,CAACC,GAAG,CAAC,sEAAsE,CAAC;QACnFF,IAAI,CAAC,QAAQ,CAAC;QACd;MACF;;MAEA;MACA,IAAID,EAAE,CAACf,IAAI,CAACM,YAAY,IAAIgB,QAAQ,KAAKP,EAAE,CAACf,IAAI,CAACM,YAAY,EAAE;QAC7DW,OAAO,CAACC,GAAG,CAAC,eAAeH,EAAE,CAACf,IAAI,CAACM,YAAY,iCAAiCgB,QAAQ,6BAA6B,CAAC;QACtHN,IAAI,CAAC,kBAAkB,CAAC;QACxB;MACF;IACF;;IAEA;IACA,IAAID,EAAE,CAACf,IAAI,CAACG,YAAY,EAAE;MACxBc,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEE,UAAU,EAAE,YAAY,EAAEC,QAAQ,CAAC;MACnF,IAAI,CAACD,UAAU,IAAIC,QAAQ,KAAK,QAAQ,EAAE;QACxCJ,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;QACrFF,IAAI,CAAC,QAAQ,CAAC;QACd;MACF;IACF;;IAEA;IACA,IAAID,EAAE,CAACf,IAAI,CAACE,aAAa,IAAIa,EAAE,CAACf,IAAI,CAACI,kBAAkB,EAAE;MACvD,IAAIgB,UAAU,EAAE;QACdH,OAAO,CAACC,GAAG,CAAC,yEAAyE,CAAC;QACtF,MAAMW,WAAW,GAAG1C,kBAAkB,CAAC2C,cAAc,CAAC3C,kBAAkB,CAACqC,cAAc,CAAC,CAAC,CAAC;QAC1FR,IAAI,CAACa,WAAW,CAAC;QACjB;MACF;IACF;IAEAZ,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEH,EAAE,CAACnB,IAAI,CAAC;IACjDoB,IAAI,CAAC,CAAC;EACR,CAAC,CAAC,OAAOW,KAAK,EAAE;IACdV,OAAO,CAACU,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C;IACAX,IAAI,CAAC,CAAC;EACR;AACF,CAAC,CAAC;AAEF,eAAeT,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}