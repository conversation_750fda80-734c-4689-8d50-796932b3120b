{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createTextVNode as _createTextVNode, normalizeClass as _normalizeClass, createStaticVNode as _createStaticVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"client-dashboard\"\n};\nconst _hoisted_2 = {\n  class: \"info-section\"\n};\nconst _hoisted_3 = {\n  class: \"container\"\n};\nconst _hoisted_4 = {\n  class: \"info-grid\"\n};\nconst _hoisted_5 = {\n  class: \"info-card help-card\"\n};\nconst _hoisted_6 = {\n  class: \"card-content\"\n};\nconst _hoisted_7 = {\n  class: \"help-actions\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_ClientHeader = _resolveComponent(\"ClientHeader\");\n  const _component_HeroSection = _resolveComponent(\"HeroSection\");\n  const _component_QuickActionsSection = _resolveComponent(\"QuickActionsSection\");\n  const _component_DocumentServicesSection = _resolveComponent(\"DocumentServicesSection\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" Background \"), _cache[8] || (_cache[8] = _createElementVNode(\"div\", {\n    class: \"background-container\"\n  }, [_createElementVNode(\"div\", {\n    class: \"background-image\"\n  }), _createElementVNode(\"div\", {\n    class: \"background-overlay\"\n  })], -1 /* HOISTED */)), _createCommentVNode(\" Client Header with Navigation \"), _createVNode(_component_ClientHeader, {\n    userName: $data.userName,\n    userEmail: $data.userEmail,\n    userAvatar: $data.userAvatar,\n    showUserDropdown: $data.showUserDropdown,\n    sidebarCollapsed: $data.sidebarCollapsed,\n    activeMenu: $data.activeMenu,\n    showBreadcrumbs: true,\n    onSidebarToggle: $options.handleSidebarToggle,\n    onUserDropdownToggle: $options.handleUserDropdownToggle,\n    onMenuAction: $options.handleMenuAction,\n    onLogout: $options.handleLogout,\n    onError: $options.handleError,\n    onSearch: $options.handleSearch\n  }, null, 8 /* PROPS */, [\"userName\", \"userEmail\", \"userAvatar\", \"showUserDropdown\", \"sidebarCollapsed\", \"activeMenu\", \"onSidebarToggle\", \"onUserDropdownToggle\", \"onMenuAction\", \"onLogout\", \"onError\", \"onSearch\"]), _createCommentVNode(\" Main Content \"), _createElementVNode(\"main\", {\n    id: \"main-content\",\n    class: _normalizeClass([\"main-content\", {\n      'sidebar-collapsed': $data.sidebarCollapsed\n    }])\n  }, [_createCommentVNode(\" Hero Section \"), _createVNode(_component_HeroSection, {\n    firstName: $data.firstName,\n    totalRequests: $data.totalRequests,\n    pendingRequests: $data.pendingRequests,\n    onStartNewRequest: $options.scrollToServices,\n    onViewRequests: $options.goToMyRequests\n  }, null, 8 /* PROPS */, [\"firstName\", \"totalRequests\", \"pendingRequests\", \"onStartNewRequest\", \"onViewRequests\"]), _createCommentVNode(\" Quick Actions Section \"), _createVNode(_component_QuickActionsSection, {\n    onStartNewRequest: $options.scrollToServices,\n    onViewRequests: $options.goToMyRequests,\n    onViewDocuments: _ctx.goToMyDocuments,\n    onGetHelp: $options.openHelp\n  }, null, 8 /* PROPS */, [\"onStartNewRequest\", \"onViewRequests\", \"onViewDocuments\", \"onGetHelp\"]), _createCommentVNode(\" Document Services Section \"), _createVNode(_component_DocumentServicesSection, {\n    ref: \"servicesSection\",\n    documentTypes: $data.documentTypes,\n    loading: $data.loading,\n    error: $data.error,\n    onSelectDocumentType: $options.selectDocumentType,\n    onRetry: $options.loadDocumentTypes\n  }, null, 8 /* PROPS */, [\"documentTypes\", \"loading\", \"error\", \"onSelectDocumentType\", \"onRetry\"]), _createCommentVNode(\" Information and Help Section \"), _createElementVNode(\"section\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createCommentVNode(\" Requirements Card \"), _cache[6] || (_cache[6] = _createStaticVNode(\"<div class=\\\"info-card requirements-card\\\" data-v-b2da9790><div class=\\\"card-header\\\" data-v-b2da9790><i class=\\\"fas fa-clipboard-list\\\" aria-hidden=\\\"true\\\" data-v-b2da9790></i><h3 data-v-b2da9790>Before You Start</h3></div><div class=\\\"card-content\\\" data-v-b2da9790><ul class=\\\"requirements-list\\\" data-v-b2da9790><li class=\\\"requirement-item\\\" data-v-b2da9790><i class=\\\"fas fa-check-circle\\\" aria-hidden=\\\"true\\\" data-v-b2da9790></i><span data-v-b2da9790>Complete and accurate profile information</span></li><li class=\\\"requirement-item\\\" data-v-b2da9790><i class=\\\"fas fa-check-circle\\\" aria-hidden=\\\"true\\\" data-v-b2da9790></i><span data-v-b2da9790>Valid government-issued ID ready for upload</span></li><li class=\\\"requirement-item\\\" data-v-b2da9790><i class=\\\"fas fa-check-circle\\\" aria-hidden=\\\"true\\\" data-v-b2da9790></i><span data-v-b2da9790>Supporting documents (if required)</span></li><li class=\\\"requirement-item\\\" data-v-b2da9790><i class=\\\"fas fa-check-circle\\\" aria-hidden=\\\"true\\\" data-v-b2da9790></i><span data-v-b2da9790>Payment method for processing fees</span></li></ul></div></div>\", 1)), _createCommentVNode(\" Process Card \"), _cache[7] || (_cache[7] = _createStaticVNode(\"<div class=\\\"info-card process-card\\\" data-v-b2da9790><div class=\\\"card-header\\\" data-v-b2da9790><i class=\\\"fas fa-route\\\" aria-hidden=\\\"true\\\" data-v-b2da9790></i><h3 data-v-b2da9790>How It Works</h3></div><div class=\\\"card-content\\\" data-v-b2da9790><ol class=\\\"process-steps\\\" data-v-b2da9790><li class=\\\"process-step\\\" data-v-b2da9790><div class=\\\"step-number\\\" data-v-b2da9790>1</div><div class=\\\"step-content\\\" data-v-b2da9790><strong data-v-b2da9790>Select Document</strong><span data-v-b2da9790>Choose the document type you need</span></div></li><li class=\\\"process-step\\\" data-v-b2da9790><div class=\\\"step-number\\\" data-v-b2da9790>2</div><div class=\\\"step-content\\\" data-v-b2da9790><strong data-v-b2da9790>Fill Application</strong><span data-v-b2da9790>Complete the required information</span></div></li><li class=\\\"process-step\\\" data-v-b2da9790><div class=\\\"step-number\\\" data-v-b2da9790>3</div><div class=\\\"step-content\\\" data-v-b2da9790><strong data-v-b2da9790>Submit &amp; Pay</strong><span data-v-b2da9790>Review and submit with payment</span></div></li><li class=\\\"process-step\\\" data-v-b2da9790><div class=\\\"step-number\\\" data-v-b2da9790>4</div><div class=\\\"step-content\\\" data-v-b2da9790><strong data-v-b2da9790>Track Progress</strong><span data-v-b2da9790>Monitor your request status</span></div></li></ol></div></div>\", 1)), _createCommentVNode(\" Help Card \"), _createElementVNode(\"div\", _hoisted_5, [_cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n    class: \"card-header\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-headset\",\n    \"aria-hidden\": \"true\"\n  }), _createElementVNode(\"h3\", null, \"Need Assistance?\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_6, [_cache[4] || (_cache[4] = _createElementVNode(\"p\", {\n    class: \"help-description\"\n  }, \"Our support team is here to help you with any questions about document requirements or the application process.\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"button\", {\n    class: \"btn btn-outline help-btn\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.openHelp && $options.openHelp(...args))\n  }, _cache[2] || (_cache[2] = [_createElementVNode(\"i\", {\n    class: \"fas fa-question-circle\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" View FAQ \")])), _createElementVNode(\"button\", {\n    class: \"btn btn-outline contact-btn\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.contactSupport && $options.contactSupport(...args))\n  }, _cache[3] || (_cache[3] = [_createElementVNode(\"i\", {\n    class: \"fas fa-phone\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Contact Support \")]))])])])])])])], 2 /* CLASS */)]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_createVNode", "_component_ClientHeader", "userName", "$data", "userEmail", "userAvatar", "showUserDropdown", "sidebarCollapsed", "activeMenu", "showBreadcrumbs", "onSidebarToggle", "$options", "handleSidebarToggle", "onUserDropdownToggle", "handleUserDropdownToggle", "onMenuAction", "handleMenuAction", "onLogout", "handleLogout", "onError", "handleError", "onSearch", "handleSearch", "id", "_normalizeClass", "_component_HeroSection", "firstName", "totalRequests", "pendingRequests", "onStartNewRequest", "scrollToServices", "onViewRequests", "goToMyRequests", "_component_QuickActionsSection", "onViewDocuments", "_ctx", "goToMyDocuments", "onGetHelp", "openHelp", "_component_DocumentServicesSection", "ref", "documentTypes", "loading", "error", "onSelectDocumentType", "selectDocumentType", "onRetry", "loadDocumentTypes", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "onClick", "_cache", "args", "contactSupport"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\client\\NewDocumentRequest.vue"], "sourcesContent": ["<template>\n  <div class=\"client-dashboard\">\n    <!-- Background -->\n    <div class=\"background-container\">\n      <div class=\"background-image\"></div>\n      <div class=\"background-overlay\"></div>\n    </div>\n\n    <!-- Client Header with Navigation -->\n    <ClientHeader\n      :userName=\"userName\"\n      :userEmail=\"userEmail\"\n      :userAvatar=\"userAvatar\"\n      :showUserDropdown=\"showUserDropdown\"\n      :sidebarCollapsed=\"sidebarCollapsed\"\n      :activeMenu=\"activeMenu\"\n      :showBreadcrumbs=\"true\"\n      @sidebar-toggle=\"handleSidebarToggle\"\n      @user-dropdown-toggle=\"handleUserDropdownToggle\"\n      @menu-action=\"handleMenuAction\"\n      @logout=\"handleLogout\"\n      @error=\"handleError\"\n      @search=\"handleSearch\"\n    />\n\n    <!-- Main Content -->\n    <main id=\"main-content\" class=\"main-content\" :class=\"{ 'sidebar-collapsed': sidebarCollapsed }\">\n      <!-- Hero Section -->\n      <HeroSection\n        :firstName=\"firstName\"\n        :totalRequests=\"totalRequests\"\n        :pendingRequests=\"pendingRequests\"\n        @start-new-request=\"scrollToServices\"\n        @view-requests=\"goToMyRequests\"\n      />\n\n      <!-- Quick Actions Section -->\n      <QuickActionsSection\n        @start-new-request=\"scrollToServices\"\n        @view-requests=\"goToMyRequests\"\n        @view-documents=\"goToMyDocuments\"\n        @get-help=\"openHelp\"\n      />\n\n      <!-- Document Services Section -->\n      <DocumentServicesSection\n        ref=\"servicesSection\"\n        :documentTypes=\"documentTypes\"\n        :loading=\"loading\"\n        :error=\"error\"\n        @select-document-type=\"selectDocumentType\"\n        @retry=\"loadDocumentTypes\"\n      />\n\n      <!-- Information and Help Section -->\n      <section class=\"info-section\">\n        <div class=\"container\">\n          <div class=\"info-grid\">\n            <!-- Requirements Card -->\n            <div class=\"info-card requirements-card\">\n              <div class=\"card-header\">\n                <i class=\"fas fa-clipboard-list\" aria-hidden=\"true\"></i>\n                <h3>Before You Start</h3>\n              </div>\n              <div class=\"card-content\">\n                <ul class=\"requirements-list\">\n                  <li class=\"requirement-item\">\n                    <i class=\"fas fa-check-circle\" aria-hidden=\"true\"></i>\n                    <span>Complete and accurate profile information</span>\n                  </li>\n                  <li class=\"requirement-item\">\n                    <i class=\"fas fa-check-circle\" aria-hidden=\"true\"></i>\n                    <span>Valid government-issued ID ready for upload</span>\n                  </li>\n                  <li class=\"requirement-item\">\n                    <i class=\"fas fa-check-circle\" aria-hidden=\"true\"></i>\n                    <span>Supporting documents (if required)</span>\n                  </li>\n                  <li class=\"requirement-item\">\n                    <i class=\"fas fa-check-circle\" aria-hidden=\"true\"></i>\n                    <span>Payment method for processing fees</span>\n                  </li>\n                </ul>\n              </div>\n            </div>\n\n            <!-- Process Card -->\n            <div class=\"info-card process-card\">\n              <div class=\"card-header\">\n                <i class=\"fas fa-route\" aria-hidden=\"true\"></i>\n                <h3>How It Works</h3>\n              </div>\n              <div class=\"card-content\">\n                <ol class=\"process-steps\">\n                  <li class=\"process-step\">\n                    <div class=\"step-number\">1</div>\n                    <div class=\"step-content\">\n                      <strong>Select Document</strong>\n                      <span>Choose the document type you need</span>\n                    </div>\n                  </li>\n                  <li class=\"process-step\">\n                    <div class=\"step-number\">2</div>\n                    <div class=\"step-content\">\n                      <strong>Fill Application</strong>\n                      <span>Complete the required information</span>\n                    </div>\n                  </li>\n                  <li class=\"process-step\">\n                    <div class=\"step-number\">3</div>\n                    <div class=\"step-content\">\n                      <strong>Submit & Pay</strong>\n                      <span>Review and submit with payment</span>\n                    </div>\n                  </li>\n                  <li class=\"process-step\">\n                    <div class=\"step-number\">4</div>\n                    <div class=\"step-content\">\n                      <strong>Track Progress</strong>\n                      <span>Monitor your request status</span>\n                    </div>\n                  </li>\n                </ol>\n              </div>\n            </div>\n\n            <!-- Help Card -->\n            <div class=\"info-card help-card\">\n              <div class=\"card-header\">\n                <i class=\"fas fa-headset\" aria-hidden=\"true\"></i>\n                <h3>Need Assistance?</h3>\n              </div>\n              <div class=\"card-content\">\n                <p class=\"help-description\">Our support team is here to help you with any questions about document requirements or the application process.</p>\n                <div class=\"help-actions\">\n                  <button class=\"btn btn-outline help-btn\" @click=\"openHelp\">\n                    <i class=\"fas fa-question-circle\" aria-hidden=\"true\"></i>\n                    View FAQ\n                  </button>\n                  <button class=\"btn btn-outline contact-btn\" @click=\"contactSupport\">\n                    <i class=\"fas fa-phone\" aria-hidden=\"true\"></i>\n                    Contact Support\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n    </main>\n  </div>\n</template>\n\n<script>\nimport documentRequestService from '@/services/documentRequestService';\nimport ClientHeader from './ClientHeader.vue';\nimport unifiedAuthService from '@/services/unifiedAuthService';\n\nexport default {\n  name: 'NewDocumentRequest',\n  components: {\n    ClientHeader\n  },\n  data() {\n    return {\n      documentTypes: [],\n      loading: true,\n      error: null,\n      // Header state\n      showUserDropdown: false,\n      sidebarCollapsed: false,\n      activeMenu: 'dashboard',\n      // User data\n      userName: 'User',\n      userEmail: '<EMAIL>',\n      userAvatar: null,\n      firstName: 'User',\n      totalRequests: 0,\n      pendingRequests: 0\n    };\n  },\n  async mounted() {\n    await this.loadUserData();\n    await this.loadDocumentTypes();\n    await this.loadUserStats();\n  },\n  methods: {\n    async loadUserData() {\n      try {\n        const currentUser = unifiedAuthService.getCurrentUser();\n        if (currentUser) {\n          this.userName = currentUser.username || 'User';\n          this.userEmail = currentUser.email || '<EMAIL>';\n          this.firstName = currentUser.first_name || currentUser.username || 'User';\n          // Set user avatar if available\n          this.userAvatar = currentUser.avatar || null;\n        }\n      } catch (error) {\n        console.error('Error loading user data:', error);\n      }\n    },\n\n    async loadUserStats() {\n      try {\n        // TODO: Implement API call to get user statistics\n        // For now, using placeholder data\n        this.totalRequests = 5;\n        this.pendingRequests = 2;\n      } catch (error) {\n        console.error('Error loading user stats:', error);\n      }\n    },\n\n    async loadDocumentTypes() {\n      try {\n        this.loading = true;\n        this.error = null;\n\n        const response = await documentRequestService.getDocumentTypes();\n        this.documentTypes = response.data || [];\n\n      } catch (error) {\n        console.error('Error loading document types:', error);\n        this.error = error.response?.data?.message || 'Failed to load available services';\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    selectDocumentType(documentType) {\n      if (!documentType.is_active) return;\n\n      // Navigate to specific document request form\n      const routeName = this.getRouteForDocumentType(documentType.type_name);\n      if (routeName) {\n        this.$router.push({ \n          name: routeName,\n          params: { documentTypeId: documentType.id }\n        });\n      }\n    },\n\n    getRouteForDocumentType(typeName) {\n      const routes = {\n        'Barangay Clearance': 'BarangayClearanceRequest',\n        'Cedula': 'CedulaRequest'\n      };\n      return routes[typeName];\n    },\n\n    getDocumentIcon(typeName) {\n      const icons = {\n        'Barangay Clearance': 'fas fa-certificate',\n        'Cedula': 'fas fa-id-card'\n      };\n      return icons[typeName] || 'fas fa-file-alt';\n    },\n\n    getProcessingTime(typeName) {\n      const times = {\n        'Barangay Clearance': '3-5 business days',\n        'Cedula': '1-2 business days'\n      };\n      return times[typeName] || '3-5 business days';\n    },\n\n    formatCurrency(amount) {\n      return parseFloat(amount).toFixed(2);\n    },\n\n    // Header event handlers\n    handleSidebarToggle() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n    },\n\n    handleUserDropdownToggle() {\n      this.showUserDropdown = !this.showUserDropdown;\n    },\n\n    handleMenuAction(action) {\n      console.log('Menu action:', action);\n      switch (action) {\n        case 'profile':\n          // TODO: Navigate to profile page\n          break;\n        case 'settings':\n          // TODO: Navigate to settings page\n          break;\n        case 'account':\n          // TODO: Navigate to account page\n          break;\n      }\n    },\n\n    handleLogout() {\n      try {\n        unifiedAuthService.logout();\n        this.$router.push({ name: 'WelcomePage' });\n      } catch (error) {\n        console.error('Logout error:', error);\n      }\n    },\n\n    handleError(error) {\n      console.error('Header error:', error);\n    },\n\n    // Search handler\n    handleSearch(query) {\n      console.log('Search query:', query);\n      // TODO: Implement search functionality\n      // This could search through documents, services, or requests\n      // For now, we'll just log the query\n    },\n\n    // Navigation methods\n    scrollToServices() {\n      this.$refs.servicesSection?.scrollIntoView({ behavior: 'smooth' });\n    },\n\n    goToMyRequests() {\n      this.$router.push({ name: 'MyRequests' });\n    },\n\n    goToProfile() {\n      // TODO: Navigate to profile page\n      console.log('Navigate to profile');\n    },\n\n    goBack() {\n      this.$router.push({ name: 'ClientDashboard' });\n    },\n\n    openHelp() {\n      // TODO: Implement help/FAQ modal or page\n      console.log('Opening help...');\n    },\n\n    contactSupport() {\n      // TODO: Implement contact support functionality\n      console.log('Contacting support...');\n      // Could open a modal, redirect to contact page, or open email client\n      // For now, we'll just log the action\n    }\n  }\n};\n</script>\n\n<style scoped>\n/* Modern Government Portal Styles - USWDS Inspired */\n:root {\n  /* Government Colors */\n  --gov-blue: #005ea2;\n  --gov-blue-dark: #0f4c96;\n  --gov-blue-light: #2378c3;\n  --gov-blue-lighter: #e7f6f8;\n  --gov-red: #d63384;\n  --gov-green: #00a91c;\n  --gov-yellow: #ffbe2e;\n  --gov-yellow-light: #fef0cd;\n\n  /* Neutral Colors */\n  --text-primary: #1b1b1b;\n  --text-secondary: #454545;\n  --text-light: #757575;\n  --text-white: #ffffff;\n\n  /* Background Colors */\n  --bg-white: #ffffff;\n  --bg-gray-5: #f9f9f9;\n  --bg-gray-10: #f0f0f0;\n  --bg-gray-20: #dfe1e2;\n\n  /* Shadows */\n  --shadow-1: 0 1px 2px 0 rgba(0, 0, 0, 0.1);\n  --shadow-2: 0 1px 4px 0 rgba(0, 0, 0, 0.1);\n  --shadow-3: 0 4px 8px 0 rgba(0, 0, 0, 0.1);\n  --shadow-4: 0 8px 16px 0 rgba(0, 0, 0, 0.1);\n\n  /* Typography */\n  --font-family-sans: 'Source Sans Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n  --font-family-serif: 'Merriweather', Georgia, serif;\n\n  /* Spacing */\n  --spacing-1: 0.25rem;\n  --spacing-2: 0.5rem;\n  --spacing-3: 0.75rem;\n  --spacing-4: 1rem;\n  --spacing-5: 1.25rem;\n  --spacing-6: 1.5rem;\n  --spacing-8: 2rem;\n  --spacing-10: 2.5rem;\n  --spacing-12: 3rem;\n  --spacing-16: 4rem;\n\n  /* Border Radius */\n  --border-radius-sm: 0.125rem;\n  --border-radius-md: 0.25rem;\n  --border-radius-lg: 0.5rem;\n  --border-radius-xl: 1rem;\n\n  /* Transitions */\n  --transition-fast: 0.15s ease-in-out;\n  --transition-base: 0.2s ease-in-out;\n}\n\n/* Reset and base styles */\n* {\n  box-sizing: border-box;\n}\n\n.client-dashboard {\n  font-family: var(--font-family-sans);\n  line-height: 1.6;\n  color: var(--text-primary);\n  background: var(--bg-gray-5);\n  min-height: 100vh;\n}\n\n/* Background Setup */\n.background-container {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: -2;\n}\n\n.background-image {\n  width: 100%;\n  height: 100%;\n  background-image: url('@/assets/bula-request-background-pic.png');\n  background-size: cover;\n  background-position: center;\n  background-repeat: no-repeat;\n  background-attachment: fixed;\n  opacity: 0.03;\n}\n\n.background-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    135deg,\n    rgba(0, 94, 162, 0.02) 0%,\n    rgba(35, 120, 195, 0.03) 100%\n  );\n  z-index: -1;\n}\n\n/* Container utility */\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 var(--spacing-4);\n}\n\n/* Main Content */\n.main-content {\n  margin-top: 140px; /* Account for new fixed header (gov banner + main header + breadcrumb) */\n  transition: margin-left var(--transition-base);\n  padding-bottom: var(--spacing-16);\n}\n\n.main-content.sidebar-collapsed {\n  margin-left: 0;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .hero-content {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-6);\n    padding: var(--spacing-6);\n  }\n\n  .hero-title {\n    font-size: clamp(1.5rem, 6vw, 2rem);\n  }\n\n  .hero-actions {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .stats-grid {\n    grid-template-columns: repeat(3, 1fr);\n    gap: var(--spacing-2);\n  }\n\n  .stat-card {\n    padding: var(--spacing-3);\n    min-height: 100px;\n  }\n\n  .stat-number {\n    font-size: 1.5rem;\n  }\n\n  .quick-actions-grid {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-3);\n  }\n\n  .action-card {\n    padding: var(--spacing-4);\n    gap: var(--spacing-3);\n  }\n\n  .action-icon {\n    width: 48px;\n    height: 48px;\n    font-size: 1.25rem;\n  }\n\n  .hero-section,\n  .quick-actions-section {\n    margin: var(--spacing-2);\n  }\n}\n\n/* Modern Button Styles */\n.btn {\n  display: inline-flex;\n  align-items: center;\n  gap: var(--spacing-2);\n  padding: var(--spacing-3) var(--spacing-6);\n  font-family: var(--font-family-sans);\n  font-size: 0.875rem;\n  font-weight: 600;\n  line-height: 1.2;\n  text-decoration: none;\n  border: 2px solid transparent;\n  border-radius: var(--border-radius-md);\n  cursor: pointer;\n  transition: all var(--transition-fast);\n  white-space: nowrap;\n}\n\n.btn:focus {\n  outline: 2px solid var(--gov-yellow);\n  outline-offset: 2px;\n}\n\n.btn-primary {\n  background-color: var(--gov-blue);\n  color: var(--text-white);\n  border-color: var(--gov-blue);\n}\n\n.btn-primary:hover {\n  background-color: var(--gov-blue-dark);\n  border-color: var(--gov-blue-dark);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-3);\n}\n\n.btn-secondary {\n  background-color: var(--bg-white);\n  color: var(--gov-blue);\n  border-color: var(--gov-blue);\n}\n\n.btn-secondary:hover {\n  background-color: var(--gov-blue);\n  color: var(--text-white);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-3);\n}\n\n.btn-outline {\n  background-color: transparent;\n  color: var(--gov-blue);\n  border-color: var(--gov-blue);\n}\n\n.btn-outline:hover {\n  background-color: var(--gov-blue);\n  color: var(--text-white);\n}\n\n/* Hero Section */\n.hero-section {\n  padding: var(--spacing-8) 0;\n  position: relative;\n  z-index: 1;\n  background: var(--bg-white);\n  margin: var(--spacing-4);\n  border-radius: var(--border-radius-xl);\n  box-shadow: var(--shadow-2);\n}\n\n.hero-content {\n  display: grid;\n  grid-template-columns: 1fr auto;\n  gap: var(--spacing-8);\n  align-items: start;\n  padding: var(--spacing-8);\n}\n\n.hero-text {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-4);\n}\n\n.hero-title {\n  font-size: clamp(1.75rem, 4vw, 2.5rem);\n  font-weight: 700;\n  color: var(--gov-blue);\n  margin: 0;\n  line-height: 1.2;\n  font-family: var(--font-family-serif);\n}\n\n.hero-subtitle {\n  font-size: 1.125rem;\n  color: var(--text-secondary);\n  margin: 0;\n  line-height: 1.5;\n  max-width: 600px;\n}\n\n.hero-actions {\n  display: flex;\n  gap: var(--spacing-3);\n  flex-wrap: wrap;\n  margin-top: var(--spacing-2);\n}\n\n.hero-stats {\n  display: flex;\n  justify-content: center;\n  min-width: 280px;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: var(--spacing-3);\n  width: 100%;\n}\n\n.stat-card {\n  background: linear-gradient(135deg, var(--gov-blue), var(--gov-blue-light));\n  border-radius: var(--border-radius-lg);\n  padding: var(--spacing-4);\n  color: var(--text-white);\n  text-align: center;\n  flex: 1;\n  box-shadow: var(--shadow-2);\n  transition: all var(--transition-fast);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  min-height: 120px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n}\n\n.stat-card:hover {\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-3);\n}\n\n.stat-icon {\n  font-size: 1.5rem;\n  color: var(--gov-yellow);\n  margin-bottom: var(--spacing-2);\n  display: block;\n}\n\n.stat-number {\n  font-size: 1.75rem;\n  font-weight: 700;\n  margin-bottom: var(--spacing-1);\n  color: var(--gov-yellow);\n  font-family: var(--font-family-serif);\n  line-height: 1;\n}\n\n.stat-label {\n  font-size: 0.75rem;\n  opacity: 0.9;\n  font-weight: 500;\n  line-height: 1.2;\n}\n\n/* Quick Actions Section */\n.quick-actions-section {\n  padding: var(--spacing-8) 0;\n  position: relative;\n  z-index: 1;\n  background: var(--bg-gray-5);\n  margin: var(--spacing-4);\n  border-radius: var(--border-radius-xl);\n}\n\n.section-header {\n  text-align: center;\n  margin-bottom: var(--spacing-8);\n  padding: 0 var(--spacing-4);\n}\n\n.section-title {\n  font-size: clamp(1.75rem, 4vw, 2.25rem);\n  font-weight: 700;\n  color: var(--gov-blue);\n  margin-bottom: var(--spacing-3);\n  font-family: var(--font-family-serif);\n}\n\n.section-description {\n  font-size: 1rem;\n  color: var(--text-secondary);\n  margin: 0;\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n.quick-actions-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));\n  gap: var(--spacing-4);\n  padding: 0 var(--spacing-4);\n}\n\n.action-card {\n  background: var(--bg-white);\n  border-radius: var(--border-radius-lg);\n  padding: var(--spacing-6);\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-4);\n  cursor: pointer;\n  transition: all var(--transition-fast);\n  border: 2px solid var(--bg-gray-20);\n  box-shadow: var(--shadow-1);\n}\n\n.action-card:hover,\n.action-card:focus {\n  transform: translateY(-2px);\n  box-shadow: var(--shadow-3);\n  border-color: var(--gov-blue);\n  outline: none;\n}\n\n.action-card.primary {\n  background: linear-gradient(135deg, var(--gov-blue), var(--gov-blue-light));\n  color: var(--text-white);\n  border-color: var(--gov-blue);\n}\n\n.action-card.primary:hover,\n.action-card.primary:focus {\n  border-color: var(--gov-yellow);\n  box-shadow: var(--shadow-4);\n}\n\n.action-icon {\n  width: 4rem;\n  height: 4rem;\n  background: linear-gradient(135deg, #fbbf24, #f59e0b);\n  border-radius: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.5rem;\n  color: #1e3a8a;\n  flex-shrink: 0;\n}\n\n.action-card.primary .action-icon {\n  background: rgba(251, 191, 36, 0.2);\n  color: #fbbf24;\n}\n\n.action-content {\n  flex: 1;\n}\n\n.action-content h3 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  color: inherit;\n}\n\n.action-content p {\n  margin: 0;\n  opacity: 0.8;\n  line-height: 1.5;\n}\n\n.action-arrow {\n  color: #6b7280;\n  font-size: 1.25rem;\n}\n\n.action-card.primary .action-arrow {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n/* Services Section */\n.services-section {\n  padding: 2rem 0;\n  position: relative;\n  z-index: 1;\n}\n\n.services-section .section-title {\n  color: white;\n}\n\n.services-section .section-description {\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.loading-container, .error-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n  text-align: center;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: 1rem;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.loading-spinner i {\n  font-size: 3rem;\n  color: #1e3a8a;\n  margin-bottom: 1rem;\n}\n\n.error-content {\n  max-width: 400px;\n}\n\n.error-content i {\n  font-size: 3rem;\n  color: #dc2626;\n  margin-bottom: 1rem;\n}\n\n.retry-btn {\n  background: linear-gradient(135deg, #1e3a8a, #1e40af);\n  color: white;\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 0.75rem;\n  cursor: pointer;\n  margin-top: 1rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);\n}\n\n.retry-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.4);\n}\n\n.document-types-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n  gap: 1.5rem;\n}\n\n.document-card {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border: 2px solid rgba(30, 58, 138, 0.2);\n  border-radius: 1rem;\n  padding: 2rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n}\n\n.document-card:hover:not(.disabled) {\n  border-color: #1e3a8a;\n  transform: translateY(-5px);\n  box-shadow: 0 12px 30px rgba(30, 58, 138, 0.2);\n  background: rgba(255, 255, 255, 1);\n}\n\n.document-card.disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.document-icon {\n  flex-shrink: 0;\n  width: 4rem;\n  height: 4rem;\n  background: linear-gradient(135deg, #1e3a8a, #1e40af);\n  border-radius: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 1.5rem;\n  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);\n}\n\n.document-content {\n  flex: 1;\n}\n\n.document-title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1e3a8a;\n  margin-bottom: 0.5rem;\n}\n\n.document-description {\n  color: #6b7280;\n  margin-bottom: 1rem;\n  line-height: 1.5;\n}\n\n.document-details {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.fee-info {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.fee-label {\n  color: #9ca3af;\n  font-size: 0.9rem;\n}\n\n.fee-amount {\n  font-weight: 600;\n  color: #059669;\n  font-size: 1.1rem;\n}\n\n.processing-time {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #9ca3af;\n  font-size: 0.9rem;\n}\n\n.document-action {\n  flex-shrink: 0;\n  display: flex;\n  align-items: center;\n}\n\n.document-action i {\n  color: #d1d5db;\n  font-size: 1.25rem;\n}\n\n.status-badge.unavailable {\n  background: #fecaca;\n  color: #dc2626;\n  padding: 0.25rem 0.75rem;\n  border-radius: 1rem;\n  font-size: 0.8rem;\n  font-weight: 500;\n}\n\n/* Info Section */\n.info-section {\n  padding: 2rem 0;\n  position: relative;\n  z-index: 1;\n}\n\n.info-grid {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 2rem;\n}\n\n.info-card, .help-card {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(30, 58, 138, 0.2);\n  border-radius: 1rem;\n  padding: 2rem;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n}\n\n.info-card:hover, .help-card:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 8px 30px rgba(30, 58, 138, 0.15);\n  background: rgba(255, 255, 255, 1);\n  border-color: #1e3a8a;\n}\n\n.info-header, .help-header {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  margin-bottom: 1.5rem;\n}\n\n.info-header h3, .help-header h3 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1e3a8a;\n  margin: 0;\n}\n\n.info-header i {\n  color: #1e3a8a;\n  font-size: 1.25rem;\n}\n\n.help-header i {\n  color: #059669;\n  font-size: 1.25rem;\n}\n\n.info-list {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.info-list li {\n  display: flex;\n  align-items: flex-start;\n  gap: 0.75rem;\n  margin-bottom: 1rem;\n  color: #6b7280;\n  line-height: 1.6;\n}\n\n.info-list i {\n  color: #059669;\n  margin-top: 0.125rem;\n  flex-shrink: 0;\n}\n\n.help-content p {\n  color: #6b7280;\n  margin-bottom: 1.5rem;\n  line-height: 1.6;\n}\n\n.help-actions {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.help-btn, .contact-btn {\n  background: rgba(255, 255, 255, 0.9);\n  backdrop-filter: blur(5px);\n  border: 2px solid rgba(30, 58, 138, 0.3);\n  padding: 0.875rem 1.25rem;\n  border-radius: 0.75rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  color: #6b7280;\n  font-weight: 500;\n  font-size: 0.95rem;\n}\n\n.help-btn:hover {\n  border-color: #1e3a8a;\n  color: #1e3a8a;\n  background: rgba(30, 58, 138, 0.1);\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.2);\n}\n\n.contact-btn:hover {\n  border-color: #059669;\n  color: #059669;\n  background: rgba(5, 150, 105, 0.1);\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(5, 150, 105, 0.2);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .container {\n    padding: 0 0.75rem;\n  }\n\n  .welcome-header {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n    padding: 1.5rem;\n  }\n\n  .welcome-stats {\n    justify-content: center;\n  }\n\n  .stat-card {\n    min-width: 120px;\n  }\n\n  .quick-actions-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n\n  .action-card {\n    padding: 1.5rem;\n  }\n\n  .document-types-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n\n  .document-card {\n    flex-direction: column;\n    text-align: center;\n    padding: 1.5rem;\n  }\n\n  .info-grid {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n\n  .info-card, .help-card {\n    padding: 1.5rem;\n  }\n\n  .loading-container, .error-container {\n    padding: 3rem 1.5rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .container {\n    padding: 0 0.5rem;\n  }\n\n  .welcome-section,\n  .quick-actions-section,\n  .services-section,\n  .info-section {\n    padding: 1.5rem 0;\n  }\n\n  .welcome-header {\n    padding: 1rem;\n  }\n\n  .stat-card {\n    padding: 1rem;\n  }\n\n  .stat-number {\n    font-size: 1.5rem;\n  }\n\n  .action-card {\n    padding: 1rem;\n    flex-direction: column;\n    text-align: center;\n    gap: 1rem;\n  }\n\n  .action-icon {\n    width: 3rem;\n    height: 3rem;\n  }\n\n  .document-card {\n    padding: 1rem;\n  }\n\n  .info-card, .help-card {\n    padding: 1rem;\n  }\n\n  .loading-container, .error-container {\n    padding: 2rem 1rem;\n  }\n}\n\n/* Animations */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.welcome-section,\n.quick-actions-section,\n.services-section,\n.info-section {\n  animation: fadeInUp 0.8s ease-out;\n}\n\n.quick-actions-section {\n  animation-delay: 0.2s;\n}\n\n.services-section {\n  animation-delay: 0.4s;\n}\n\n.info-section {\n  animation-delay: 0.6s;\n}\n\n/* Accessibility */\n@media (prefers-reduced-motion: reduce) {\n  * {\n    animation: none !important;\n    transition: none !important;\n  }\n\n  .action-card:hover,\n  .document-card:hover,\n  .info-card:hover,\n  .help-card:hover {\n    transform: none;\n  }\n}\n\n/* Focus styles */\n.action-card:focus,\n.document-card:focus,\n.help-btn:focus,\n.contact-btn:focus,\n.retry-btn:focus {\n  outline: 3px solid #fbbf24;\n  outline-offset: 2px;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAkB;;EAsDhBA,KAAK,EAAC;AAAc;;EACtBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAW;;EAsEfA,KAAK,EAAC;AAAqB;;EAKzBA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAc;;;;;;uBArIvCC,mBAAA,CAqJM,OArJNC,UAqJM,GApJJC,mBAAA,gBAAmB,E,0BACnBC,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAsB,IAC/BI,mBAAA,CAAoC;IAA/BJ,KAAK,EAAC;EAAkB,IAC7BI,mBAAA,CAAsC;IAAjCJ,KAAK,EAAC;EAAoB,G,sBAGjCG,mBAAA,mCAAsC,EACtCE,YAAA,CAcEC,uBAAA;IAbCC,QAAQ,EAAEC,KAAA,CAAAD,QAAQ;IAClBE,SAAS,EAAED,KAAA,CAAAC,SAAS;IACpBC,UAAU,EAAEF,KAAA,CAAAE,UAAU;IACtBC,gBAAgB,EAAEH,KAAA,CAAAG,gBAAgB;IAClCC,gBAAgB,EAAEJ,KAAA,CAAAI,gBAAgB;IAClCC,UAAU,EAAEL,KAAA,CAAAK,UAAU;IACtBC,eAAe,EAAE,IAAI;IACrBC,eAAc,EAAEC,QAAA,CAAAC,mBAAmB;IACnCC,oBAAoB,EAAEF,QAAA,CAAAG,wBAAwB;IAC9CC,YAAW,EAAEJ,QAAA,CAAAK,gBAAgB;IAC7BC,QAAM,EAAEN,QAAA,CAAAO,YAAY;IACpBC,OAAK,EAAER,QAAA,CAAAS,WAAW;IAClBC,QAAM,EAAEV,QAAA,CAAAW;wNAGXxB,mBAAA,kBAAqB,EACrBC,mBAAA,CA2HO;IA3HDwB,EAAE,EAAC,cAAc;IAAC5B,KAAK,EAAA6B,eAAA,EAAC,cAAc;MAAA,qBAAgCrB,KAAA,CAAAI;IAAgB;MAC1FT,mBAAA,kBAAqB,EACrBE,YAAA,CAMEyB,sBAAA;IALCC,SAAS,EAAEvB,KAAA,CAAAuB,SAAS;IACpBC,aAAa,EAAExB,KAAA,CAAAwB,aAAa;IAC5BC,eAAe,EAAEzB,KAAA,CAAAyB,eAAe;IAChCC,iBAAiB,EAAElB,QAAA,CAAAmB,gBAAgB;IACnCC,cAAa,EAAEpB,QAAA,CAAAqB;qHAGlBlC,mBAAA,2BAA8B,EAC9BE,YAAA,CAKEiC,8BAAA;IAJCJ,iBAAiB,EAAElB,QAAA,CAAAmB,gBAAgB;IACnCC,cAAa,EAAEpB,QAAA,CAAAqB,cAAc;IAC7BE,eAAc,EAAEC,IAAA,CAAAC,eAAe;IAC/BC,SAAQ,EAAE1B,QAAA,CAAA2B;oGAGbxC,mBAAA,+BAAkC,EAClCE,YAAA,CAOEuC,kCAAA;IANAC,GAAG,EAAC,iBAAiB;IACpBC,aAAa,EAAEtC,KAAA,CAAAsC,aAAa;IAC5BC,OAAO,EAAEvC,KAAA,CAAAuC,OAAO;IAChBC,KAAK,EAAExC,KAAA,CAAAwC,KAAK;IACZC,oBAAoB,EAAEjC,QAAA,CAAAkC,kBAAkB;IACxCC,OAAK,EAAEnC,QAAA,CAAAoC;qGAGVjD,mBAAA,kCAAqC,EACrCC,mBAAA,CA6FU,WA7FViD,UA6FU,GA5FRjD,mBAAA,CA2FM,OA3FNkD,UA2FM,GA1FJlD,mBAAA,CAyFM,OAzFNmD,UAyFM,GAxFJpD,mBAAA,uBAA0B,E,woCA4B1BA,mBAAA,kBAAqB,E,82CAwCrBA,mBAAA,eAAkB,EAClBC,mBAAA,CAkBM,OAlBNoD,UAkBM,G,0BAjBJpD,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAAiD;IAA9CJ,KAAK,EAAC,gBAAgB;IAAC,aAAW,EAAC;MACtCI,mBAAA,CAAyB,YAArB,kBAAgB,E,sBAEtBA,mBAAA,CAYM,OAZNqD,UAYM,G,0BAXJrD,mBAAA,CAA+I;IAA5IJ,KAAK,EAAC;EAAkB,GAAC,iHAA+G,sBAC3II,mBAAA,CASM,OATNsD,UASM,GARJtD,mBAAA,CAGS;IAHDJ,KAAK,EAAC,0BAA0B;IAAE2D,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAE7C,QAAA,CAAA2B,QAAA,IAAA3B,QAAA,CAAA2B,QAAA,IAAAkB,IAAA,CAAQ;gCACvDzD,mBAAA,CAAyD;IAAtDJ,KAAK,EAAC,wBAAwB;IAAC,aAAW,EAAC;+CAAW,YAE3D,E,IACAI,mBAAA,CAGS;IAHDJ,KAAK,EAAC,6BAA6B;IAAE2D,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAE7C,QAAA,CAAA8C,cAAA,IAAA9C,QAAA,CAAA8C,cAAA,IAAAD,IAAA,CAAc;gCAChEzD,mBAAA,CAA+C;IAA5CJ,KAAK,EAAC,cAAc;IAAC,aAAW,EAAC;+CAAW,mBAEjD,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}