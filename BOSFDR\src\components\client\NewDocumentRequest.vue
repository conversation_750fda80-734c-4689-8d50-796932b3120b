<template>
  <div class="document-request-page">
    <!-- Skip to main content link for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Client Header with Navigation -->
    <ClientHeader
      :user-name="userState.name"
      :user-email="userState.email"
      :user-avatar="userState.avatar"
      :show-user-dropdown="uiState.showUserDropdown"
      :active-menu="'services'"
      :show-breadcrumbs="true"
      @user-dropdown-toggle="toggleUserDropdown"
      @menu-action="handleMenuAction"
      @logout="handleLogout"
      @error="handleError"
      @search="handleSearch"
    />

    <!-- Main Content -->
    <main id="main-content" class="main-content" role="main">
      <!-- Page Header -->
      <PageHeader
        title="Document Request Services"
        subtitle="Request official documents from Barangay Bula through our secure digital platform"
        :breadcrumbs="breadcrumbs"
      />

      <!-- User Dashboard Stats -->
      <UserStatsSection
        :total-requests="userStats.totalRequests"
        :pending-requests="userStats.pendingRequests"
        :completed-requests="userStats.completedRequests"
        :loading="userStats.loading"
        @view-requests="navigateToRequests"
      />

      <!-- Quick Actions -->
      <QuickActionsSection
        @new-request="scrollToServices"
        @view-requests="navigateToRequests"
        @view-profile="navigateToProfile"
        @contact-support="openSupportModal"
      />

      <!-- Document Services -->
      <DocumentServicesSection
        ref="servicesSection"
        :document-types="documentTypes.data"
        :loading="documentTypes.loading"
        :error="documentTypes.error"
        @select-document="selectDocumentType"
        @retry-load="loadDocumentTypes"
      />

      <!-- Information Section -->
      <InformationSection
        @open-help="openHelpModal"
        @contact-support="openSupportModal"
      />
    </main>

    <!-- Modals -->
    <HelpModal
      v-if="uiState.showHelpModal"
      @close="closeHelpModal"
    />

    <SupportModal
      v-if="uiState.showSupportModal"
      @close="closeSupportModal"
    />
  </div>






</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/userStore'
import { useDocumentStore } from '@/stores/documentStore'
import { useNotificationStore } from '@/stores/notificationStore'

// Components
import ClientHeader from './ClientHeader.vue'
import PageHeader from './components/PageHeader.vue'
import UserStatsSection from './components/UserStatsSection.vue'
import QuickActionsSection from './components/QuickActionsSection.vue'
import DocumentServicesSection from './components/DocumentServicesSection.vue'
import InformationSection from './components/InformationSection.vue'
import HelpModal from './components/HelpModal.vue'
import SupportModal from './components/SupportModal.vue'

// Composables
import { useDocumentTypes } from '@/composables/useDocumentTypes'
import { useUserStats } from '@/composables/useUserStats'
import { useNavigation } from '@/composables/useNavigation'

// Router and stores
const router = useRouter()
const userStore = useUserStore()
const documentStore = useDocumentStore()
const notificationStore = useNotificationStore()

// Reactive state
const uiState = reactive({
  showUserDropdown: false,
  showHelpModal: false,
  showSupportModal: false
})

// User state
const userState = computed(() => ({
  name: userStore.currentUser?.username || 'User',
  email: userStore.currentUser?.email || '<EMAIL>',
  avatar: userStore.currentUser?.avatar || null,
  firstName: userStore.currentUser?.first_name || userStore.currentUser?.username || 'User'
}))

// Breadcrumbs
const breadcrumbs = computed(() => [
  { label: 'Dashboard', href: '/dashboard', icon: 'fas fa-home' },
  { label: 'Document Services', current: true }
])

// Composables
const { documentTypes, loadDocumentTypes } = useDocumentTypes()
const { userStats } = useUserStats()
const { navigateToRequests, navigateToProfile, navigateToDocument } = useNavigation()

// Refs
const servicesSection = ref(null)

// Methods
const toggleUserDropdown = () => {
  uiState.showUserDropdown = !uiState.showUserDropdown
}

const handleMenuAction = (action) => {
  switch (action) {
    case 'dashboard':
      router.push({ name: 'ClientDashboard' })
      break
    case 'services':
      scrollToServices()
      break
    case 'requests':
      navigateToRequests()
      break
    case 'profile':
      navigateToProfile()
      break
    case 'help':
      openHelpModal()
      break
    default:
      console.log('Menu action:', action)
  }
}

const handleLogout = async () => {
  try {
    await userStore.logout()
    router.push({ name: 'WelcomePage' })
  } catch (error) {
    console.error('Logout error:', error)
    notificationStore.addNotification({
      type: 'error',
      title: 'Logout Failed',
      message: 'Unable to logout. Please try again.'
    })
  }
}

const handleError = (error) => {
  console.error('Component error:', error)
  notificationStore.addNotification({
    type: 'error',
    title: 'Error',
    message: error.message || 'An unexpected error occurred'
  })
}

const handleSearch = (query) => {
  // TODO: Implement search functionality
  console.log('Search query:', query)
  // Could search through documents, services, or requests
}

const scrollToServices = () => {
  servicesSection.value?.$el?.scrollIntoView({ behavior: 'smooth' })
}

const selectDocumentType = (documentType) => {
  if (!documentType.is_active) {
    notificationStore.addNotification({
      type: 'warning',
      title: 'Service Unavailable',
      message: `${documentType.type_name} service is currently unavailable.`
    })
    return
  }

  navigateToDocument(documentType)
}

const openHelpModal = () => {
  uiState.showHelpModal = true
}

const closeHelpModal = () => {
  uiState.showHelpModal = false
}

const openSupportModal = () => {
  uiState.showSupportModal = true
}

const closeSupportModal = () => {
  uiState.showSupportModal = false
}

// Lifecycle
onMounted(async () => {
  try {
    await Promise.all([
      userStore.loadCurrentUser(),
      loadDocumentTypes(),
      userStats.load()
    ])
  } catch (error) {
    console.error('Failed to load initial data:', error)
    handleError(error)
  }
})
</script>

<style scoped>
/* Modern Government Portal Styles - USWDS 3.0 Inspired */

/* Skip link for accessibility */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--uswds-color-primary-darker);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 0 0 4px 4px;
  z-index: 1000;
  font-weight: 600;
}

.skip-link:focus {
  top: 0;
}

/* Main page container */
.document-request-page {
  min-height: 100vh;
  background: var(--uswds-color-base-lightest);
  font-family: var(--uswds-font-family-sans);
  line-height: 1.6;
  color: var(--uswds-color-ink);
}

/* Main content area */
.main-content {
  margin-top: var(--header-height, 140px);
  padding-bottom: var(--uswds-spacing-8);
}

/* Responsive design */
@media (max-width: 768px) {
  .main-content {
    margin-top: var(--header-height-mobile, 120px);
    padding: 0 var(--uswds-spacing-2);
  }
}

/* Focus management for accessibility */
.main-content:focus {
  outline: 2px solid var(--uswds-color-focus);
  outline-offset: 2px;
}

/* Animation for smooth page transitions */
.document-request-page {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .document-request-page {
    animation: none;
  }

  * {
    transition: none !important;
    animation: none !important;
  }
}
</style>



</style>
