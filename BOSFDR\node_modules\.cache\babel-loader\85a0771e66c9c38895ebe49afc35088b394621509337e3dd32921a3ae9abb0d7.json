{"ast": null, "code": "import { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue';\nimport ClientNotifications from './ClientNotifications.vue';\nexport default {\n  name: 'ClientHeader',\n  components: {\n    ClientNotifications\n  },\n  props: {\n    userName: {\n      type: String,\n      default: 'User'\n    },\n    userEmail: {\n      type: String,\n      default: '<EMAIL>'\n    },\n    userAvatar: {\n      type: String,\n      default: null\n    },\n    showUserDropdown: {\n      type: Boolean,\n      default: false\n    },\n    sidebarCollapsed: {\n      type: Boolean,\n      default: false\n    },\n    activeMenu: {\n      type: String,\n      default: 'dashboard'\n    },\n    showBreadcrumbs: {\n      type: Boolean,\n      default: true\n    }\n  },\n  emits: ['sidebar-toggle', 'user-dropdown-toggle', 'menu-action', 'logout', 'error', 'search', 'notification-click'],\n  setup(props, {\n    emit\n  }) {\n    // Reactive state\n    const showSearch = ref(false);\n    const showBannerDetails = ref(false);\n    const searchQuery = ref('');\n    const searchInput = ref(null);\n\n    // Computed properties\n    const getPageTitle = computed(() => {\n      const titles = {\n        dashboard: 'Dashboard',\n        services: 'Services',\n        requests: 'My Requests',\n        profile: 'My Profile',\n        settings: 'Settings',\n        help: 'Help & Support',\n        documents: 'My Documents',\n        history: 'Request History'\n      };\n      return titles[props.activeMenu] || 'Dashboard';\n    });\n\n    // Methods\n    const toggleSearch = async () => {\n      showSearch.value = !showSearch.value;\n      if (showSearch.value) {\n        await nextTick();\n        searchInput.value?.focus();\n      }\n    };\n    const closeSearch = () => {\n      showSearch.value = false;\n      searchQuery.value = '';\n    };\n    const toggleBannerDetails = () => {\n      showBannerDetails.value = !showBannerDetails.value;\n    };\n    const handleSearch = () => {\n      if (searchQuery.value.trim()) {\n        emit('search', searchQuery.value.trim());\n        closeSearch();\n      }\n    };\n    const performSearch = () => {\n      handleSearch();\n    };\n    const handleSidebarToggle = () => {\n      emit('sidebar-toggle');\n    };\n    const handleUserDropdownToggle = () => {\n      emit('user-dropdown-toggle');\n    };\n    const handleMenuAction = action => {\n      emit('menu-action', action);\n    };\n    const handleLogout = () => {\n      emit('logout');\n    };\n    const handleOutsideClick = event => {\n      // Check if click is outside user dropdown\n      if (!event.target.closest('.user-profile')) {\n        if (props.showUserDropdown) {\n          emit('user-dropdown-toggle');\n        }\n      }\n\n      // Check if click is outside search\n      if (!event.target.closest('.search-container')) {\n        showSearch.value = false;\n      }\n\n      // Check if click is outside banner\n      if (!event.target.closest('.gov-banner')) {\n        showBannerDetails.value = false;\n      }\n    };\n\n    // Notification event handlers\n    const handleNewNotification = notification => {\n      console.log('New notification received:', notification);\n      // Handle new notification - could show toast, update UI, etc.\n    };\n    const handleNotificationClick = notification => {\n      console.log('📊 ClientHeader: Handling notification click:', notification);\n      try {\n        // Parse notification data\n        const notificationData = typeof notification.data === 'string' ? JSON.parse(notification.data) : notification.data || {};\n\n        // Log navigation for debugging\n        console.log('📊 ClientHeader: Notification data:', notificationData);\n\n        // Additional header-specific logic can go here\n        // For example, updating header state, showing badges, etc.\n\n        // The navigation is now handled by the ClientNotifications component\n        // This handler can focus on header-specific updates\n      } catch (error) {\n        console.error('❌ ClientHeader: Error handling notification click:', error);\n      }\n\n      // Always emit the event for other components that might need it\n      emit('notification-click', notification);\n    };\n    const handleNotificationError = error => {\n      console.error('Notification error:', error);\n      emit('error', error);\n    };\n\n    // Lifecycle hooks\n    onMounted(() => {\n      document.addEventListener('click', handleOutsideClick);\n    });\n    onBeforeUnmount(() => {\n      document.removeEventListener('click', handleOutsideClick);\n    });\n    return {\n      // Reactive state\n      showSearch,\n      showBannerDetails,\n      searchQuery,\n      searchInput,\n      // Computed\n      getPageTitle,\n      // Methods\n      toggleSearch,\n      closeSearch,\n      toggleBannerDetails,\n      handleSearch,\n      performSearch,\n      handleSidebarToggle,\n      handleUserDropdownToggle,\n      handleMenuAction,\n      handleLogout,\n      handleOutsideClick,\n      handleNewNotification,\n      handleNotificationClick,\n      handleNotificationError\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "onMounted", "onBeforeUnmount", "nextTick", "ClientNotifications", "name", "components", "props", "userName", "type", "String", "default", "userEmail", "userAvatar", "showUserDropdown", "Boolean", "sidebarCollapsed", "activeMenu", "showBreadcrumbs", "emits", "setup", "emit", "showSearch", "showBannerDetails", "searchQuery", "searchInput", "getPageTitle", "titles", "dashboard", "services", "requests", "profile", "settings", "help", "documents", "history", "toggleSearch", "value", "focus", "closeSearch", "toggleBannerDetails", "handleSearch", "trim", "performSearch", "handleSidebarToggle", "handleUserDropdownToggle", "handleMenuAction", "action", "handleLogout", "handleOutsideClick", "event", "target", "closest", "handleNewNotification", "notification", "console", "log", "handleNotificationClick", "notificationData", "data", "JSON", "parse", "error", "handleNotificationError", "document", "addEventListener", "removeEventListener"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\client\\ClientHeader.vue"], "sourcesContent": ["<template>\n  <!-- Skip Navigation Link for Accessibility -->\n  <a href=\"#main-content\" class=\"skip-link\">Skip to main content</a>\n\n  <header class=\"client-header\" role=\"banner\" aria-label=\"Site header\">\n    <!-- Government Banner - USWDS Compliant -->\n    <section\n      class=\"gov-banner\"\n      aria-label=\"Official website of Barangay Bula, General Santos City\"\n    >\n      <div class=\"usa-accordion\">\n        <header class=\"usa-banner__header\">\n          <div class=\"usa-banner__inner\">\n            <div class=\"grid-col-auto\">\n              <img\n                aria-hidden=\"true\"\n                class=\"usa-banner__header-flag\"\n                src=\"/assets/images/ph_flag_small.png\"\n                alt=\"\"\n              />\n            </div>\n            <div class=\"grid-col-fill tablet:grid-col-auto\" aria-hidden=\"true\">\n              <p class=\"usa-banner__header-text\">\n                An official website of Barangay Bula, General Santos City\n              </p>\n              <p class=\"usa-banner__header-action\">Here's how you know</p>\n            </div>\n            <button\n              type=\"button\"\n              class=\"usa-accordion__button usa-banner__button\"\n              :aria-expanded=\"showBannerDetails\"\n              aria-controls=\"gov-banner-content\"\n              @click=\"toggleBannerDetails\"\n            >\n              <span class=\"usa-banner__button-text\">Here's how you know</span>\n            </button>\n          </div>\n        </header>\n        <div\n          class=\"usa-banner__content usa-accordion__content\"\n          id=\"gov-banner-content\"\n          :hidden=\"!showBannerDetails\"\n        >\n          <div class=\"grid-row grid-gap-lg\">\n            <div class=\"usa-banner__guidance tablet:grid-col-6\">\n              <img\n                class=\"usa-banner__icon usa-media-block__img\"\n                src=\"/assets/images/icon-dot-gov.svg\"\n                role=\"img\"\n                alt=\"\"\n                aria-hidden=\"true\"\n              />\n              <div class=\"usa-media-block__body\">\n                <p>\n                  <strong>Official websites use .gov.ph</strong><br />\n                  A <strong>.gov.ph</strong> website belongs to an official government\n                  organization in the Philippines.\n                </p>\n              </div>\n            </div>\n            <div class=\"usa-banner__guidance tablet:grid-col-6\">\n              <img\n                class=\"usa-banner__icon usa-media-block__img\"\n                src=\"/assets/images/icon-https.svg\"\n                role=\"img\"\n                alt=\"\"\n                aria-hidden=\"true\"\n              />\n              <div class=\"usa-media-block__body\">\n                <p>\n                  <strong>Secure .gov.ph websites use HTTPS</strong><br />\n                  A <strong>lock</strong> (🔒) or <strong>https://</strong> means you've\n                  safely connected to the .gov.ph website. Share sensitive information\n                  only on official, secure websites.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- Main Header -->\n    <div class=\"main-header\">\n      <div class=\"header-container\">\n        <!-- Left Section: Logo and Navigation -->\n        <div class=\"header-left\">\n          <button\n            class=\"logo-section\"\n            @click=\"handleMenuAction('dashboard')\"\n            :aria-label=\"`Go to ${getPageTitle()} dashboard`\"\n            type=\"button\"\n          >\n            <img\n              src=\"@/assets/icon-of-bula.jpg\"\n              alt=\"Barangay Bula Logo\"\n              class=\"logo\"\n              width=\"40\"\n              height=\"40\"\n            />\n            <div class=\"site-identity\">\n              <h1 class=\"site-title\">Barangay Bula</h1>\n              <span class=\"site-subtitle\">Digital Services Portal</span>\n            </div>\n          </button>\n\n          <!-- Mobile Menu Toggle -->\n          <button\n            class=\"mobile-menu-toggle\"\n            @click=\"handleSidebarToggle\"\n            :aria-label=\"sidebarCollapsed ? 'Open navigation menu' : 'Close navigation menu'\"\n            :aria-expanded=\"!sidebarCollapsed\"\n            type=\"button\"\n          >\n            <span class=\"hamburger-line\" :class=\"{ active: !sidebarCollapsed }\"></span>\n            <span class=\"hamburger-line\" :class=\"{ active: !sidebarCollapsed }\"></span>\n            <span class=\"hamburger-line\" :class=\"{ active: !sidebarCollapsed }\"></span>\n          </button>\n        </div>\n\n        <!-- Center Section: Navigation (Desktop) -->\n        <nav class=\"main-navigation\" role=\"navigation\" aria-label=\"Main navigation\">\n          <ul class=\"nav-list\" role=\"menubar\">\n            <li class=\"nav-item\" role=\"none\">\n              <button\n                class=\"nav-link\"\n                :class=\"{ active: activeMenu === 'dashboard' }\"\n                @click=\"handleMenuAction('dashboard')\"\n                role=\"menuitem\"\n                :aria-current=\"activeMenu === 'dashboard' ? 'page' : undefined\"\n                type=\"button\"\n              >\n                <svg class=\"nav-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z\"/>\n                </svg>\n                <span>Dashboard</span>\n              </button>\n            </li>\n            <li class=\"nav-item\" role=\"none\">\n              <button\n                class=\"nav-link\"\n                :class=\"{ active: activeMenu === 'services' }\"\n                @click=\"handleMenuAction('services')\"\n                role=\"menuitem\"\n                :aria-current=\"activeMenu === 'services' ? 'page' : undefined\"\n                type=\"button\"\n              >\n                <svg class=\"nav-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"/>\n                </svg>\n                <span>Services</span>\n              </button>\n            </li>\n            <li class=\"nav-item\" role=\"none\">\n              <button\n                class=\"nav-link\"\n                :class=\"{ active: activeMenu === 'requests' }\"\n                @click=\"handleMenuAction('requests')\"\n                role=\"menuitem\"\n                :aria-current=\"activeMenu === 'requests' ? 'page' : undefined\"\n                type=\"button\"\n              >\n                <svg class=\"nav-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z\"/>\n                </svg>\n                <span>My Requests</span>\n              </button>\n            </li>\n            <li class=\"nav-item\" role=\"none\">\n              <button\n                class=\"nav-link\"\n                :class=\"{ active: activeMenu === 'help' }\"\n                @click=\"handleMenuAction('help')\"\n                role=\"menuitem\"\n                :aria-current=\"activeMenu === 'help' ? 'page' : undefined\"\n                type=\"button\"\n              >\n                <svg class=\"nav-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M11,18H13V16H11V18M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,20C7.59,20 4,16.41 4,12C7.59,8 4,8 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,6A4,4 0 0,0 8,10H10A2,2 0 0,1 12,8A2,2 0 0,1 14,10C14,12 11,11.75 11,15H13C13,12.75 16,12.5 16,10A4,4 0 0,0 12,6Z\"/>\n                </svg>\n                <span>Help</span>\n              </button>\n            </li>\n          </ul>\n        </nav>\n\n        <!-- Right Section: User Actions -->\n        <div class=\"header-actions\">\n          <!-- Search -->\n          <div class=\"search-container\">\n            <button class=\"search-toggle\" @click=\"toggleSearch\" aria-label=\"Search documents and services\" :aria-expanded=\"showSearch\">\n              <i class=\"fas fa-search\" aria-hidden=\"true\"></i>\n            </button>\n            <div class=\"search-box\" :class=\"{ active: showSearch }\" role=\"search\">\n              <label for=\"header-search\" class=\"sr-only\">Search documents and services</label>\n              <input\n                id=\"header-search\"\n                type=\"search\"\n                placeholder=\"Search documents, services...\"\n                class=\"search-input\"\n                v-model=\"searchQuery\"\n                @keyup.enter=\"performSearch\"\n                autocomplete=\"off\"\n              />\n              <button class=\"search-submit\" @click=\"performSearch\" aria-label=\"Submit search\">\n                <i class=\"fas fa-search\" aria-hidden=\"true\"></i>\n              </button>\n            </div>\n          </div>\n\n          <!-- Notifications -->\n          <ClientNotifications\n            @new-notification=\"handleNewNotification\"\n            @notification-click=\"handleNotificationClick\"\n            @error=\"handleNotificationError\"\n          />\n\n          <!-- User Profile -->\n          <div class=\"user-profile\" :class=\"{ active: showUserDropdown }\">\n            <button class=\"user-btn\" @click=\"handleUserDropdownToggle\" aria-label=\"User account menu\" :aria-expanded=\"showUserDropdown\">\n              <div class=\"user-avatar\">\n                <img v-if=\"userAvatar\" :src=\"userAvatar\" :alt=\"userName\" class=\"avatar-image\" />\n                <i v-else class=\"fas fa-user-circle avatar-icon\" aria-hidden=\"true\"></i>\n              </div>\n              <div class=\"user-info\">\n                <span class=\"user-name\">{{ userName }}</span>\n                <span class=\"user-role\">Client Portal</span>\n              </div>\n              <i class=\"fas fa-chevron-down dropdown-arrow\" aria-hidden=\"true\"></i>\n            </button>\n\n            <div v-if=\"showUserDropdown\" class=\"user-dropdown-menu\" role=\"menu\" aria-label=\"User account options\">\n              <div class=\"dropdown-header\">\n                <div class=\"user-details\">\n                  <strong>{{ userName }}</strong>\n                  <span class=\"user-email\">{{ userEmail }}</span>\n                </div>\n              </div>\n              <div class=\"dropdown-divider\"></div>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('profile')\" role=\"menuitem\">\n                <i class=\"fas fa-user\" aria-hidden=\"true\"></i>\n                <span>My Profile</span>\n              </a>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('settings')\" role=\"menuitem\">\n                <i class=\"fas fa-cog\" aria-hidden=\"true\"></i>\n                <span>Account Settings</span>\n              </a>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('documents')\" role=\"menuitem\">\n                <i class=\"fas fa-folder\" aria-hidden=\"true\"></i>\n                <span>My Documents</span>\n              </a>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('history')\" role=\"menuitem\">\n                <i class=\"fas fa-history\" aria-hidden=\"true\"></i>\n                <span>Request History</span>\n              </a>\n              <div class=\"dropdown-divider\"></div>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('help')\" role=\"menuitem\">\n                <i class=\"fas fa-question-circle\" aria-hidden=\"true\"></i>\n                <span>Help & Support</span>\n              </a>\n              <div class=\"dropdown-divider\"></div>\n              <a href=\"#\" class=\"dropdown-item logout-item\" @click=\"handleLogout\" role=\"menuitem\">\n                <i class=\"fas fa-sign-out-alt\" aria-hidden=\"true\"></i>\n                <span>Sign Out</span>\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Breadcrumb Navigation -->\n    <div class=\"breadcrumb-section\" v-if=\"showBreadcrumbs\">\n      <div class=\"header-container\">\n        <nav class=\"breadcrumb-nav\" aria-label=\"Breadcrumb navigation\">\n          <ol class=\"breadcrumb-list\">\n            <li class=\"breadcrumb-item\">\n              <a href=\"#\" @click=\"handleMenuAction('dashboard')\">\n                <i class=\"fas fa-home\" aria-hidden=\"true\"></i>\n                Dashboard\n              </a>\n            </li>\n            <li class=\"breadcrumb-item active\" aria-current=\"page\">\n              {{ getPageTitle() }}\n            </li>\n          </ol>\n        </nav>\n      </div>\n    </div>\n  </header>\n</template>\n\n<script>\nimport { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue';\nimport ClientNotifications from './ClientNotifications.vue';\n\nexport default {\n  name: 'ClientHeader',\n  components: {\n    ClientNotifications\n  },\n  props: {\n    userName: {\n      type: String,\n      default: 'User'\n    },\n    userEmail: {\n      type: String,\n      default: '<EMAIL>'\n    },\n    userAvatar: {\n      type: String,\n      default: null\n    },\n    showUserDropdown: {\n      type: Boolean,\n      default: false\n    },\n    sidebarCollapsed: {\n      type: Boolean,\n      default: false\n    },\n    activeMenu: {\n      type: String,\n      default: 'dashboard'\n    },\n    showBreadcrumbs: {\n      type: Boolean,\n      default: true\n    }\n  },\n\n  emits: [\n    'sidebar-toggle',\n    'user-dropdown-toggle',\n    'menu-action',\n    'logout',\n    'error',\n    'search',\n    'notification-click'\n  ],\n\n  setup(props, { emit }) {\n    // Reactive state\n    const showSearch = ref(false);\n    const showBannerDetails = ref(false);\n    const searchQuery = ref('');\n    const searchInput = ref(null);\n\n    // Computed properties\n    const getPageTitle = computed(() => {\n      const titles = {\n        dashboard: 'Dashboard',\n        services: 'Services',\n        requests: 'My Requests',\n        profile: 'My Profile',\n        settings: 'Settings',\n        help: 'Help & Support',\n        documents: 'My Documents',\n        history: 'Request History'\n      };\n      return titles[props.activeMenu] || 'Dashboard';\n    });\n\n    // Methods\n    const toggleSearch = async () => {\n      showSearch.value = !showSearch.value;\n\n      if (showSearch.value) {\n        await nextTick();\n        searchInput.value?.focus();\n      }\n    };\n\n    const closeSearch = () => {\n      showSearch.value = false;\n      searchQuery.value = '';\n    };\n\n    const toggleBannerDetails = () => {\n      showBannerDetails.value = !showBannerDetails.value;\n    };\n\n    const handleSearch = () => {\n      if (searchQuery.value.trim()) {\n        emit('search', searchQuery.value.trim());\n        closeSearch();\n      }\n    };\n\n    const performSearch = () => {\n      handleSearch();\n    };\n\n    const handleSidebarToggle = () => {\n      emit('sidebar-toggle');\n    };\n\n    const handleUserDropdownToggle = () => {\n      emit('user-dropdown-toggle');\n    };\n\n    const handleMenuAction = (action) => {\n      emit('menu-action', action);\n    };\n\n    const handleLogout = () => {\n      emit('logout');\n    };\n\n    const handleOutsideClick = (event) => {\n      // Check if click is outside user dropdown\n      if (!event.target.closest('.user-profile')) {\n        if (props.showUserDropdown) {\n          emit('user-dropdown-toggle');\n        }\n      }\n\n      // Check if click is outside search\n      if (!event.target.closest('.search-container')) {\n        showSearch.value = false;\n      }\n\n      // Check if click is outside banner\n      if (!event.target.closest('.gov-banner')) {\n        showBannerDetails.value = false;\n      }\n    };\n\n    // Notification event handlers\n    const handleNewNotification = (notification) => {\n      console.log('New notification received:', notification);\n      // Handle new notification - could show toast, update UI, etc.\n    };\n\n    const handleNotificationClick = (notification) => {\n      console.log('📊 ClientHeader: Handling notification click:', notification);\n\n      try {\n        // Parse notification data\n        const notificationData = typeof notification.data === 'string'\n          ? JSON.parse(notification.data)\n          : notification.data || {};\n\n        // Log navigation for debugging\n        console.log('📊 ClientHeader: Notification data:', notificationData);\n\n        // Additional header-specific logic can go here\n        // For example, updating header state, showing badges, etc.\n\n        // The navigation is now handled by the ClientNotifications component\n        // This handler can focus on header-specific updates\n\n      } catch (error) {\n        console.error('❌ ClientHeader: Error handling notification click:', error);\n      }\n\n      // Always emit the event for other components that might need it\n      emit('notification-click', notification);\n    };\n\n    const handleNotificationError = (error) => {\n      console.error('Notification error:', error);\n      emit('error', error);\n    };\n\n    // Lifecycle hooks\n    onMounted(() => {\n      document.addEventListener('click', handleOutsideClick);\n    });\n\n    onBeforeUnmount(() => {\n      document.removeEventListener('click', handleOutsideClick);\n    });\n\n    return {\n      // Reactive state\n      showSearch,\n      showBannerDetails,\n      searchQuery,\n      searchInput,\n\n      // Computed\n      getPageTitle,\n\n      // Methods\n      toggleSearch,\n      closeSearch,\n      toggleBannerDetails,\n      handleSearch,\n      performSearch,\n      handleSidebarToggle,\n      handleUserDropdownToggle,\n      handleMenuAction,\n      handleLogout,\n      handleOutsideClick,\n      handleNewNotification,\n      handleNotificationClick,\n      handleNotificationError\n    };\n  }\n};\n</script>\n\n<style scoped src=\"./css/clientHeader.css\"></style>\n"], "mappings": "AAqSA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,eAAe,EAAEC,QAAO,QAAS,KAAK;AACzE,OAAOC,mBAAkB,MAAO,2BAA2B;AAE3D,eAAe;EACbC,IAAI,EAAE,cAAc;EACpBC,UAAU,EAAE;IACVF;EACF,CAAC;EACDG,KAAK,EAAE;IACLC,QAAQ,EAAE;MACRC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDC,SAAS,EAAE;MACTH,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDE,UAAU,EAAE;MACVJ,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDG,gBAAgB,EAAE;MAChBL,IAAI,EAAEM,OAAO;MACbJ,OAAO,EAAE;IACX,CAAC;IACDK,gBAAgB,EAAE;MAChBP,IAAI,EAAEM,OAAO;MACbJ,OAAO,EAAE;IACX,CAAC;IACDM,UAAU,EAAE;MACVR,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDO,eAAe,EAAE;MACfT,IAAI,EAAEM,OAAO;MACbJ,OAAO,EAAE;IACX;EACF,CAAC;EAEDQ,KAAK,EAAE,CACL,gBAAgB,EAChB,sBAAsB,EACtB,aAAa,EACb,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,oBAAmB,CACpB;EAEDC,KAAKA,CAACb,KAAK,EAAE;IAAEc;EAAK,CAAC,EAAE;IACrB;IACA,MAAMC,UAAS,GAAIvB,GAAG,CAAC,KAAK,CAAC;IAC7B,MAAMwB,iBAAgB,GAAIxB,GAAG,CAAC,KAAK,CAAC;IACpC,MAAMyB,WAAU,GAAIzB,GAAG,CAAC,EAAE,CAAC;IAC3B,MAAM0B,WAAU,GAAI1B,GAAG,CAAC,IAAI,CAAC;;IAE7B;IACA,MAAM2B,YAAW,GAAI1B,QAAQ,CAAC,MAAM;MAClC,MAAM2B,MAAK,GAAI;QACbC,SAAS,EAAE,WAAW;QACtBC,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,aAAa;QACvBC,OAAO,EAAE,YAAY;QACrBC,QAAQ,EAAE,UAAU;QACpBC,IAAI,EAAE,gBAAgB;QACtBC,SAAS,EAAE,cAAc;QACzBC,OAAO,EAAE;MACX,CAAC;MACD,OAAOR,MAAM,CAACpB,KAAK,CAACU,UAAU,KAAK,WAAW;IAChD,CAAC,CAAC;;IAEF;IACA,MAAMmB,YAAW,GAAI,MAAAA,CAAA,KAAY;MAC/Bd,UAAU,CAACe,KAAI,GAAI,CAACf,UAAU,CAACe,KAAK;MAEpC,IAAIf,UAAU,CAACe,KAAK,EAAE;QACpB,MAAMlC,QAAQ,CAAC,CAAC;QAChBsB,WAAW,CAACY,KAAK,EAAEC,KAAK,CAAC,CAAC;MAC5B;IACF,CAAC;IAED,MAAMC,WAAU,GAAIA,CAAA,KAAM;MACxBjB,UAAU,CAACe,KAAI,GAAI,KAAK;MACxBb,WAAW,CAACa,KAAI,GAAI,EAAE;IACxB,CAAC;IAED,MAAMG,mBAAkB,GAAIA,CAAA,KAAM;MAChCjB,iBAAiB,CAACc,KAAI,GAAI,CAACd,iBAAiB,CAACc,KAAK;IACpD,CAAC;IAED,MAAMI,YAAW,GAAIA,CAAA,KAAM;MACzB,IAAIjB,WAAW,CAACa,KAAK,CAACK,IAAI,CAAC,CAAC,EAAE;QAC5BrB,IAAI,CAAC,QAAQ,EAAEG,WAAW,CAACa,KAAK,CAACK,IAAI,CAAC,CAAC,CAAC;QACxCH,WAAW,CAAC,CAAC;MACf;IACF,CAAC;IAED,MAAMI,aAAY,GAAIA,CAAA,KAAM;MAC1BF,YAAY,CAAC,CAAC;IAChB,CAAC;IAED,MAAMG,mBAAkB,GAAIA,CAAA,KAAM;MAChCvB,IAAI,CAAC,gBAAgB,CAAC;IACxB,CAAC;IAED,MAAMwB,wBAAuB,GAAIA,CAAA,KAAM;MACrCxB,IAAI,CAAC,sBAAsB,CAAC;IAC9B,CAAC;IAED,MAAMyB,gBAAe,GAAKC,MAAM,IAAK;MACnC1B,IAAI,CAAC,aAAa,EAAE0B,MAAM,CAAC;IAC7B,CAAC;IAED,MAAMC,YAAW,GAAIA,CAAA,KAAM;MACzB3B,IAAI,CAAC,QAAQ,CAAC;IAChB,CAAC;IAED,MAAM4B,kBAAiB,GAAKC,KAAK,IAAK;MACpC;MACA,IAAI,CAACA,KAAK,CAACC,MAAM,CAACC,OAAO,CAAC,eAAe,CAAC,EAAE;QAC1C,IAAI7C,KAAK,CAACO,gBAAgB,EAAE;UAC1BO,IAAI,CAAC,sBAAsB,CAAC;QAC9B;MACF;;MAEA;MACA,IAAI,CAAC6B,KAAK,CAACC,MAAM,CAACC,OAAO,CAAC,mBAAmB,CAAC,EAAE;QAC9C9B,UAAU,CAACe,KAAI,GAAI,KAAK;MAC1B;;MAEA;MACA,IAAI,CAACa,KAAK,CAACC,MAAM,CAACC,OAAO,CAAC,aAAa,CAAC,EAAE;QACxC7B,iBAAiB,CAACc,KAAI,GAAI,KAAK;MACjC;IACF,CAAC;;IAED;IACA,MAAMgB,qBAAoB,GAAKC,YAAY,IAAK;MAC9CC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEF,YAAY,CAAC;MACvD;IACF,CAAC;IAED,MAAMG,uBAAsB,GAAKH,YAAY,IAAK;MAChDC,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAEF,YAAY,CAAC;MAE1E,IAAI;QACF;QACA,MAAMI,gBAAe,GAAI,OAAOJ,YAAY,CAACK,IAAG,KAAM,QAAO,GACzDC,IAAI,CAACC,KAAK,CAACP,YAAY,CAACK,IAAI,IAC5BL,YAAY,CAACK,IAAG,IAAK,CAAC,CAAC;;QAE3B;QACAJ,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEE,gBAAgB,CAAC;;QAEpE;QACA;;QAEA;QACA;MAEF,EAAE,OAAOI,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;MAC5E;;MAEA;MACAzC,IAAI,CAAC,oBAAoB,EAAEiC,YAAY,CAAC;IAC1C,CAAC;IAED,MAAMS,uBAAsB,GAAKD,KAAK,IAAK;MACzCP,OAAO,CAACO,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CzC,IAAI,CAAC,OAAO,EAAEyC,KAAK,CAAC;IACtB,CAAC;;IAED;IACA7D,SAAS,CAAC,MAAM;MACd+D,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAEhB,kBAAkB,CAAC;IACxD,CAAC,CAAC;IAEF/C,eAAe,CAAC,MAAM;MACpB8D,QAAQ,CAACE,mBAAmB,CAAC,OAAO,EAAEjB,kBAAkB,CAAC;IAC3D,CAAC,CAAC;IAEF,OAAO;MACL;MACA3B,UAAU;MACVC,iBAAiB;MACjBC,WAAW;MACXC,WAAW;MAEX;MACAC,YAAY;MAEZ;MACAU,YAAY;MACZG,WAAW;MACXC,mBAAmB;MACnBC,YAAY;MACZE,aAAa;MACbC,mBAAmB;MACnBC,wBAAwB;MACxBC,gBAAgB;MAChBE,YAAY;MACZC,kBAAkB;MAClBI,qBAAqB;MACrBI,uBAAuB;MACvBM;IACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}