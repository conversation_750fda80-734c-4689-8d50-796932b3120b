{"ast": null, "code": "export default {\n  name: 'DocumentServicesSection',\n  props: {\n    documentTypes: {\n      type: Array,\n      default: () => []\n    },\n    loading: {\n      type: Boolean,\n      default: false\n    },\n    error: {\n      type: String,\n      default: null\n    }\n  },\n  emits: ['select-document-type', 'retry'],\n  methods: {\n    selectDocumentType(documentType) {\n      if (documentType.is_active) {\n        this.$emit('select-document-type', documentType);\n      }\n    },\n    formatCurrency(amount) {\n      return new Intl.NumberFormat('en-PH', {\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n      }).format(amount || 0);\n    },\n    getProcessingTime(typeName) {\n      const processingTimes = {\n        'Barangay Clearance': '1-2 business days',\n        'Certificate of Residency': '1-2 business days',\n        'Certificate of Indigency': '2-3 business days',\n        'Business Permit': '3-5 business days',\n        'Barangay ID': '5-7 business days',\n        'Certificate of Good Moral': '1-2 business days'\n      };\n      return processingTimes[typeName] || '3-5 business days';\n    },\n    getDocumentIconPath(typeName) {\n      const iconPaths = {\n        'Barangay Clearance': 'M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z',\n        'Certificate of Residency': 'M12,3L2,12H5V20H19V12H22L12,3M12,8.75A2.25,2.25 0 0,1 14.25,11A2.25,2.25 0 0,1 12,13.25A2.25,2.25 0 0,1 9.75,11A2.25,2.25 0 0,1 12,8.75Z',\n        'Certificate of Indigency': 'M17,18C15.89,18 15,18.89 15,20A3,3 0 0,0 18,23A3,3 0 0,0 21,20C21,18.89 20.1,18 19,18H17M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C7.59,22 4,18.41 4,14C4,9.59 7.59,6 12,6Z',\n        'Business Permit': 'M20,6C20.58,6 21.05,6.2 21.42,6.59C21.8,7 22,7.45 22,8V19C22,19.55 21.8,20 21.42,20.41C21.05,20.8 20.58,21 20,21H4C3.42,21 2.95,20.8 2.58,20.41C2.2,20 2,19.55 2,19V8C2,7.45 2.2,7 2.58,6.59C2.95,6.2 3.42,6 4,6H8V4C8,3.42 8.2,2.95 8.58,2.58C8.95,2.2 9.42,2 10,2H14C14.58,2 15.05,2.2 15.42,2.58C15.8,2.95 16,3.42 16,4V6H20M4,8V19H20V8H4M10,4V6H14V4H10Z',\n        'Barangay ID': 'M2,3H22C23.05,3 24,3.95 24,5V19C24,20.05 23.05,21 22,21H2C0.95,21 0,20.05 0,19V5C0,3.95 0.95,3 2,3M14,6V7H22V6H14M14,8V9H21.5L22,9V8H14M14,10V11H21V10H14M8,13.91C6,13.91 2,15 2,17V18H14V17C14,15 10,13.91 8,13.91M8,6A3,3 0 0,0 5,9A3,3 0 0,0 8,12A3,3 0 0,0 11,9A3,3 0 0,0 8,6Z',\n        'Certificate of Good Moral': 'M23,12L20.56,9.22L20.9,5.54L17.29,4.72L15.4,1.54L12,3L8.6,1.54L6.71,4.72L3.1,5.53L3.44,9.21L1,12L3.44,14.78L3.1,18.47L6.71,19.29L8.6,22.47L12,21L15.4,22.46L17.29,19.28L20.9,18.46L20.56,14.78L23,12M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z'\n      };\n      return iconPaths[typeName] || iconPaths['Barangay Clearance'];\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "documentTypes", "type", "Array", "default", "loading", "Boolean", "error", "String", "emits", "methods", "selectDocumentType", "documentType", "is_active", "$emit", "formatCurrency", "amount", "Intl", "NumberFormat", "minimumFractionDigits", "maximumFractionDigits", "format", "getProcessingTime", "typeName", "processingTimes", "getDocumentIconPath", "iconPaths"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\client\\sections\\DocumentServicesSection.vue"], "sourcesContent": ["<template>\n  <section class=\"services-section\" aria-labelledby=\"services-title\" ref=\"servicesSection\">\n    <div class=\"container\">\n      <div class=\"section-header\">\n        <h2 id=\"services-title\" class=\"section-title\">Available Document Services</h2>\n        <p class=\"section-description\">Select the type of document you need to request</p>\n      </div>\n\n      <!-- Loading State -->\n      <div v-if=\"loading\" class=\"loading-container\" role=\"status\" aria-live=\"polite\">\n        <div class=\"loading-spinner\">\n          <svg class=\"spinner\" aria-hidden=\"true\" viewBox=\"0 0 24 24\">\n            <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"2\" fill=\"none\" stroke-dasharray=\"31.416\" stroke-dashoffset=\"31.416\">\n              <animate attributeName=\"stroke-dasharray\" dur=\"2s\" values=\"0 31.416;15.708 15.708;0 31.416\" repeatCount=\"indefinite\"/>\n              <animate attributeName=\"stroke-dashoffset\" dur=\"2s\" values=\"0;-15.708;-31.416\" repeatCount=\"indefinite\"/>\n            </circle>\n          </svg>\n        </div>\n        <p>Loading available services...</p>\n      </div>\n\n      <!-- Error State -->\n      <div v-else-if=\"error\" class=\"error-container\" role=\"alert\">\n        <div class=\"error-content\">\n          <svg class=\"error-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n            <path d=\"M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z\"/>\n          </svg>\n          <h3>Unable to Load Services</h3>\n          <p>{{ error }}</p>\n          <button class=\"btn btn-primary retry-btn\" @click=\"$emit('retry')\" type=\"button\">\n            <svg class=\"btn-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z\"/>\n            </svg>\n            Try Again\n          </button>\n        </div>\n      </div>\n\n      <!-- Document Types Grid -->\n      <div v-else class=\"document-types-grid\">\n        <button\n          v-for=\"documentType in documentTypes\"\n          :key=\"documentType.id\"\n          class=\"document-card\"\n          @click=\"selectDocumentType(documentType)\"\n          :class=\"{ 'disabled': !documentType.is_active }\"\n          :disabled=\"!documentType.is_active\"\n          :aria-describedby=\"`doc-${documentType.id}-desc`\"\n          type=\"button\"\n        >\n          <div class=\"document-header\">\n            <div class=\"document-icon\">\n              <svg aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path :d=\"getDocumentIconPath(documentType.type_name)\"/>\n              </svg>\n            </div>\n            <div class=\"document-status\">\n              <span \n                v-if=\"!documentType.is_active\" \n                class=\"status-badge unavailable\"\n                aria-label=\"Service unavailable\"\n              >\n                Unavailable\n              </span>\n              <span \n                v-else \n                class=\"status-badge available\"\n                aria-label=\"Service available\"\n              >\n                Available\n              </span>\n            </div>\n          </div>\n\n          <div class=\"document-content\">\n            <h3 class=\"document-title\">{{ documentType.type_name }}</h3>\n            <p :id=\"`doc-${documentType.id}-desc`\" class=\"document-description\">\n              {{ documentType.description }}\n            </p>\n\n            <div class=\"document-details\">\n              <div class=\"detail-item\">\n                <svg class=\"detail-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M7,15H9C9,16.08 10.37,17 12,17C13.63,17 15,16.08 15,15C15,13.9 13.96,13.5 11.76,12.97C9.64,12.44 7,11.78 7,9C7,7.21 8.47,5.69 10.5,5.18V3H13.5V5.18C15.53,5.69 17,7.21 17,9H15C15,7.92 13.63,7 12,7C10.37,7 9,7.92 9,9C9,10.1 10.04,10.5 12.24,11.03C14.36,11.56 17,12.22 17,15C17,16.79 15.53,18.31 13.5,18.82V21H10.5V18.82C8.47,18.31 7,16.79 7,15Z\"/>\n                </svg>\n                <span class=\"detail-label\">Fee:</span>\n                <span class=\"detail-value fee-amount\">₱{{ formatCurrency(documentType.base_fee) }}</span>\n              </div>\n\n              <div class=\"detail-item\">\n                <svg class=\"detail-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z\"/>\n                </svg>\n                <span class=\"detail-label\">Processing:</span>\n                <span class=\"detail-value\">{{ getProcessingTime(documentType.type_name) }}</span>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"document-action\" v-if=\"documentType.is_active\">\n            <svg aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z\"/>\n            </svg>\n          </div>\n        </button>\n      </div>\n    </div>\n  </section>\n</template>\n\n<script>\nexport default {\n  name: 'DocumentServicesSection',\n  props: {\n    documentTypes: {\n      type: Array,\n      default: () => []\n    },\n    loading: {\n      type: Boolean,\n      default: false\n    },\n    error: {\n      type: String,\n      default: null\n    }\n  },\n  emits: [\n    'select-document-type',\n    'retry'\n  ],\n  methods: {\n    selectDocumentType(documentType) {\n      if (documentType.is_active) {\n        this.$emit('select-document-type', documentType);\n      }\n    },\n    \n    formatCurrency(amount) {\n      return new Intl.NumberFormat('en-PH', {\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n      }).format(amount || 0);\n    },\n    \n    getProcessingTime(typeName) {\n      const processingTimes = {\n        'Barangay Clearance': '1-2 business days',\n        'Certificate of Residency': '1-2 business days',\n        'Certificate of Indigency': '2-3 business days',\n        'Business Permit': '3-5 business days',\n        'Barangay ID': '5-7 business days',\n        'Certificate of Good Moral': '1-2 business days'\n      };\n      return processingTimes[typeName] || '3-5 business days';\n    },\n    \n    getDocumentIconPath(typeName) {\n      const iconPaths = {\n        'Barangay Clearance': 'M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z',\n        'Certificate of Residency': 'M12,3L2,12H5V20H19V12H22L12,3M12,8.75A2.25,2.25 0 0,1 14.25,11A2.25,2.25 0 0,1 12,13.25A2.25,2.25 0 0,1 9.75,11A2.25,2.25 0 0,1 12,8.75Z',\n        'Certificate of Indigency': 'M17,18C15.89,18 15,18.89 15,20A3,3 0 0,0 18,23A3,3 0 0,0 21,20C21,18.89 20.1,18 19,18H17M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C7.59,22 4,18.41 4,14C4,9.59 7.59,6 12,6Z',\n        'Business Permit': 'M20,6C20.58,6 21.05,6.2 21.42,6.59C21.8,7 22,7.45 22,8V19C22,19.55 21.8,20 21.42,20.41C21.05,20.8 20.58,21 20,21H4C3.42,21 2.95,20.8 2.58,20.41C2.2,20 2,19.55 2,19V8C2,7.45 2.2,7 2.58,6.59C2.95,6.2 3.42,6 4,6H8V4C8,3.42 8.2,2.95 8.58,2.58C8.95,2.2 9.42,2 10,2H14C14.58,2 15.05,2.2 15.42,2.58C15.8,2.95 16,3.42 16,4V6H20M4,8V19H20V8H4M10,4V6H14V4H10Z',\n        'Barangay ID': 'M2,3H22C23.05,3 24,3.95 24,5V19C24,20.05 23.05,21 22,21H2C0.95,21 0,20.05 0,19V5C0,3.95 0.95,3 2,3M14,6V7H22V6H14M14,8V9H21.5L22,9V8H14M14,10V11H21V10H14M8,13.91C6,13.91 2,15 2,17V18H14V17C14,15 10,13.91 8,13.91M8,6A3,3 0 0,0 5,9A3,3 0 0,0 8,12A3,3 0 0,0 11,9A3,3 0 0,0 8,6Z',\n        'Certificate of Good Moral': 'M23,12L20.56,9.22L20.9,5.54L17.29,4.72L15.4,1.54L12,3L8.6,1.54L6.71,4.72L3.1,5.53L3.44,9.21L1,12L3.44,14.78L3.1,18.47L6.71,19.29L8.6,22.47L12,21L15.4,22.46L17.29,19.28L20.9,18.46L20.56,14.78L23,12M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z'\n      };\n      return iconPaths[typeName] || iconPaths['Barangay Clearance'];\n    }\n  }\n};\n</script>\n\n<style scoped>\n/* Document Services Section */\n.services-section {\n  padding: var(--spacing-10) 0;\n  background: var(--color-bg-primary);\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 var(--spacing-4);\n}\n\n.section-header {\n  text-align: center;\n  margin-bottom: var(--spacing-10);\n}\n\n.section-title {\n  font-size: var(--font-size-2xl);\n  font-weight: 700;\n  color: var(--color-text-primary);\n  margin: 0 0 var(--spacing-3);\n  line-height: var(--line-height-3);\n}\n\n.section-description {\n  font-size: var(--font-size-lg);\n  color: var(--color-text-secondary);\n  margin: 0;\n  line-height: var(--line-height-5);\n}\n\n/* Loading State */\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: var(--spacing-12) var(--spacing-4);\n  color: var(--color-text-secondary);\n}\n\n.loading-spinner {\n  width: 48px;\n  height: 48px;\n  margin-bottom: var(--spacing-4);\n  color: var(--color-primary);\n}\n\n.spinner {\n  width: 100%;\n  height: 100%;\n}\n\n/* Error State */\n.error-container {\n  display: flex;\n  justify-content: center;\n  padding: var(--spacing-12) var(--spacing-4);\n}\n\n.error-content {\n  text-align: center;\n  max-width: 400px;\n}\n\n.error-icon {\n  width: 48px;\n  height: 48px;\n  color: var(--color-error);\n  margin-bottom: var(--spacing-4);\n}\n\n.error-content h3 {\n  font-size: var(--font-size-xl);\n  font-weight: 600;\n  color: var(--color-text-primary);\n  margin: 0 0 var(--spacing-3);\n}\n\n.error-content p {\n  color: var(--color-text-secondary);\n  margin: 0 0 var(--spacing-6);\n  line-height: var(--line-height-5);\n}\n\n.btn {\n  display: inline-flex;\n  align-items: center;\n  gap: var(--spacing-2);\n  padding: var(--spacing-3) var(--spacing-6);\n  border: 2px solid transparent;\n  border-radius: var(--radius-lg);\n  font-size: var(--font-size-md);\n  font-weight: 600;\n  text-decoration: none;\n  cursor: pointer;\n  transition: all var(--duration-base) var(--easing-standard);\n  min-height: 48px;\n}\n\n.btn-primary {\n  background: var(--color-primary);\n  color: var(--color-text-inverse);\n  border-color: var(--color-primary);\n}\n\n.btn-primary:hover,\n.btn-primary:focus {\n  background: var(--color-primary-dark);\n  border-color: var(--color-primary-dark);\n  transform: translateY(-2px);\n  box-shadow: var(--shadow-3);\n}\n\n.btn:focus {\n  outline: var(--focus-ring-width) solid var(--color-accent);\n  outline-offset: var(--focus-ring-offset);\n}\n\n.btn-icon {\n  width: 20px;\n  height: 20px;\n}\n\n/* Document Types Grid */\n.document-types-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\n  gap: var(--spacing-6);\n}\n\n.document-card {\n  display: flex;\n  flex-direction: column;\n  padding: var(--spacing-6);\n  background: var(--color-bg-primary);\n  border: 2px solid var(--color-border-light);\n  border-radius: var(--radius-xl);\n  transition: all var(--duration-base) var(--easing-standard);\n  cursor: pointer;\n  text-align: left;\n  width: 100%;\n}\n\n.document-card:hover:not(.disabled) {\n  border-color: var(--color-primary);\n  transform: translateY(-4px);\n  box-shadow: var(--shadow-4);\n}\n\n.document-card:focus:not(.disabled) {\n  outline: var(--focus-ring-width) solid var(--color-accent);\n  outline-offset: var(--focus-ring-offset);\n  border-color: var(--color-primary);\n}\n\n.document-card.disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  background: var(--color-bg-secondary);\n}\n\n.document-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: var(--spacing-4);\n}\n\n.document-icon {\n  width: 48px;\n  height: 48px;\n  background: var(--color-primary-lighter);\n  color: var(--color-primary);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.document-icon svg {\n  width: 24px;\n  height: 24px;\n}\n\n.status-badge {\n  padding: var(--spacing-1) var(--spacing-3);\n  border-radius: var(--radius-full);\n  font-size: var(--font-size-xs);\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.status-badge.available {\n  background: var(--color-success);\n  color: var(--color-text-inverse);\n}\n\n.status-badge.unavailable {\n  background: var(--color-error);\n  color: var(--color-text-inverse);\n}\n\n.document-content {\n  flex: 1;\n  margin-bottom: var(--spacing-4);\n}\n\n.document-title {\n  font-size: var(--font-size-lg);\n  font-weight: 600;\n  color: var(--color-text-primary);\n  margin: 0 0 var(--spacing-2);\n  line-height: var(--line-height-3);\n}\n\n.document-description {\n  font-size: var(--font-size-sm);\n  color: var(--color-text-secondary);\n  margin: 0 0 var(--spacing-4);\n  line-height: var(--line-height-5);\n}\n\n.document-details {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-2);\n}\n\n.detail-item {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-2);\n  font-size: var(--font-size-sm);\n}\n\n.detail-icon {\n  width: 16px;\n  height: 16px;\n  color: var(--color-text-tertiary);\n}\n\n.detail-label {\n  color: var(--color-text-secondary);\n  font-weight: 500;\n}\n\n.detail-value {\n  color: var(--color-text-primary);\n  font-weight: 600;\n}\n\n.fee-amount {\n  color: var(--color-primary);\n}\n\n.document-action {\n  align-self: flex-end;\n  width: 24px;\n  height: 24px;\n  color: var(--color-text-tertiary);\n  transition: all var(--duration-base) var(--easing-standard);\n}\n\n.document-card:hover:not(.disabled) .document-action {\n  color: var(--color-primary);\n  transform: translateX(4px);\n}\n\n.document-action svg {\n  width: 100%;\n  height: 100%;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .document-types-grid {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-4);\n  }\n  \n  .document-card {\n    padding: var(--spacing-5);\n  }\n}\n</style>\n"], "mappings": "AA+GA,eAAe;EACbA,IAAI,EAAE,yBAAyB;EAC/BC,KAAK,EAAE;IACLC,aAAa,EAAE;MACbC,IAAI,EAAEC,KAAK;MACXC,OAAO,EAAEA,CAAA,KAAM;IACjB,CAAC;IACDC,OAAO,EAAE;MACPH,IAAI,EAAEI,OAAO;MACbF,OAAO,EAAE;IACX,CAAC;IACDG,KAAK,EAAE;MACLL,IAAI,EAAEM,MAAM;MACZJ,OAAO,EAAE;IACX;EACF,CAAC;EACDK,KAAK,EAAE,CACL,sBAAsB,EACtB,OAAM,CACP;EACDC,OAAO,EAAE;IACPC,kBAAkBA,CAACC,YAAY,EAAE;MAC/B,IAAIA,YAAY,CAACC,SAAS,EAAE;QAC1B,IAAI,CAACC,KAAK,CAAC,sBAAsB,EAAEF,YAAY,CAAC;MAClD;IACF,CAAC;IAEDG,cAAcA,CAACC,MAAM,EAAE;MACrB,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;QACpCC,qBAAqB,EAAE,CAAC;QACxBC,qBAAqB,EAAE;MACzB,CAAC,CAAC,CAACC,MAAM,CAACL,MAAK,IAAK,CAAC,CAAC;IACxB,CAAC;IAEDM,iBAAiBA,CAACC,QAAQ,EAAE;MAC1B,MAAMC,eAAc,GAAI;QACtB,oBAAoB,EAAE,mBAAmB;QACzC,0BAA0B,EAAE,mBAAmB;QAC/C,0BAA0B,EAAE,mBAAmB;QAC/C,iBAAiB,EAAE,mBAAmB;QACtC,aAAa,EAAE,mBAAmB;QAClC,2BAA2B,EAAE;MAC/B,CAAC;MACD,OAAOA,eAAe,CAACD,QAAQ,KAAK,mBAAmB;IACzD,CAAC;IAEDE,mBAAmBA,CAACF,QAAQ,EAAE;MAC5B,MAAMG,SAAQ,GAAI;QAChB,oBAAoB,EAAE,yFAAyF;QAC/G,0BAA0B,EAAE,0IAA0I;QACtK,0BAA0B,EAAE,4KAA4K;QACxM,iBAAiB,EAAE,+VAA+V;QAClX,aAAa,EAAE,oRAAoR;QACnS,2BAA2B,EAAE;MAC/B,CAAC;MACD,OAAOA,SAAS,CAACH,QAAQ,KAAKG,SAAS,CAAC,oBAAoB,CAAC;IAC/D;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}