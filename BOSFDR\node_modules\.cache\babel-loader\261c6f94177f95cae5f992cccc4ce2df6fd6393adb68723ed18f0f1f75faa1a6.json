{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport documentRequestService from '@/services/documentRequestService';\nexport default {\n  name: 'NewDocumentRequest',\n  data() {\n    return {\n      documentTypes: [],\n      loading: true,\n      error: null\n    };\n  },\n  async mounted() {\n    await this.loadDocumentTypes();\n  },\n  methods: {\n    async loadDocumentTypes() {\n      try {\n        this.loading = true;\n        this.error = null;\n        const response = await documentRequestService.getDocumentTypes();\n        this.documentTypes = response.data || [];\n      } catch (error) {\n        console.error('Error loading document types:', error);\n        this.error = error.response?.data?.message || 'Failed to load available services';\n      } finally {\n        this.loading = false;\n      }\n    },\n    selectDocumentType(documentType) {\n      if (!documentType.is_active) return;\n\n      // Navigate to specific document request form\n      const routeName = this.getRouteForDocumentType(documentType.type_name);\n      if (routeName) {\n        this.$router.push({\n          name: routeName,\n          params: {\n            documentTypeId: documentType.id\n          }\n        });\n      }\n    },\n    getRouteForDocumentType(typeName) {\n      const routes = {\n        'Barangay Clearance': 'BarangayClearanceRequest',\n        'Cedula': 'CedulaRequest'\n      };\n      return routes[typeName];\n    },\n    getDocumentIcon(typeName) {\n      const icons = {\n        'Barangay Clearance': 'fas fa-certificate',\n        'Cedula': 'fas fa-id-card'\n      };\n      return icons[typeName] || 'fas fa-file-alt';\n    },\n    getProcessingTime(typeName) {\n      const times = {\n        'Barangay Clearance': '3-5 business days',\n        'Cedula': '1-2 business days'\n      };\n      return times[typeName] || '3-5 business days';\n    },\n    formatCurrency(amount) {\n      return parseFloat(amount).toFixed(2);\n    },\n    goBack() {\n      this.$router.push({\n        name: 'ClientDashboard'\n      });\n    },\n    openHelp() {\n      // TODO: Implement help/FAQ modal or page\n      console.log('Opening help...');\n    },\n    contactSupport() {\n      // TODO: Implement contact support functionality\n      console.log('Contacting support...');\n    }\n  }\n};", "map": {"version": 3, "names": ["documentRequestService", "name", "data", "documentTypes", "loading", "error", "mounted", "loadDocumentTypes", "methods", "response", "getDocumentTypes", "console", "message", "selectDocumentType", "documentType", "is_active", "routeName", "getRouteForDocumentType", "type_name", "$router", "push", "params", "documentTypeId", "id", "typeName", "routes", "getDocumentIcon", "icons", "getProcessingTime", "times", "formatCurrency", "amount", "parseFloat", "toFixed", "goBack", "openHelp", "log", "contactSupport"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\client\\NewDocumentRequest.vue"], "sourcesContent": ["<template>\n  <div class=\"new-request-page\">\n    <!-- Background Image -->\n    <div class=\"background-container\">\n      <div class=\"background-image\"></div>\n      <div class=\"background-overlay\"></div>\n    </div>\n\n    <!-- Header -->\n    <div class=\"page-header\">\n      <div class=\"header-content\">\n        <div class=\"header-main\">\n          <div class=\"logo-section\">\n            <img\n              src=\"@/assets/icon-of-bula.jpg\"\n              alt=\"Barangay Bula Official Seal\"\n              class=\"header-logo\"\n            />\n          </div>\n          <h1 class=\"page-title\">\n            <i class=\"fas fa-plus-circle\"></i>\n            New Document Request\n          </h1>\n          <p class=\"page-description\">\n            Choose the type of document you want to request from Barangay Bula\n          </p>\n        </div>\n        <button class=\"back-btn\" @click=\"goBack\">\n          <i class=\"fas fa-arrow-left\"></i>\n          Back to Dashboard\n        </button>\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div v-if=\"loading\" class=\"loading-container\">\n      <div class=\"loading-spinner\">\n        <i class=\"fas fa-spinner fa-spin\"></i>\n      </div>\n      <p>Loading available services...</p>\n    </div>\n\n    <!-- Error State -->\n    <div v-else-if=\"error\" class=\"error-container\">\n      <div class=\"error-content\">\n        <i class=\"fas fa-exclamation-triangle\"></i>\n        <h3>Unable to Load Services</h3>\n        <p>{{ error }}</p>\n        <button class=\"retry-btn\" @click=\"loadDocumentTypes\">\n          <i class=\"fas fa-redo\"></i>\n          Try Again\n        </button>\n      </div>\n    </div>\n\n    <!-- Document Types -->\n    <div v-else class=\"document-types-container\">\n      <div class=\"content-wrapper\">\n        <div class=\"document-types-grid\">\n        <div\n          v-for=\"documentType in documentTypes\"\n          :key=\"documentType.id\"\n          class=\"document-card\"\n          @click=\"selectDocumentType(documentType)\"\n          :class=\"{ 'disabled': !documentType.is_active }\"\n        >\n          <div class=\"document-icon\">\n            <i :class=\"getDocumentIcon(documentType.type_name)\"></i>\n          </div>\n          \n          <div class=\"document-content\">\n            <h3 class=\"document-title\">{{ documentType.type_name }}</h3>\n            <p class=\"document-description\">{{ documentType.description }}</p>\n            \n            <div class=\"document-details\">\n              <div class=\"fee-info\">\n                <span class=\"fee-label\">Base Fee:</span>\n                <span class=\"fee-amount\">₱{{ formatCurrency(documentType.base_fee) }}</span>\n              </div>\n              \n              <div class=\"processing-time\">\n                <i class=\"fas fa-clock\"></i>\n                <span>{{ getProcessingTime(documentType.type_name) }}</span>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"document-action\">\n            <span v-if=\"!documentType.is_active\" class=\"status-badge unavailable\">\n              Unavailable\n            </span>\n            <i v-else class=\"fas fa-chevron-right\"></i>\n          </div>\n        </div>\n      </div>\n\n      <!-- Information Section -->\n      <div class=\"info-section\">\n        <div class=\"info-card\">\n          <div class=\"info-header\">\n            <i class=\"fas fa-info-circle\"></i>\n            <h3>Important Information</h3>\n          </div>\n          <div class=\"info-content\">\n            <ul class=\"info-list\">\n              <li>\n                <i class=\"fas fa-check\"></i>\n                Ensure your profile information is complete and accurate\n              </li>\n              <li>\n                <i class=\"fas fa-check\"></i>\n                Have your valid ID and supporting documents ready\n              </li>\n              <li>\n                <i class=\"fas fa-check\"></i>\n                Processing time may vary depending on document verification\n              </li>\n              <li>\n                <i class=\"fas fa-check\"></i>\n                You can pay online using various payment methods\n              </li>\n            </ul>\n          </div>\n        </div>\n\n        <div class=\"help-card\">\n          <div class=\"help-header\">\n            <i class=\"fas fa-headset\"></i>\n            <h3>Need Help?</h3>\n          </div>\n          <div class=\"help-content\">\n            <p>If you have questions about document requirements or the application process:</p>\n            <div class=\"help-actions\">\n              <button class=\"help-btn\" @click=\"openHelp\">\n                <i class=\"fas fa-question-circle\"></i>\n                View FAQ\n              </button>\n              <button class=\"contact-btn\" @click=\"contactSupport\">\n                <i class=\"fas fa-phone\"></i>\n                Contact Support\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n      </div> <!-- Close content-wrapper -->\n    </div>\n  </div>\n</template>\n\n<script>\nimport documentRequestService from '@/services/documentRequestService';\n\nexport default {\n  name: 'NewDocumentRequest',\n  data() {\n    return {\n      documentTypes: [],\n      loading: true,\n      error: null\n    };\n  },\n  async mounted() {\n    await this.loadDocumentTypes();\n  },\n  methods: {\n    async loadDocumentTypes() {\n      try {\n        this.loading = true;\n        this.error = null;\n        \n        const response = await documentRequestService.getDocumentTypes();\n        this.documentTypes = response.data || [];\n        \n      } catch (error) {\n        console.error('Error loading document types:', error);\n        this.error = error.response?.data?.message || 'Failed to load available services';\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    selectDocumentType(documentType) {\n      if (!documentType.is_active) return;\n\n      // Navigate to specific document request form\n      const routeName = this.getRouteForDocumentType(documentType.type_name);\n      if (routeName) {\n        this.$router.push({ \n          name: routeName,\n          params: { documentTypeId: documentType.id }\n        });\n      }\n    },\n\n    getRouteForDocumentType(typeName) {\n      const routes = {\n        'Barangay Clearance': 'BarangayClearanceRequest',\n        'Cedula': 'CedulaRequest'\n      };\n      return routes[typeName];\n    },\n\n    getDocumentIcon(typeName) {\n      const icons = {\n        'Barangay Clearance': 'fas fa-certificate',\n        'Cedula': 'fas fa-id-card'\n      };\n      return icons[typeName] || 'fas fa-file-alt';\n    },\n\n    getProcessingTime(typeName) {\n      const times = {\n        'Barangay Clearance': '3-5 business days',\n        'Cedula': '1-2 business days'\n      };\n      return times[typeName] || '3-5 business days';\n    },\n\n    formatCurrency(amount) {\n      return parseFloat(amount).toFixed(2);\n    },\n\n    goBack() {\n      this.$router.push({ name: 'ClientDashboard' });\n    },\n\n    openHelp() {\n      // TODO: Implement help/FAQ modal or page\n      console.log('Opening help...');\n    },\n\n    contactSupport() {\n      // TODO: Implement contact support functionality\n      console.log('Contacting support...');\n    }\n  }\n};\n</script>\n\n<style scoped>\n.new-request-page {\n  position: relative;\n  min-height: 100vh;\n  overflow-x: hidden;\n}\n\n/* Background Setup */\n.background-container {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: -2;\n}\n\n.background-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  object-position: center;\n}\n\n.background-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    135deg,\n    rgba(0, 123, 191, 0.75) 0%,\n    rgba(0, 86, 134, 0.85) 50%,\n    rgba(26, 54, 93, 0.9) 100%\n  );\n  z-index: -1;\n}\n\n.page-header {\n  position: relative;\n  z-index: 1;\n  margin-bottom: 2rem;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: 1rem;\n  padding: 2rem;\n  margin: 2rem;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  gap: 2rem;\n}\n\n.header-main {\n  flex: 1;\n}\n\n.logo-section {\n  margin-bottom: 1.5rem;\n}\n\n.header-logo {\n  width: 80px;\n  height: 80px;\n  border-radius: 50%;\n  border: 3px solid rgba(0, 123, 191, 0.3);\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\n}\n\n.page-title {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #1a365d;\n  margin-bottom: 0.5rem;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.page-title i {\n  color: #007bbf;\n}\n\n.page-description {\n  font-size: 1.1rem;\n  color: #4a5568;\n  margin: 0;\n  line-height: 1.5;\n}\n\n.back-btn {\n  background: rgba(0, 123, 191, 0.1);\n  border: 2px solid #007bbf;\n  padding: 0.75rem 1.5rem;\n  border-radius: 0.75rem;\n  color: #007bbf;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  backdrop-filter: blur(5px);\n}\n\n.back-btn:hover {\n  background: #007bbf;\n  color: white;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(0, 123, 191, 0.3);\n}\n\n.loading-container, .error-container {\n  position: relative;\n  z-index: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n  text-align: center;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: 1rem;\n  margin: 2rem;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.loading-spinner i {\n  font-size: 3rem;\n  color: #007bbf;\n  margin-bottom: 1rem;\n}\n\n.error-content {\n  max-width: 400px;\n}\n\n.error-content i {\n  font-size: 3rem;\n  color: #e53e3e;\n  margin-bottom: 1rem;\n}\n\n.retry-btn {\n  background: linear-gradient(135deg, #007bbf, #005a86);\n  color: white;\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 0.75rem;\n  cursor: pointer;\n  margin-top: 1rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 15px rgba(0, 123, 191, 0.3);\n}\n\n.retry-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(0, 123, 191, 0.4);\n}\n\n.document-types-container {\n  position: relative;\n  z-index: 1;\n}\n\n.content-wrapper {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: 1rem;\n  padding: 2rem;\n  margin: 2rem;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.document-types-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n  gap: 1.5rem;\n  margin-bottom: 3rem;\n}\n\n.document-card {\n  background: rgba(255, 255, 255, 0.9);\n  backdrop-filter: blur(5px);\n  border: 2px solid rgba(0, 123, 191, 0.2);\n  border-radius: 1rem;\n  padding: 2rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);\n}\n\n.document-card:hover:not(.disabled) {\n  border-color: #007bbf;\n  transform: translateY(-5px);\n  box-shadow: 0 12px 30px rgba(0, 123, 191, 0.2);\n  background: rgba(255, 255, 255, 0.95);\n}\n\n.document-card.disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.document-icon {\n  flex-shrink: 0;\n  width: 4rem;\n  height: 4rem;\n  background: linear-gradient(135deg, #007bbf, #005a86);\n  border-radius: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 1.5rem;\n  box-shadow: 0 4px 15px rgba(0, 123, 191, 0.3);\n}\n\n.document-content {\n  flex: 1;\n}\n\n.document-title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 0.5rem;\n}\n\n.document-description {\n  color: #4a5568;\n  margin-bottom: 1rem;\n  line-height: 1.5;\n}\n\n.document-details {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.fee-info {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.fee-label {\n  color: #718096;\n  font-size: 0.9rem;\n}\n\n.fee-amount {\n  font-weight: 600;\n  color: #38a169;\n  font-size: 1.1rem;\n}\n\n.processing-time {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #718096;\n  font-size: 0.9rem;\n}\n\n.document-action {\n  flex-shrink: 0;\n  display: flex;\n  align-items: center;\n}\n\n.document-action i {\n  color: #a0aec0;\n  font-size: 1.25rem;\n}\n\n.status-badge.unavailable {\n  background: #fed7d7;\n  color: #c53030;\n  padding: 0.25rem 0.75rem;\n  border-radius: 1rem;\n  font-size: 0.8rem;\n  font-weight: 500;\n}\n\n.info-section {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 2rem;\n}\n\n.info-card, .help-card {\n  background: rgba(255, 255, 255, 0.9);\n  backdrop-filter: blur(5px);\n  border: 1px solid rgba(0, 123, 191, 0.2);\n  border-radius: 0.75rem;\n  padding: 1.5rem;\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);\n  transition: all 0.3s ease;\n}\n\n.info-card:hover, .help-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(0, 123, 191, 0.1);\n  background: rgba(255, 255, 255, 0.95);\n}\n\n.info-header, .help-header {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  margin-bottom: 1rem;\n}\n\n.info-header h3, .help-header h3 {\n  font-size: 1.1rem;\n  font-weight: 600;\n  color: #1a365d;\n  margin: 0;\n}\n\n.info-header i {\n  color: #007bbf;\n}\n\n.help-header i {\n  color: #38a169;\n}\n\n.info-list {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.info-list li {\n  display: flex;\n  align-items: flex-start;\n  gap: 0.75rem;\n  margin-bottom: 0.75rem;\n  color: #4a5568;\n  line-height: 1.5;\n}\n\n.info-list i {\n  color: #38a169;\n  margin-top: 0.125rem;\n  flex-shrink: 0;\n}\n\n.help-content p {\n  color: #4a5568;\n  margin-bottom: 1rem;\n  line-height: 1.5;\n}\n\n.help-actions {\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n}\n\n.help-btn, .contact-btn {\n  background: rgba(255, 255, 255, 0.8);\n  backdrop-filter: blur(5px);\n  border: 2px solid rgba(0, 123, 191, 0.3);\n  padding: 0.75rem 1rem;\n  border-radius: 0.75rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #4a5568;\n  font-weight: 500;\n}\n\n.help-btn:hover {\n  border-color: #007bbf;\n  color: #007bbf;\n  background: rgba(0, 123, 191, 0.1);\n  transform: translateY(-1px);\n  box-shadow: 0 4px 10px rgba(0, 123, 191, 0.2);\n}\n\n.contact-btn:hover {\n  border-color: #38a169;\n  color: #38a169;\n  background: rgba(56, 161, 105, 0.1);\n  transform: translateY(-1px);\n  box-shadow: 0 4px 10px rgba(56, 161, 105, 0.2);\n}\n\n@media (max-width: 768px) {\n  .page-header {\n    margin: 1rem;\n    padding: 1.5rem;\n  }\n\n  .content-wrapper {\n    margin: 1rem;\n    padding: 1.5rem;\n  }\n\n  .header-content {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 1.5rem;\n  }\n\n  .header-logo {\n    width: 60px;\n    height: 60px;\n  }\n\n  .page-title {\n    font-size: 1.5rem;\n  }\n\n  .document-types-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n\n  .document-card {\n    flex-direction: column;\n    text-align: center;\n    padding: 1.5rem;\n  }\n\n  .info-section {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n\n  .loading-container, .error-container {\n    margin: 1rem;\n    padding: 3rem 1.5rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .page-header {\n    margin: 0.5rem;\n    padding: 1rem;\n  }\n\n  .content-wrapper {\n    margin: 0.5rem;\n    padding: 1rem;\n  }\n\n  .header-logo {\n    width: 50px;\n    height: 50px;\n  }\n\n  .page-title {\n    font-size: 1.25rem;\n  }\n\n  .document-card {\n    padding: 1rem;\n  }\n\n  .info-card, .help-card {\n    padding: 1rem;\n  }\n\n  .loading-container, .error-container {\n    margin: 0.5rem;\n    padding: 2rem 1rem;\n  }\n}\n</style>\n"], "mappings": ";AAuJA,OAAOA,sBAAqB,MAAO,mCAAmC;AAEtE,eAAe;EACbC,IAAI,EAAE,oBAAoB;EAC1BC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,aAAa,EAAE,EAAE;MACjBC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE;IACT,CAAC;EACH,CAAC;EACD,MAAMC,OAAOA,CAAA,EAAG;IACd,MAAM,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAChC,CAAC;EACDC,OAAO,EAAE;IACP,MAAMD,iBAAiBA,CAAA,EAAG;MACxB,IAAI;QACF,IAAI,CAACH,OAAM,GAAI,IAAI;QACnB,IAAI,CAACC,KAAI,GAAI,IAAI;QAEjB,MAAMI,QAAO,GAAI,MAAMT,sBAAsB,CAACU,gBAAgB,CAAC,CAAC;QAChE,IAAI,CAACP,aAAY,GAAIM,QAAQ,CAACP,IAAG,IAAK,EAAE;MAE1C,EAAE,OAAOG,KAAK,EAAE;QACdM,OAAO,CAACN,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,IAAI,CAACA,KAAI,GAAIA,KAAK,CAACI,QAAQ,EAAEP,IAAI,EAAEU,OAAM,IAAK,mCAAmC;MACnF,UAAU;QACR,IAAI,CAACR,OAAM,GAAI,KAAK;MACtB;IACF,CAAC;IAEDS,kBAAkBA,CAACC,YAAY,EAAE;MAC/B,IAAI,CAACA,YAAY,CAACC,SAAS,EAAE;;MAE7B;MACA,MAAMC,SAAQ,GAAI,IAAI,CAACC,uBAAuB,CAACH,YAAY,CAACI,SAAS,CAAC;MACtE,IAAIF,SAAS,EAAE;QACb,IAAI,CAACG,OAAO,CAACC,IAAI,CAAC;UAChBnB,IAAI,EAAEe,SAAS;UACfK,MAAM,EAAE;YAAEC,cAAc,EAAER,YAAY,CAACS;UAAG;QAC5C,CAAC,CAAC;MACJ;IACF,CAAC;IAEDN,uBAAuBA,CAACO,QAAQ,EAAE;MAChC,MAAMC,MAAK,GAAI;QACb,oBAAoB,EAAE,0BAA0B;QAChD,QAAQ,EAAE;MACZ,CAAC;MACD,OAAOA,MAAM,CAACD,QAAQ,CAAC;IACzB,CAAC;IAEDE,eAAeA,CAACF,QAAQ,EAAE;MACxB,MAAMG,KAAI,GAAI;QACZ,oBAAoB,EAAE,oBAAoB;QAC1C,QAAQ,EAAE;MACZ,CAAC;MACD,OAAOA,KAAK,CAACH,QAAQ,KAAK,iBAAiB;IAC7C,CAAC;IAEDI,iBAAiBA,CAACJ,QAAQ,EAAE;MAC1B,MAAMK,KAAI,GAAI;QACZ,oBAAoB,EAAE,mBAAmB;QACzC,QAAQ,EAAE;MACZ,CAAC;MACD,OAAOA,KAAK,CAACL,QAAQ,KAAK,mBAAmB;IAC/C,CAAC;IAEDM,cAAcA,CAACC,MAAM,EAAE;MACrB,OAAOC,UAAU,CAACD,MAAM,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC;IACtC,CAAC;IAEDC,MAAMA,CAAA,EAAG;MACP,IAAI,CAACf,OAAO,CAACC,IAAI,CAAC;QAAEnB,IAAI,EAAE;MAAkB,CAAC,CAAC;IAChD,CAAC;IAEDkC,QAAQA,CAAA,EAAG;MACT;MACAxB,OAAO,CAACyB,GAAG,CAAC,iBAAiB,CAAC;IAChC,CAAC;IAEDC,cAAcA,CAAA,EAAG;MACf;MACA1B,OAAO,CAACyB,GAAG,CAAC,uBAAuB,CAAC;IACtC;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}