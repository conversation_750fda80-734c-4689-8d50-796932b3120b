{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, normalizeClass as _normalizeClass, vModelText as _vModelText, withKeys as _withKeys, withDirectives as _withDirectives, resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createStaticVNode as _createStaticVNode, Fragment as _Fragment } from \"vue\";\nimport _imports_0 from '@/assets/icon-of-bula.jpg';\nconst _hoisted_1 = {\n  class: \"client-header\",\n  role: \"banner\",\n  \"aria-label\": \"Site header\"\n};\nconst _hoisted_2 = {\n  class: \"gov-banner\",\n  \"aria-label\": \"Official website of Barangay Bula, General Santos City\"\n};\nconst _hoisted_3 = {\n  class: \"usa-accordion\"\n};\nconst _hoisted_4 = {\n  class: \"usa-banner__header\"\n};\nconst _hoisted_5 = {\n  class: \"usa-banner__inner\"\n};\nconst _hoisted_6 = [\"aria-expanded\"];\nconst _hoisted_7 = [\"hidden\"];\nconst _hoisted_8 = {\n  class: \"main-header\"\n};\nconst _hoisted_9 = {\n  class: \"header-container\"\n};\nconst _hoisted_10 = {\n  class: \"header-left\"\n};\nconst _hoisted_11 = [\"aria-label\"];\nconst _hoisted_12 = [\"aria-label\", \"aria-expanded\"];\nconst _hoisted_13 = {\n  class: \"main-navigation\",\n  role: \"navigation\",\n  \"aria-label\": \"Main navigation\"\n};\nconst _hoisted_14 = {\n  class: \"nav-list\"\n};\nconst _hoisted_15 = {\n  class: \"nav-item\"\n};\nconst _hoisted_16 = {\n  class: \"nav-item\"\n};\nconst _hoisted_17 = {\n  class: \"nav-item\"\n};\nconst _hoisted_18 = {\n  class: \"nav-item\"\n};\nconst _hoisted_19 = {\n  class: \"header-actions\"\n};\nconst _hoisted_20 = {\n  class: \"search-container\"\n};\nconst _hoisted_21 = [\"aria-expanded\"];\nconst _hoisted_22 = [\"aria-expanded\"];\nconst _hoisted_23 = {\n  class: \"user-avatar\"\n};\nconst _hoisted_24 = [\"src\", \"alt\"];\nconst _hoisted_25 = {\n  key: 1,\n  class: \"fas fa-user-circle avatar-icon\",\n  \"aria-hidden\": \"true\"\n};\nconst _hoisted_26 = {\n  class: \"user-info\"\n};\nconst _hoisted_27 = {\n  class: \"user-name\"\n};\nconst _hoisted_28 = {\n  key: 0,\n  class: \"user-dropdown-menu\",\n  role: \"menu\",\n  \"aria-label\": \"User account options\"\n};\nconst _hoisted_29 = {\n  class: \"dropdown-header\"\n};\nconst _hoisted_30 = {\n  class: \"user-details\"\n};\nconst _hoisted_31 = {\n  class: \"user-email\"\n};\nconst _hoisted_32 = {\n  key: 0,\n  class: \"breadcrumb-section\"\n};\nconst _hoisted_33 = {\n  class: \"header-container\"\n};\nconst _hoisted_34 = {\n  class: \"breadcrumb-nav\",\n  \"aria-label\": \"Breadcrumb navigation\"\n};\nconst _hoisted_35 = {\n  class: \"breadcrumb-list\"\n};\nconst _hoisted_36 = {\n  class: \"breadcrumb-item\"\n};\nconst _hoisted_37 = {\n  class: \"breadcrumb-item active\",\n  \"aria-current\": \"page\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_ClientNotifications = _resolveComponent(\"ClientNotifications\");\n  return _openBlock(), _createElementBlock(_Fragment, null, [_createCommentVNode(\" Skip Navigation Link for Accessibility \"), _cache[42] || (_cache[42] = _createElementVNode(\"a\", {\n    href: \"#main-content\",\n    class: \"skip-link\"\n  }, \"Skip to main content\", -1 /* HOISTED */)), _createElementVNode(\"header\", _hoisted_1, [_createCommentVNode(\" Government Banner - USWDS Compliant \"), _createElementVNode(\"section\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"header\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_cache[20] || (_cache[20] = _createStaticVNode(\"<div class=\\\"grid-col-auto\\\" data-v-237c3b88><img aria-hidden=\\\"true\\\" class=\\\"usa-banner__header-flag\\\" src=\\\"/assets/images/ph_flag_small.png\\\" alt=\\\"\\\" data-v-237c3b88></div><div class=\\\"grid-col-fill tablet:grid-col-auto\\\" aria-hidden=\\\"true\\\" data-v-237c3b88><p class=\\\"usa-banner__header-text\\\" data-v-237c3b88> An official website of Barangay Bula, General Santos City </p><p class=\\\"usa-banner__header-action\\\" data-v-237c3b88>Here&#39;s how you know</p></div>\", 2)), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"usa-accordion__button usa-banner__button\",\n    \"aria-expanded\": _ctx.showBannerDetails,\n    \"aria-controls\": \"gov-banner-content\",\n    onClick: _cache[0] || (_cache[0] = (...args) => _ctx.toggleBannerDetails && _ctx.toggleBannerDetails(...args))\n  }, _cache[19] || (_cache[19] = [_createElementVNode(\"span\", {\n    class: \"usa-banner__button-text\"\n  }, \"Here's how you know\", -1 /* HOISTED */)]), 8 /* PROPS */, _hoisted_6)])]), _createElementVNode(\"div\", {\n    class: \"usa-banner__content usa-accordion__content\",\n    id: \"gov-banner-content\",\n    hidden: !_ctx.showBannerDetails\n  }, _cache[21] || (_cache[21] = [_createElementVNode(\"div\", {\n    class: \"grid-row grid-gap-lg\"\n  }, [_createElementVNode(\"div\", {\n    class: \"usa-banner__guidance tablet:grid-col-6\"\n  }, [_createElementVNode(\"img\", {\n    class: \"usa-banner__icon usa-media-block__img\",\n    src: \"/assets/images/icon-dot-gov.svg\",\n    role: \"img\",\n    alt: \"\",\n    \"aria-hidden\": \"true\"\n  }), _createElementVNode(\"div\", {\n    class: \"usa-media-block__body\"\n  }, [_createElementVNode(\"p\", null, [_createElementVNode(\"strong\", null, \"Official websites use .gov.ph\"), _createElementVNode(\"br\"), _createTextVNode(\" A \"), _createElementVNode(\"strong\", null, \".gov.ph\"), _createTextVNode(\" website belongs to an official government organization in the Philippines. \")])])]), _createElementVNode(\"div\", {\n    class: \"usa-banner__guidance tablet:grid-col-6\"\n  }, [_createElementVNode(\"img\", {\n    class: \"usa-banner__icon usa-media-block__img\",\n    src: \"/assets/images/icon-https.svg\",\n    role: \"img\",\n    alt: \"\",\n    \"aria-hidden\": \"true\"\n  }), _createElementVNode(\"div\", {\n    class: \"usa-media-block__body\"\n  }, [_createElementVNode(\"p\", null, [_createElementVNode(\"strong\", null, \"Secure .gov.ph websites use HTTPS\"), _createElementVNode(\"br\"), _createTextVNode(\" A \"), _createElementVNode(\"strong\", null, \"lock\"), _createTextVNode(\" (🔒) or \"), _createElementVNode(\"strong\", null, \"https://\"), _createTextVNode(\" means you've safely connected to the .gov.ph website. Share sensitive information only on official, secure websites. \")])])])], -1 /* HOISTED */)]), 8 /* PROPS */, _hoisted_7)])]), _createCommentVNode(\" Main Header \"), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_createCommentVNode(\" Left Section: Logo and Navigation \"), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"button\", {\n    class: \"logo-section\",\n    onClick: _cache[1] || (_cache[1] = $event => $options.handleMenuAction('dashboard')),\n    \"aria-label\": `Go to ${$options.getPageTitle()} dashboard`,\n    type: \"button\"\n  }, _cache[22] || (_cache[22] = [_createElementVNode(\"img\", {\n    src: _imports_0,\n    alt: \"Barangay Bula Logo\",\n    class: \"logo\",\n    width: \"40\",\n    height: \"40\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"div\", {\n    class: \"site-identity\"\n  }, [_createElementVNode(\"h1\", {\n    class: \"site-title\"\n  }, \"Barangay Bula\"), _createElementVNode(\"span\", {\n    class: \"site-subtitle\"\n  }, \"Digital Services Portal\")], -1 /* HOISTED */)]), 8 /* PROPS */, _hoisted_11), _createCommentVNode(\" Mobile Menu Toggle \"), _createElementVNode(\"button\", {\n    class: \"mobile-menu-toggle\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.handleSidebarToggle && $options.handleSidebarToggle(...args)),\n    \"aria-label\": $props.sidebarCollapsed ? 'Open navigation menu' : 'Close navigation menu',\n    \"aria-expanded\": !$props.sidebarCollapsed,\n    type: \"button\"\n  }, [_createElementVNode(\"span\", {\n    class: _normalizeClass([\"hamburger-line\", {\n      active: !$props.sidebarCollapsed\n    }])\n  }, null, 2 /* CLASS */), _createElementVNode(\"span\", {\n    class: _normalizeClass([\"hamburger-line\", {\n      active: !$props.sidebarCollapsed\n    }])\n  }, null, 2 /* CLASS */), _createElementVNode(\"span\", {\n    class: _normalizeClass([\"hamburger-line\", {\n      active: !$props.sidebarCollapsed\n    }])\n  }, null, 2 /* CLASS */)], 8 /* PROPS */, _hoisted_12)]), _createCommentVNode(\" Center Section: Navigation (Desktop) \"), _createElementVNode(\"nav\", _hoisted_13, [_createElementVNode(\"ul\", _hoisted_14, [_createElementVNode(\"li\", _hoisted_15, [_createElementVNode(\"a\", {\n    href: \"#\",\n    class: _normalizeClass([\"nav-link\", {\n      active: $props.activeMenu === 'dashboard'\n    }]),\n    onClick: _cache[3] || (_cache[3] = $event => $options.handleMenuAction('dashboard')),\n    role: \"menuitem\"\n  }, _cache[23] || (_cache[23] = [_createElementVNode(\"i\", {\n    class: \"fas fa-home\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Dashboard\", -1 /* HOISTED */)]), 2 /* CLASS */)]), _createElementVNode(\"li\", _hoisted_16, [_createElementVNode(\"a\", {\n    href: \"#\",\n    class: _normalizeClass([\"nav-link\", {\n      active: $props.activeMenu === 'services'\n    }]),\n    onClick: _cache[4] || (_cache[4] = $event => $options.handleMenuAction('services')),\n    role: \"menuitem\"\n  }, _cache[24] || (_cache[24] = [_createElementVNode(\"i\", {\n    class: \"fas fa-file-alt\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Services\", -1 /* HOISTED */)]), 2 /* CLASS */)]), _createElementVNode(\"li\", _hoisted_17, [_createElementVNode(\"a\", {\n    href: \"#\",\n    class: _normalizeClass([\"nav-link\", {\n      active: $props.activeMenu === 'requests'\n    }]),\n    onClick: _cache[5] || (_cache[5] = $event => $options.handleMenuAction('requests')),\n    role: \"menuitem\"\n  }, _cache[25] || (_cache[25] = [_createElementVNode(\"i\", {\n    class: \"fas fa-clock\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"My Requests\", -1 /* HOISTED */)]), 2 /* CLASS */)]), _createElementVNode(\"li\", _hoisted_18, [_createElementVNode(\"a\", {\n    href: \"#\",\n    class: _normalizeClass([\"nav-link\", {\n      active: $props.activeMenu === 'help'\n    }]),\n    onClick: _cache[6] || (_cache[6] = $event => $options.handleMenuAction('help')),\n    role: \"menuitem\"\n  }, _cache[26] || (_cache[26] = [_createElementVNode(\"i\", {\n    class: \"fas fa-question-circle\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Help\", -1 /* HOISTED */)]), 2 /* CLASS */)])])]), _createCommentVNode(\" Right Section: User Actions \"), _createElementVNode(\"div\", _hoisted_19, [_createCommentVNode(\" Search \"), _createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"button\", {\n    class: \"search-toggle\",\n    onClick: _cache[7] || (_cache[7] = (...args) => $options.toggleSearch && $options.toggleSearch(...args)),\n    \"aria-label\": \"Search documents and services\",\n    \"aria-expanded\": $data.showSearch\n  }, _cache[27] || (_cache[27] = [_createElementVNode(\"i\", {\n    class: \"fas fa-search\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */)]), 8 /* PROPS */, _hoisted_21), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"search-box\", {\n      active: $data.showSearch\n    }]),\n    role: \"search\"\n  }, [_cache[29] || (_cache[29] = _createElementVNode(\"label\", {\n    for: \"header-search\",\n    class: \"sr-only\"\n  }, \"Search documents and services\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    id: \"header-search\",\n    type: \"search\",\n    placeholder: \"Search documents, services...\",\n    class: \"search-input\",\n    \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $data.searchQuery = $event),\n    onKeyup: _cache[9] || (_cache[9] = _withKeys((...args) => $options.performSearch && $options.performSearch(...args), [\"enter\"])),\n    autocomplete: \"off\"\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelText, $data.searchQuery]]), _createElementVNode(\"button\", {\n    class: \"search-submit\",\n    onClick: _cache[10] || (_cache[10] = (...args) => $options.performSearch && $options.performSearch(...args)),\n    \"aria-label\": \"Submit search\"\n  }, _cache[28] || (_cache[28] = [_createElementVNode(\"i\", {\n    class: \"fas fa-search\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */)]))], 2 /* CLASS */)]), _createCommentVNode(\" Notifications \"), _createVNode(_component_ClientNotifications, {\n    onNewNotification: $options.handleNewNotification,\n    onNotificationClick: $options.handleNotificationClick,\n    onError: $options.handleNotificationError\n  }, null, 8 /* PROPS */, [\"onNewNotification\", \"onNotificationClick\", \"onError\"]), _createCommentVNode(\" User Profile \"), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"user-profile\", {\n      active: $props.showUserDropdown\n    }])\n  }, [_createElementVNode(\"button\", {\n    class: \"user-btn\",\n    onClick: _cache[11] || (_cache[11] = (...args) => $options.handleUserDropdownToggle && $options.handleUserDropdownToggle(...args)),\n    \"aria-label\": \"User account menu\",\n    \"aria-expanded\": $props.showUserDropdown\n  }, [_createElementVNode(\"div\", _hoisted_23, [$props.userAvatar ? (_openBlock(), _createElementBlock(\"img\", {\n    key: 0,\n    src: $props.userAvatar,\n    alt: $props.userName,\n    class: \"avatar-image\"\n  }, null, 8 /* PROPS */, _hoisted_24)) : (_openBlock(), _createElementBlock(\"i\", _hoisted_25))]), _createElementVNode(\"div\", _hoisted_26, [_createElementVNode(\"span\", _hoisted_27, _toDisplayString($props.userName), 1 /* TEXT */), _cache[30] || (_cache[30] = _createElementVNode(\"span\", {\n    class: \"user-role\"\n  }, \"Client Portal\", -1 /* HOISTED */))]), _cache[31] || (_cache[31] = _createElementVNode(\"i\", {\n    class: \"fas fa-chevron-down dropdown-arrow\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */))], 8 /* PROPS */, _hoisted_22), $props.showUserDropdown ? (_openBlock(), _createElementBlock(\"div\", _hoisted_28, [_createElementVNode(\"div\", _hoisted_29, [_createElementVNode(\"div\", _hoisted_30, [_createElementVNode(\"strong\", null, _toDisplayString($props.userName), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_31, _toDisplayString($props.userEmail), 1 /* TEXT */)])]), _cache[38] || (_cache[38] = _createElementVNode(\"div\", {\n    class: \"dropdown-divider\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"dropdown-item\",\n    onClick: _cache[12] || (_cache[12] = $event => $options.handleMenuAction('profile')),\n    role: \"menuitem\"\n  }, _cache[32] || (_cache[32] = [_createElementVNode(\"i\", {\n    class: \"fas fa-user\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"My Profile\", -1 /* HOISTED */)])), _createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"dropdown-item\",\n    onClick: _cache[13] || (_cache[13] = $event => $options.handleMenuAction('settings')),\n    role: \"menuitem\"\n  }, _cache[33] || (_cache[33] = [_createElementVNode(\"i\", {\n    class: \"fas fa-cog\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Account Settings\", -1 /* HOISTED */)])), _createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"dropdown-item\",\n    onClick: _cache[14] || (_cache[14] = $event => $options.handleMenuAction('documents')),\n    role: \"menuitem\"\n  }, _cache[34] || (_cache[34] = [_createElementVNode(\"i\", {\n    class: \"fas fa-folder\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"My Documents\", -1 /* HOISTED */)])), _createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"dropdown-item\",\n    onClick: _cache[15] || (_cache[15] = $event => $options.handleMenuAction('history')),\n    role: \"menuitem\"\n  }, _cache[35] || (_cache[35] = [_createElementVNode(\"i\", {\n    class: \"fas fa-history\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Request History\", -1 /* HOISTED */)])), _cache[39] || (_cache[39] = _createElementVNode(\"div\", {\n    class: \"dropdown-divider\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"dropdown-item\",\n    onClick: _cache[16] || (_cache[16] = $event => $options.handleMenuAction('help')),\n    role: \"menuitem\"\n  }, _cache[36] || (_cache[36] = [_createElementVNode(\"i\", {\n    class: \"fas fa-question-circle\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Help & Support\", -1 /* HOISTED */)])), _cache[40] || (_cache[40] = _createElementVNode(\"div\", {\n    class: \"dropdown-divider\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"dropdown-item logout-item\",\n    onClick: _cache[17] || (_cache[17] = (...args) => $options.handleLogout && $options.handleLogout(...args)),\n    role: \"menuitem\"\n  }, _cache[37] || (_cache[37] = [_createElementVNode(\"i\", {\n    class: \"fas fa-sign-out-alt\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Sign Out\", -1 /* HOISTED */)]))])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)])])]), _createCommentVNode(\" Breadcrumb Navigation \"), $props.showBreadcrumbs ? (_openBlock(), _createElementBlock(\"div\", _hoisted_32, [_createElementVNode(\"div\", _hoisted_33, [_createElementVNode(\"nav\", _hoisted_34, [_createElementVNode(\"ol\", _hoisted_35, [_createElementVNode(\"li\", _hoisted_36, [_createElementVNode(\"a\", {\n    href: \"#\",\n    onClick: _cache[18] || (_cache[18] = $event => $options.handleMenuAction('dashboard'))\n  }, _cache[41] || (_cache[41] = [_createElementVNode(\"i\", {\n    class: \"fas fa-home\",\n    \"aria-hidden\": \"true\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Dashboard \")]))]), _createElementVNode(\"li\", _hoisted_37, _toDisplayString($options.getPageTitle()), 1 /* TEXT */)])])])])) : _createCommentVNode(\"v-if\", true)])], 64 /* STABLE_FRAGMENT */);\n}", "map": {"version": 3, "names": ["_imports_0", "class", "role", "_createCommentVNode", "_createElementVNode", "href", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "type", "_ctx", "showBannerDetails", "onClick", "_cache", "args", "toggleBannerDetails", "id", "hidden", "src", "alt", "_hoisted_8", "_hoisted_9", "_hoisted_10", "$event", "$options", "handleMenuAction", "getPageTitle", "width", "height", "handleSidebarToggle", "$props", "sidebarCollapsed", "_normalizeClass", "active", "_hoisted_13", "_hoisted_14", "_hoisted_15", "activeMenu", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "toggleSearch", "$data", "showSearch", "for", "placeholder", "searchQuery", "onKeyup", "_with<PERSON><PERSON><PERSON>", "performSearch", "autocomplete", "_createVNode", "_component_ClientNotifications", "onNewNotification", "handleNewNotification", "onNotificationClick", "handleNotificationClick", "onError", "handleNotificationError", "showUserDropdown", "handleUserDropdownToggle", "_hoisted_23", "userAvatar", "_createElementBlock", "userName", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_toDisplayString", "_hoisted_28", "_hoisted_29", "_hoisted_30", "_hoisted_31", "userEmail", "handleLogout", "showBreadcrumbs", "_hoisted_32", "_hoisted_33", "_hoisted_34", "_hoisted_35", "_hoisted_36", "_hoisted_37"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\client\\ClientHeader.vue"], "sourcesContent": ["<template>\n  <!-- Skip Navigation Link for Accessibility -->\n  <a href=\"#main-content\" class=\"skip-link\">Skip to main content</a>\n\n  <header class=\"client-header\" role=\"banner\" aria-label=\"Site header\">\n    <!-- Government Banner - USWDS Compliant -->\n    <section\n      class=\"gov-banner\"\n      aria-label=\"Official website of Barangay Bula, General Santos City\"\n    >\n      <div class=\"usa-accordion\">\n        <header class=\"usa-banner__header\">\n          <div class=\"usa-banner__inner\">\n            <div class=\"grid-col-auto\">\n              <img\n                aria-hidden=\"true\"\n                class=\"usa-banner__header-flag\"\n                src=\"/assets/images/ph_flag_small.png\"\n                alt=\"\"\n              />\n            </div>\n            <div class=\"grid-col-fill tablet:grid-col-auto\" aria-hidden=\"true\">\n              <p class=\"usa-banner__header-text\">\n                An official website of Barangay Bula, General Santos City\n              </p>\n              <p class=\"usa-banner__header-action\">Here's how you know</p>\n            </div>\n            <button\n              type=\"button\"\n              class=\"usa-accordion__button usa-banner__button\"\n              :aria-expanded=\"showBannerDetails\"\n              aria-controls=\"gov-banner-content\"\n              @click=\"toggleBannerDetails\"\n            >\n              <span class=\"usa-banner__button-text\">Here's how you know</span>\n            </button>\n          </div>\n        </header>\n        <div\n          class=\"usa-banner__content usa-accordion__content\"\n          id=\"gov-banner-content\"\n          :hidden=\"!showBannerDetails\"\n        >\n          <div class=\"grid-row grid-gap-lg\">\n            <div class=\"usa-banner__guidance tablet:grid-col-6\">\n              <img\n                class=\"usa-banner__icon usa-media-block__img\"\n                src=\"/assets/images/icon-dot-gov.svg\"\n                role=\"img\"\n                alt=\"\"\n                aria-hidden=\"true\"\n              />\n              <div class=\"usa-media-block__body\">\n                <p>\n                  <strong>Official websites use .gov.ph</strong><br />\n                  A <strong>.gov.ph</strong> website belongs to an official government\n                  organization in the Philippines.\n                </p>\n              </div>\n            </div>\n            <div class=\"usa-banner__guidance tablet:grid-col-6\">\n              <img\n                class=\"usa-banner__icon usa-media-block__img\"\n                src=\"/assets/images/icon-https.svg\"\n                role=\"img\"\n                alt=\"\"\n                aria-hidden=\"true\"\n              />\n              <div class=\"usa-media-block__body\">\n                <p>\n                  <strong>Secure .gov.ph websites use HTTPS</strong><br />\n                  A <strong>lock</strong> (🔒) or <strong>https://</strong> means you've\n                  safely connected to the .gov.ph website. Share sensitive information\n                  only on official, secure websites.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- Main Header -->\n    <div class=\"main-header\">\n      <div class=\"header-container\">\n        <!-- Left Section: Logo and Navigation -->\n        <div class=\"header-left\">\n          <button\n            class=\"logo-section\"\n            @click=\"handleMenuAction('dashboard')\"\n            :aria-label=\"`Go to ${getPageTitle()} dashboard`\"\n            type=\"button\"\n          >\n            <img\n              src=\"@/assets/icon-of-bula.jpg\"\n              alt=\"Barangay Bula Logo\"\n              class=\"logo\"\n              width=\"40\"\n              height=\"40\"\n            />\n            <div class=\"site-identity\">\n              <h1 class=\"site-title\">Barangay Bula</h1>\n              <span class=\"site-subtitle\">Digital Services Portal</span>\n            </div>\n          </button>\n\n          <!-- Mobile Menu Toggle -->\n          <button\n            class=\"mobile-menu-toggle\"\n            @click=\"handleSidebarToggle\"\n            :aria-label=\"sidebarCollapsed ? 'Open navigation menu' : 'Close navigation menu'\"\n            :aria-expanded=\"!sidebarCollapsed\"\n            type=\"button\"\n          >\n            <span class=\"hamburger-line\" :class=\"{ active: !sidebarCollapsed }\"></span>\n            <span class=\"hamburger-line\" :class=\"{ active: !sidebarCollapsed }\"></span>\n            <span class=\"hamburger-line\" :class=\"{ active: !sidebarCollapsed }\"></span>\n          </button>\n        </div>\n\n        <!-- Center Section: Navigation (Desktop) -->\n        <nav class=\"main-navigation\" role=\"navigation\" aria-label=\"Main navigation\">\n          <ul class=\"nav-list\">\n            <li class=\"nav-item\">\n              <a href=\"#\" class=\"nav-link\" :class=\"{ active: activeMenu === 'dashboard' }\" @click=\"handleMenuAction('dashboard')\" role=\"menuitem\">\n                <i class=\"fas fa-home\" aria-hidden=\"true\"></i>\n                <span>Dashboard</span>\n              </a>\n            </li>\n            <li class=\"nav-item\">\n              <a href=\"#\" class=\"nav-link\" :class=\"{ active: activeMenu === 'services' }\" @click=\"handleMenuAction('services')\" role=\"menuitem\">\n                <i class=\"fas fa-file-alt\" aria-hidden=\"true\"></i>\n                <span>Services</span>\n              </a>\n            </li>\n            <li class=\"nav-item\">\n              <a href=\"#\" class=\"nav-link\" :class=\"{ active: activeMenu === 'requests' }\" @click=\"handleMenuAction('requests')\" role=\"menuitem\">\n                <i class=\"fas fa-clock\" aria-hidden=\"true\"></i>\n                <span>My Requests</span>\n              </a>\n            </li>\n            <li class=\"nav-item\">\n              <a href=\"#\" class=\"nav-link\" :class=\"{ active: activeMenu === 'help' }\" @click=\"handleMenuAction('help')\" role=\"menuitem\">\n                <i class=\"fas fa-question-circle\" aria-hidden=\"true\"></i>\n                <span>Help</span>\n              </a>\n            </li>\n          </ul>\n        </nav>\n\n        <!-- Right Section: User Actions -->\n        <div class=\"header-actions\">\n          <!-- Search -->\n          <div class=\"search-container\">\n            <button class=\"search-toggle\" @click=\"toggleSearch\" aria-label=\"Search documents and services\" :aria-expanded=\"showSearch\">\n              <i class=\"fas fa-search\" aria-hidden=\"true\"></i>\n            </button>\n            <div class=\"search-box\" :class=\"{ active: showSearch }\" role=\"search\">\n              <label for=\"header-search\" class=\"sr-only\">Search documents and services</label>\n              <input\n                id=\"header-search\"\n                type=\"search\"\n                placeholder=\"Search documents, services...\"\n                class=\"search-input\"\n                v-model=\"searchQuery\"\n                @keyup.enter=\"performSearch\"\n                autocomplete=\"off\"\n              />\n              <button class=\"search-submit\" @click=\"performSearch\" aria-label=\"Submit search\">\n                <i class=\"fas fa-search\" aria-hidden=\"true\"></i>\n              </button>\n            </div>\n          </div>\n\n          <!-- Notifications -->\n          <ClientNotifications\n            @new-notification=\"handleNewNotification\"\n            @notification-click=\"handleNotificationClick\"\n            @error=\"handleNotificationError\"\n          />\n\n          <!-- User Profile -->\n          <div class=\"user-profile\" :class=\"{ active: showUserDropdown }\">\n            <button class=\"user-btn\" @click=\"handleUserDropdownToggle\" aria-label=\"User account menu\" :aria-expanded=\"showUserDropdown\">\n              <div class=\"user-avatar\">\n                <img v-if=\"userAvatar\" :src=\"userAvatar\" :alt=\"userName\" class=\"avatar-image\" />\n                <i v-else class=\"fas fa-user-circle avatar-icon\" aria-hidden=\"true\"></i>\n              </div>\n              <div class=\"user-info\">\n                <span class=\"user-name\">{{ userName }}</span>\n                <span class=\"user-role\">Client Portal</span>\n              </div>\n              <i class=\"fas fa-chevron-down dropdown-arrow\" aria-hidden=\"true\"></i>\n            </button>\n\n            <div v-if=\"showUserDropdown\" class=\"user-dropdown-menu\" role=\"menu\" aria-label=\"User account options\">\n              <div class=\"dropdown-header\">\n                <div class=\"user-details\">\n                  <strong>{{ userName }}</strong>\n                  <span class=\"user-email\">{{ userEmail }}</span>\n                </div>\n              </div>\n              <div class=\"dropdown-divider\"></div>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('profile')\" role=\"menuitem\">\n                <i class=\"fas fa-user\" aria-hidden=\"true\"></i>\n                <span>My Profile</span>\n              </a>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('settings')\" role=\"menuitem\">\n                <i class=\"fas fa-cog\" aria-hidden=\"true\"></i>\n                <span>Account Settings</span>\n              </a>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('documents')\" role=\"menuitem\">\n                <i class=\"fas fa-folder\" aria-hidden=\"true\"></i>\n                <span>My Documents</span>\n              </a>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('history')\" role=\"menuitem\">\n                <i class=\"fas fa-history\" aria-hidden=\"true\"></i>\n                <span>Request History</span>\n              </a>\n              <div class=\"dropdown-divider\"></div>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('help')\" role=\"menuitem\">\n                <i class=\"fas fa-question-circle\" aria-hidden=\"true\"></i>\n                <span>Help & Support</span>\n              </a>\n              <div class=\"dropdown-divider\"></div>\n              <a href=\"#\" class=\"dropdown-item logout-item\" @click=\"handleLogout\" role=\"menuitem\">\n                <i class=\"fas fa-sign-out-alt\" aria-hidden=\"true\"></i>\n                <span>Sign Out</span>\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Breadcrumb Navigation -->\n    <div class=\"breadcrumb-section\" v-if=\"showBreadcrumbs\">\n      <div class=\"header-container\">\n        <nav class=\"breadcrumb-nav\" aria-label=\"Breadcrumb navigation\">\n          <ol class=\"breadcrumb-list\">\n            <li class=\"breadcrumb-item\">\n              <a href=\"#\" @click=\"handleMenuAction('dashboard')\">\n                <i class=\"fas fa-home\" aria-hidden=\"true\"></i>\n                Dashboard\n              </a>\n            </li>\n            <li class=\"breadcrumb-item active\" aria-current=\"page\">\n              {{ getPageTitle() }}\n            </li>\n          </ol>\n        </nav>\n      </div>\n    </div>\n  </header>\n</template>\n\n<script>\nimport ClientNotifications from './ClientNotifications.vue';\n\nexport default {\n  name: 'ClientHeader',\n  components: {\n    ClientNotifications\n  },\n  props: {\n    userName: {\n      type: String,\n      default: 'User'\n    },\n    userEmail: {\n      type: String,\n      default: '<EMAIL>'\n    },\n    userAvatar: {\n      type: String,\n      default: null\n    },\n    showUserDropdown: {\n      type: Boolean,\n      default: false\n    },\n    sidebarCollapsed: {\n      type: Boolean,\n      default: false\n    },\n    activeMenu: {\n      type: String,\n      default: 'dashboard'\n    },\n    showBreadcrumbs: {\n      type: Boolean,\n      default: true\n    }\n  },\n\n  data() {\n    return {\n      showSearch: false,\n      searchQuery: ''\n    };\n  },\n\n  emits: [\n    'sidebar-toggle',\n    'user-dropdown-toggle',\n    'menu-action',\n    'logout',\n    'error',\n    'search',\n    'notification-click'\n  ],\n\n  mounted() {\n    // Setup event listeners for outside clicks\n    document.addEventListener('click', this.handleOutsideClick);\n  },\n\n  beforeUnmount() {\n    // Clean up event listeners\n    document.removeEventListener('click', this.handleOutsideClick);\n  },\n\n  methods: {\n    // Get page title based on active menu\n    getPageTitle() {\n      const titles = {\n        'dashboard': 'Dashboard',\n        'services': 'Document Services',\n        'requests': 'My Requests',\n        'documents': 'My Documents',\n        'profile': 'My Profile',\n        'settings': 'Account Settings',\n        'history': 'Request History',\n        'notifications': 'Notifications',\n        'help': 'Help & Support'\n      };\n      return titles[this.activeMenu] || 'Dashboard';\n    },\n\n    // Toggle search functionality\n    toggleSearch() {\n      this.showSearch = !this.showSearch;\n      if (this.showSearch) {\n        this.$nextTick(() => {\n          const searchInput = this.$el.querySelector('.search-input');\n          if (searchInput) {\n            searchInput.focus();\n          }\n        });\n      }\n    },\n\n    // Perform search\n    performSearch() {\n      if (this.searchQuery.trim()) {\n        this.$emit('search', this.searchQuery.trim());\n        // Close search on mobile after search\n        if (window.innerWidth <= 768) {\n          this.showSearch = false;\n        }\n      }\n    },\n\n    // Handle sidebar toggle\n    handleSidebarToggle() {\n      this.$emit('sidebar-toggle');\n    },\n\n    // Handle user dropdown toggle\n    handleUserDropdownToggle() {\n      this.$emit('user-dropdown-toggle');\n    },\n\n    // Handle menu actions (profile, settings, etc.)\n    handleMenuAction(action) {\n      this.$emit('menu-action', action);\n    },\n\n    // Handle logout\n    handleLogout() {\n      this.$emit('logout');\n    },\n\n    // Handle outside clicks to close dropdowns\n    handleOutsideClick(event) {\n      // Check if click is outside user dropdown\n      if (!event.target.closest('.user-profile')) {\n        if (this.showUserDropdown) {\n          this.$emit('user-dropdown-toggle');\n        }\n      }\n\n      // Check if click is outside search\n      if (!event.target.closest('.search-container')) {\n        this.showSearch = false;\n      }\n    },\n\n    // Notification event handlers\n    handleNewNotification(notification) {\n      console.log('New notification received:', notification);\n      // Handle new notification - could show toast, update UI, etc.\n    },\n\n    async handleNotificationClick(notification) {\n      console.log('🔔 ClientHeader: Notification clicked:', notification);\n\n      // Ensure we have a valid notification object\n      if (!notification || typeof notification !== 'object') {\n        console.error('Invalid notification object received:', notification);\n        return;\n      }\n\n      try {\n        // The ClientNotifications component now handles navigation internally,\n        // but we can add additional logic here if needed\n\n        // Parse notification data\n        const notificationData = typeof notification.data === 'string'\n          ? JSON.parse(notification.data)\n          : notification.data || {};\n\n        // Log navigation for debugging\n        console.log('📊 ClientHeader: Notification data:', notificationData);\n\n        // Additional header-specific logic can go here\n        // For example, updating header state, showing badges, etc.\n\n        // The navigation is now handled by the ClientNotifications component\n        // This handler can focus on header-specific updates\n\n      } catch (error) {\n        console.error('❌ ClientHeader: Error handling notification click:', error);\n      }\n\n      // Always emit the event for other components that might need it\n      this.$emit('notification-click', notification);\n    },\n\n    handleNotificationError(error) {\n      console.error('Notification error:', error);\n      this.$emit('error', error);\n    }\n  }\n};\n</script>\n\n<style scoped src=\"./css/clientHeader.css\"></style>\n"], "mappings": ";OA8FcA,UAA+B;;EA1FnCC,KAAK,EAAC,eAAe;EAACC,IAAI,EAAC,QAAQ;EAAC,YAAU,EAAC;;;EAGnDD,KAAK,EAAC,YAAY;EAClB,YAAU,EAAC;;;EAENA,KAAK,EAAC;AAAe;;EAChBA,KAAK,EAAC;AAAoB;;EAC3BA,KAAK,EAAC;AAAmB;;;;EAuE/BA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAkB;;EAEtBA,KAAK,EAAC;AAAa;;;;EAmCnBA,KAAK,EAAC,iBAAiB;EAACC,IAAI,EAAC,YAAY;EAAC,YAAU,EAAC;;;EACpDD,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAU;;EAMhBA,KAAK,EAAC;AAAU;;EAMhBA,KAAK,EAAC;AAAU;;EAMhBA,KAAK,EAAC;AAAU;;EAUnBA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAkB;;;;EA+BpBA,KAAK,EAAC;AAAa;;;;EAEZA,KAAK,EAAC,gCAAgC;EAAC,aAAW,EAAC;;;EAE1DA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAW;;;EAMEA,KAAK,EAAC,oBAAoB;EAACC,IAAI,EAAC,MAAM;EAAC,YAAU,EAAC;;;EACxED,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAc;;EAEjBA,KAAK,EAAC;AAAY;;;EAqCjCA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC,gBAAgB;EAAC,YAAU,EAAC;;;EACjCA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAiB;;EAMvBA,KAAK,EAAC,wBAAwB;EAAC,cAAY,EAAC;;;;6DArP1DE,mBAAA,4CAA+C,E,4BAC/CC,mBAAA,CAAkE;IAA/DC,IAAI,EAAC,eAAe;IAACJ,KAAK,EAAC;KAAY,sBAAoB,sBAE9DG,mBAAA,CAyPS,UAzPTE,UAyPS,GAxPPH,mBAAA,yCAA4C,EAC5CC,mBAAA,CA0EU,WA1EVG,UA0EU,GAtERH,mBAAA,CAqEM,OArENI,UAqEM,GApEJJ,mBAAA,CA0BS,UA1BTK,UA0BS,GAzBPL,mBAAA,CAwBM,OAxBNM,UAwBM,G,4gBATJN,mBAAA,CAQS;IAPPO,IAAI,EAAC,QAAQ;IACbV,KAAK,EAAC,0CAA0C;IAC/C,eAAa,EAAEW,IAAA,CAAAC,iBAAiB;IACjC,eAAa,EAAC,oBAAoB;IACjCC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEJ,IAAA,CAAAK,mBAAA,IAAAL,IAAA,CAAAK,mBAAA,IAAAD,IAAA,CAAmB;kCAE3BZ,mBAAA,CAAgE;IAA1DH,KAAK,EAAC;EAAyB,GAAC,qBAAmB,oB,mCAI/DG,mBAAA,CAwCM;IAvCJH,KAAK,EAAC,4CAA4C;IAClDiB,EAAE,EAAC,oBAAoB;IACtBC,MAAM,GAAGP,IAAA,CAAAC;kCAEVT,mBAAA,CAkCM;IAlCDH,KAAK,EAAC;EAAsB,IAC/BG,mBAAA,CAeM;IAfDH,KAAK,EAAC;EAAwC,IACjDG,mBAAA,CAME;IALAH,KAAK,EAAC,uCAAuC;IAC7CmB,GAAG,EAAC,iCAAiC;IACrClB,IAAI,EAAC,KAAK;IACVmB,GAAG,EAAC,EAAE;IACN,aAAW,EAAC;MAEdjB,mBAAA,CAMM;IANDH,KAAK,EAAC;EAAuB,IAChCG,mBAAA,CAII,YAHFA,mBAAA,CAA8C,gBAAtC,+BAA6B,GAASA,mBAAA,CAAM,O,iBAAA,KAClD,GAAAA,mBAAA,CAAwB,gBAAhB,SAAO,G,iBAAS,8EAE5B,E,OAGJA,mBAAA,CAgBM;IAhBDH,KAAK,EAAC;EAAwC,IACjDG,mBAAA,CAME;IALAH,KAAK,EAAC,uCAAuC;IAC7CmB,GAAG,EAAC,+BAA+B;IACnClB,IAAI,EAAC,KAAK;IACVmB,GAAG,EAAC,EAAE;IACN,aAAW,EAAC;MAEdjB,mBAAA,CAOM;IAPDH,KAAK,EAAC;EAAuB,IAChCG,mBAAA,CAKI,YAJFA,mBAAA,CAAkD,gBAA1C,mCAAiC,GAASA,mBAAA,CAAM,O,iBAAA,KACtD,GAAAA,mBAAA,CAAqB,gBAAb,MAAI,G,iBAAS,WAAS,GAAAA,mBAAA,CAAyB,gBAAjB,UAAQ,G,iBAAS,wHAG3D,E,6DAQZD,mBAAA,iBAAoB,EACpBC,mBAAA,CAsJM,OAtJNkB,UAsJM,GArJJlB,mBAAA,CAoJM,OApJNmB,UAoJM,GAnJJpB,mBAAA,uCAA0C,EAC1CC,mBAAA,CAgCM,OAhCNoB,WAgCM,GA/BJpB,mBAAA,CAiBS;IAhBPH,KAAK,EAAC,cAAc;IACnBa,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAU,MAAA,IAAEC,QAAA,CAAAC,gBAAgB;IACvB,YAAU,WAAWD,QAAA,CAAAE,YAAY;IAClCjB,IAAI,EAAC;kCAELP,mBAAA,CAME;IALAgB,GAA+B,EAA/BpB,UAA+B;IAC/BqB,GAAG,EAAC,oBAAoB;IACxBpB,KAAK,EAAC,MAAM;IACZ4B,KAAK,EAAC,IAAI;IACVC,MAAM,EAAC;8BAET1B,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAe,IACxBG,mBAAA,CAAyC;IAArCH,KAAK,EAAC;EAAY,GAAC,eAAa,GACpCG,mBAAA,CAA0D;IAApDH,KAAK,EAAC;EAAe,GAAC,yBAAuB,E,oDAIvDE,mBAAA,wBAA2B,EAC3BC,mBAAA,CAUS;IATPH,KAAK,EAAC,oBAAoB;IACzBa,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEU,QAAA,CAAAK,mBAAA,IAAAL,QAAA,CAAAK,mBAAA,IAAAf,IAAA,CAAmB;IAC1B,YAAU,EAAEgB,MAAA,CAAAC,gBAAgB;IAC5B,eAAa,GAAGD,MAAA,CAAAC,gBAAgB;IACjCtB,IAAI,EAAC;MAELP,mBAAA,CAA2E;IAArEH,KAAK,EAAAiC,eAAA,EAAC,gBAAgB;MAAAC,MAAA,GAAoBH,MAAA,CAAAC;IAAgB;2BAChE7B,mBAAA,CAA2E;IAArEH,KAAK,EAAAiC,eAAA,EAAC,gBAAgB;MAAAC,MAAA,GAAoBH,MAAA,CAAAC;IAAgB;2BAChE7B,mBAAA,CAA2E;IAArEH,KAAK,EAAAiC,eAAA,EAAC,gBAAgB;MAAAC,MAAA,GAAoBH,MAAA,CAAAC;IAAgB;2DAIpE9B,mBAAA,0CAA6C,EAC7CC,mBAAA,CA2BM,OA3BNgC,WA2BM,GA1BJhC,mBAAA,CAyBK,MAzBLiC,WAyBK,GAxBHjC,mBAAA,CAKK,MALLkC,WAKK,GAJHlC,mBAAA,CAGI;IAHDC,IAAI,EAAC,GAAG;IAACJ,KAAK,EAAAiC,eAAA,EAAC,UAAU;MAAAC,MAAA,EAAmBH,MAAA,CAAAO,UAAU;IAAA;IAAqBzB,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAU,MAAA,IAAEC,QAAA,CAAAC,gBAAgB;IAAezB,IAAI,EAAC;kCACvHE,mBAAA,CAA8C;IAA3CH,KAAK,EAAC,aAAa;IAAC,aAAW,EAAC;8BACnCG,mBAAA,CAAsB,cAAhB,WAAS,oB,qBAGnBA,mBAAA,CAKK,MALLoC,WAKK,GAJHpC,mBAAA,CAGI;IAHDC,IAAI,EAAC,GAAG;IAACJ,KAAK,EAAAiC,eAAA,EAAC,UAAU;MAAAC,MAAA,EAAmBH,MAAA,CAAAO,UAAU;IAAA;IAAoBzB,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAU,MAAA,IAAEC,QAAA,CAAAC,gBAAgB;IAAczB,IAAI,EAAC;kCACrHE,mBAAA,CAAkD;IAA/CH,KAAK,EAAC,iBAAiB;IAAC,aAAW,EAAC;8BACvCG,mBAAA,CAAqB,cAAf,UAAQ,oB,qBAGlBA,mBAAA,CAKK,MALLqC,WAKK,GAJHrC,mBAAA,CAGI;IAHDC,IAAI,EAAC,GAAG;IAACJ,KAAK,EAAAiC,eAAA,EAAC,UAAU;MAAAC,MAAA,EAAmBH,MAAA,CAAAO,UAAU;IAAA;IAAoBzB,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAU,MAAA,IAAEC,QAAA,CAAAC,gBAAgB;IAAczB,IAAI,EAAC;kCACrHE,mBAAA,CAA+C;IAA5CH,KAAK,EAAC,cAAc;IAAC,aAAW,EAAC;8BACpCG,mBAAA,CAAwB,cAAlB,aAAW,oB,qBAGrBA,mBAAA,CAKK,MALLsC,WAKK,GAJHtC,mBAAA,CAGI;IAHDC,IAAI,EAAC,GAAG;IAACJ,KAAK,EAAAiC,eAAA,EAAC,UAAU;MAAAC,MAAA,EAAmBH,MAAA,CAAAO,UAAU;IAAA;IAAgBzB,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAU,MAAA,IAAEC,QAAA,CAAAC,gBAAgB;IAAUzB,IAAI,EAAC;kCAC7GE,mBAAA,CAAyD;IAAtDH,KAAK,EAAC,wBAAwB;IAAC,aAAW,EAAC;8BAC9CG,mBAAA,CAAiB,cAAX,MAAI,oB,yBAMlBD,mBAAA,iCAAoC,EACpCC,mBAAA,CAgFM,OAhFNuC,WAgFM,GA/EJxC,mBAAA,YAAe,EACfC,mBAAA,CAmBM,OAnBNwC,WAmBM,GAlBJxC,mBAAA,CAES;IAFDH,KAAK,EAAC,eAAe;IAAEa,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEU,QAAA,CAAAmB,YAAA,IAAAnB,QAAA,CAAAmB,YAAA,IAAA7B,IAAA,CAAY;IAAE,YAAU,EAAC,+BAA+B;IAAE,eAAa,EAAE8B,KAAA,CAAAC;kCAC7G3C,mBAAA,CAAgD;IAA7CH,KAAK,EAAC,eAAe;IAAC,aAAW,EAAC;6DAEvCG,mBAAA,CAcM;IAdDH,KAAK,EAAAiC,eAAA,EAAC,YAAY;MAAAC,MAAA,EAAmBW,KAAA,CAAAC;IAAU;IAAI7C,IAAI,EAAC;kCAC3DE,mBAAA,CAAgF;IAAzE4C,GAAG,EAAC,eAAe;IAAC/C,KAAK,EAAC;KAAU,+BAA6B,sB,gBACxEG,mBAAA,CAQE;IAPAc,EAAE,EAAC,eAAe;IAClBP,IAAI,EAAC,QAAQ;IACbsC,WAAW,EAAC,+BAA+B;IAC3ChD,KAAK,EAAC,cAAc;+DACX6C,KAAA,CAAAI,WAAW,GAAAzB,MAAA;IACnB0B,OAAK,EAAApC,MAAA,QAAAA,MAAA,MAAAqC,SAAA,KAAApC,IAAA,KAAQU,QAAA,CAAA2B,aAAA,IAAA3B,QAAA,CAAA2B,aAAA,IAAArC,IAAA,CAAa;IAC3BsC,YAAY,EAAC;iEAFJR,KAAA,CAAAI,WAAW,E,GAItB9C,mBAAA,CAES;IAFDH,KAAK,EAAC,eAAe;IAAEa,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEU,QAAA,CAAA2B,aAAA,IAAA3B,QAAA,CAAA2B,aAAA,IAAArC,IAAA,CAAa;IAAE,YAAU,EAAC;kCAC9DZ,mBAAA,CAAgD;IAA7CH,KAAK,EAAC,eAAe;IAAC,aAAW,EAAC;oDAK3CE,mBAAA,mBAAsB,EACtBoD,YAAA,CAIEC,8BAAA;IAHCC,iBAAgB,EAAE/B,QAAA,CAAAgC,qBAAqB;IACvCC,mBAAkB,EAAEjC,QAAA,CAAAkC,uBAAuB;IAC3CC,OAAK,EAAEnC,QAAA,CAAAoC;oFAGV3D,mBAAA,kBAAqB,EACrBC,mBAAA,CAgDM;IAhDDH,KAAK,EAAAiC,eAAA,EAAC,cAAc;MAAAC,MAAA,EAAmBH,MAAA,CAAA+B;IAAgB;MAC1D3D,mBAAA,CAUS;IAVDH,KAAK,EAAC,UAAU;IAAEa,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEU,QAAA,CAAAsC,wBAAA,IAAAtC,QAAA,CAAAsC,wBAAA,IAAAhD,IAAA,CAAwB;IAAE,YAAU,EAAC,mBAAmB;IAAE,eAAa,EAAEgB,MAAA,CAAA+B;MACxG3D,mBAAA,CAGM,OAHN6D,WAGM,GAFOjC,MAAA,CAAAkC,UAAU,I,cAArBC,mBAAA,CAAgF;;IAAxD/C,GAAG,EAAEY,MAAA,CAAAkC,UAAU;IAAG7C,GAAG,EAAEW,MAAA,CAAAoC,QAAQ;IAAEnE,KAAK,EAAC;yDAC/DkE,mBAAA,CAAwE,KAAxEE,WAAwE,G,GAE1EjE,mBAAA,CAGM,OAHNkE,WAGM,GAFJlE,mBAAA,CAA6C,QAA7CmE,WAA6C,EAAAC,gBAAA,CAAlBxC,MAAA,CAAAoC,QAAQ,kB,4BACnChE,mBAAA,CAA4C;IAAtCH,KAAK,EAAC;EAAW,GAAC,eAAa,qB,+BAEvCG,mBAAA,CAAqE;IAAlEH,KAAK,EAAC,oCAAoC;IAAC,aAAW,EAAC;6DAGjD+B,MAAA,CAAA+B,gBAAgB,I,cAA3BI,mBAAA,CAkCM,OAlCNM,WAkCM,GAjCJrE,mBAAA,CAKM,OALNsE,WAKM,GAJJtE,mBAAA,CAGM,OAHNuE,WAGM,GAFJvE,mBAAA,CAA+B,gBAAAoE,gBAAA,CAApBxC,MAAA,CAAAoC,QAAQ,kBACnBhE,mBAAA,CAA+C,QAA/CwE,WAA+C,EAAAJ,gBAAA,CAAnBxC,MAAA,CAAA6C,SAAS,iB,iCAGzCzE,mBAAA,CAAoC;IAA/BH,KAAK,EAAC;EAAkB,6BAC7BG,mBAAA,CAGI;IAHDC,IAAI,EAAC,GAAG;IAACJ,KAAK,EAAC,eAAe;IAAEa,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAU,MAAA,IAAEC,QAAA,CAAAC,gBAAgB;IAAazB,IAAI,EAAC;kCAC1EE,mBAAA,CAA8C;IAA3CH,KAAK,EAAC,aAAa;IAAC,aAAW,EAAC;8BACnCG,mBAAA,CAAuB,cAAjB,YAAU,oB,IAElBA,mBAAA,CAGI;IAHDC,IAAI,EAAC,GAAG;IAACJ,KAAK,EAAC,eAAe;IAAEa,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAU,MAAA,IAAEC,QAAA,CAAAC,gBAAgB;IAAczB,IAAI,EAAC;kCAC3EE,mBAAA,CAA6C;IAA1CH,KAAK,EAAC,YAAY;IAAC,aAAW,EAAC;8BAClCG,mBAAA,CAA6B,cAAvB,kBAAgB,oB,IAExBA,mBAAA,CAGI;IAHDC,IAAI,EAAC,GAAG;IAACJ,KAAK,EAAC,eAAe;IAAEa,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAU,MAAA,IAAEC,QAAA,CAAAC,gBAAgB;IAAezB,IAAI,EAAC;kCAC5EE,mBAAA,CAAgD;IAA7CH,KAAK,EAAC,eAAe;IAAC,aAAW,EAAC;8BACrCG,mBAAA,CAAyB,cAAnB,cAAY,oB,IAEpBA,mBAAA,CAGI;IAHDC,IAAI,EAAC,GAAG;IAACJ,KAAK,EAAC,eAAe;IAAEa,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAU,MAAA,IAAEC,QAAA,CAAAC,gBAAgB;IAAazB,IAAI,EAAC;kCAC1EE,mBAAA,CAAiD;IAA9CH,KAAK,EAAC,gBAAgB;IAAC,aAAW,EAAC;8BACtCG,mBAAA,CAA4B,cAAtB,iBAAe,oB,gCAEvBA,mBAAA,CAAoC;IAA/BH,KAAK,EAAC;EAAkB,6BAC7BG,mBAAA,CAGI;IAHDC,IAAI,EAAC,GAAG;IAACJ,KAAK,EAAC,eAAe;IAAEa,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAU,MAAA,IAAEC,QAAA,CAAAC,gBAAgB;IAAUzB,IAAI,EAAC;kCACvEE,mBAAA,CAAyD;IAAtDH,KAAK,EAAC,wBAAwB;IAAC,aAAW,EAAC;8BAC9CG,mBAAA,CAA2B,cAArB,gBAAc,oB,gCAEtBA,mBAAA,CAAoC;IAA/BH,KAAK,EAAC;EAAkB,6BAC7BG,mBAAA,CAGI;IAHDC,IAAI,EAAC,GAAG;IAACJ,KAAK,EAAC,2BAA2B;IAAEa,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEU,QAAA,CAAAoD,YAAA,IAAApD,QAAA,CAAAoD,YAAA,IAAA9D,IAAA,CAAY;IAAEd,IAAI,EAAC;kCACvEE,mBAAA,CAAsD;IAAnDH,KAAK,EAAC,qBAAqB;IAAC,aAAW,EAAC;8BAC3CG,mBAAA,CAAqB,cAAf,UAAQ,oB,kEAQ1BD,mBAAA,2BAA8B,EACQ6B,MAAA,CAAA+C,eAAe,I,cAArDZ,mBAAA,CAgBM,OAhBNa,WAgBM,GAfJ5E,mBAAA,CAcM,OAdN6E,WAcM,GAbJ7E,mBAAA,CAYM,OAZN8E,WAYM,GAXJ9E,mBAAA,CAUK,MAVL+E,WAUK,GATH/E,mBAAA,CAKK,MALLgF,WAKK,GAJHhF,mBAAA,CAGI;IAHDC,IAAI,EAAC,GAAG;IAAES,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAU,MAAA,IAAEC,QAAA,CAAAC,gBAAgB;kCAClCvB,mBAAA,CAA8C;IAA3CH,KAAK,EAAC,aAAa;IAAC,aAAW,EAAC;+CAAW,aAEhD,E,MAEFG,mBAAA,CAEK,MAFLiF,WAEK,EAAAb,gBAAA,CADA9C,QAAA,CAAAE,YAAY,mB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}