{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, normalizeClass as _normalizeClass, vModelText as _vModelText, withKeys as _withKeys, withDirectives as _withDirectives, resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, createStaticVNode as _createStaticVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"client-header\",\n  role: \"banner\"\n};\nconst _hoisted_2 = {\n  class: \"main-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-container\"\n};\nconst _hoisted_4 = {\n  class: \"header-left\"\n};\nconst _hoisted_5 = {\n  class: \"main-navigation\",\n  role: \"navigation\",\n  \"aria-label\": \"Main navigation\"\n};\nconst _hoisted_6 = {\n  class: \"nav-list\"\n};\nconst _hoisted_7 = {\n  class: \"nav-item\"\n};\nconst _hoisted_8 = {\n  class: \"nav-item\"\n};\nconst _hoisted_9 = {\n  class: \"nav-item\"\n};\nconst _hoisted_10 = {\n  class: \"nav-item\"\n};\nconst _hoisted_11 = {\n  class: \"header-actions\"\n};\nconst _hoisted_12 = {\n  class: \"search-container\"\n};\nconst _hoisted_13 = {\n  class: \"user-avatar\"\n};\nconst _hoisted_14 = [\"src\", \"alt\"];\nconst _hoisted_15 = {\n  key: 1,\n  class: \"fas fa-user-circle avatar-icon\"\n};\nconst _hoisted_16 = {\n  class: \"user-info\"\n};\nconst _hoisted_17 = {\n  class: \"user-name\"\n};\nconst _hoisted_18 = {\n  key: 0,\n  class: \"user-dropdown-menu\",\n  role: \"menu\"\n};\nconst _hoisted_19 = {\n  class: \"dropdown-header\"\n};\nconst _hoisted_20 = {\n  class: \"user-details\"\n};\nconst _hoisted_21 = {\n  class: \"user-email\"\n};\nconst _hoisted_22 = {\n  key: 0,\n  class: \"breadcrumb-section\"\n};\nconst _hoisted_23 = {\n  class: \"header-container\"\n};\nconst _hoisted_24 = {\n  class: \"breadcrumb-nav\",\n  \"aria-label\": \"Breadcrumb\"\n};\nconst _hoisted_25 = {\n  class: \"breadcrumb-list\"\n};\nconst _hoisted_26 = {\n  class: \"breadcrumb-item\"\n};\nconst _hoisted_27 = {\n  class: \"breadcrumb-item active\",\n  \"aria-current\": \"page\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_ClientNotifications = _resolveComponent(\"ClientNotifications\");\n  return _openBlock(), _createElementBlock(\"header\", _hoisted_1, [_createCommentVNode(\" Government Banner \"), _cache[37] || (_cache[37] = _createStaticVNode(\"<div class=\\\"gov-banner\\\" data-v-237c3b88><div class=\\\"gov-banner-content\\\" data-v-237c3b88><div class=\\\"gov-banner-flag\\\" data-v-237c3b88><img src=\\\"/assets/images/us_flag_small.png\\\" alt=\\\"U.S. flag\\\" class=\\\"flag-icon\\\" data-v-237c3b88></div><div class=\\\"gov-banner-text\\\" data-v-237c3b88><span class=\\\"gov-banner-label\\\" data-v-237c3b88>An official website of the</span><strong class=\\\"gov-banner-agency\\\" data-v-237c3b88>Barangay Bula General Santos City</strong></div><div class=\\\"gov-banner-secure\\\" data-v-237c3b88><i class=\\\"fas fa-lock\\\" data-v-237c3b88></i><span data-v-237c3b88>Secure</span></div></div></div>\", 1)), _createCommentVNode(\" Main Header \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createCommentVNode(\" Left Section: Logo and Navigation \"), _createElementVNode(\"div\", _hoisted_4, [_cache[18] || (_cache[18] = _createStaticVNode(\"<div class=\\\"logo-section\\\" data-v-237c3b88><img src=\\\"/assets/images/barangay-logo.png\\\" alt=\\\"Barangay Bula Logo\\\" class=\\\"logo\\\" data-v-237c3b88><div class=\\\"site-identity\\\" data-v-237c3b88><h1 class=\\\"site-title\\\" data-v-237c3b88>Barangay Bula</h1><span class=\\\"site-subtitle\\\" data-v-237c3b88>Document Request System</span></div></div>\", 1)), _createCommentVNode(\" Mobile Menu Toggle \"), _createElementVNode(\"button\", {\n    class: \"mobile-menu-toggle\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.handleSidebarToggle && $options.handleSidebarToggle(...args)),\n    \"aria-label\": \"Toggle navigation menu\"\n  }, _cache[17] || (_cache[17] = [_createElementVNode(\"span\", {\n    class: \"hamburger-line\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", {\n    class: \"hamburger-line\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", {\n    class: \"hamburger-line\"\n  }, null, -1 /* HOISTED */)]))]), _createCommentVNode(\" Center Section: Navigation (Desktop) \"), _createElementVNode(\"nav\", _hoisted_5, [_createElementVNode(\"ul\", _hoisted_6, [_createElementVNode(\"li\", _hoisted_7, [_createElementVNode(\"a\", {\n    href: \"#\",\n    class: _normalizeClass([\"nav-link\", {\n      active: $props.activeMenu === 'dashboard'\n    }]),\n    onClick: _cache[1] || (_cache[1] = $event => $options.handleMenuAction('dashboard'))\n  }, _cache[19] || (_cache[19] = [_createElementVNode(\"i\", {\n    class: \"fas fa-home\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Dashboard\", -1 /* HOISTED */)]), 2 /* CLASS */)]), _createElementVNode(\"li\", _hoisted_8, [_createElementVNode(\"a\", {\n    href: \"#\",\n    class: _normalizeClass([\"nav-link\", {\n      active: $props.activeMenu === 'services'\n    }]),\n    onClick: _cache[2] || (_cache[2] = $event => $options.handleMenuAction('services'))\n  }, _cache[20] || (_cache[20] = [_createElementVNode(\"i\", {\n    class: \"fas fa-file-alt\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Services\", -1 /* HOISTED */)]), 2 /* CLASS */)]), _createElementVNode(\"li\", _hoisted_9, [_createElementVNode(\"a\", {\n    href: \"#\",\n    class: _normalizeClass([\"nav-link\", {\n      active: $props.activeMenu === 'requests'\n    }]),\n    onClick: _cache[3] || (_cache[3] = $event => $options.handleMenuAction('requests'))\n  }, _cache[21] || (_cache[21] = [_createElementVNode(\"i\", {\n    class: \"fas fa-clock\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"My Requests\", -1 /* HOISTED */)]), 2 /* CLASS */)]), _createElementVNode(\"li\", _hoisted_10, [_createElementVNode(\"a\", {\n    href: \"#\",\n    class: _normalizeClass([\"nav-link\", {\n      active: $props.activeMenu === 'help'\n    }]),\n    onClick: _cache[4] || (_cache[4] = $event => $options.handleMenuAction('help'))\n  }, _cache[22] || (_cache[22] = [_createElementVNode(\"i\", {\n    class: \"fas fa-question-circle\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Help\", -1 /* HOISTED */)]), 2 /* CLASS */)])])]), _createCommentVNode(\" Right Section: User Actions \"), _createElementVNode(\"div\", _hoisted_11, [_createCommentVNode(\" Search \"), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"button\", {\n    class: \"search-toggle\",\n    onClick: _cache[5] || (_cache[5] = (...args) => _ctx.toggleSearch && _ctx.toggleSearch(...args)),\n    \"aria-label\": \"Search\"\n  }, _cache[23] || (_cache[23] = [_createElementVNode(\"i\", {\n    class: \"fas fa-search\"\n  }, null, -1 /* HOISTED */)])), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"search-box\", {\n      active: $data.showSearch\n    }])\n  }, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"search\",\n    placeholder: \"Search documents, services...\",\n    class: \"search-input\",\n    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.searchQuery = $event),\n    onKeyup: _cache[7] || (_cache[7] = _withKeys((...args) => _ctx.performSearch && _ctx.performSearch(...args), [\"enter\"]))\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelText, $data.searchQuery]]), _createElementVNode(\"button\", {\n    class: \"search-submit\",\n    onClick: _cache[8] || (_cache[8] = (...args) => _ctx.performSearch && _ctx.performSearch(...args)),\n    \"aria-label\": \"Submit search\"\n  }, _cache[24] || (_cache[24] = [_createElementVNode(\"i\", {\n    class: \"fas fa-search\"\n  }, null, -1 /* HOISTED */)]))], 2 /* CLASS */)]), _createCommentVNode(\" Notifications \"), _createVNode(_component_ClientNotifications, {\n    onNewNotification: $options.handleNewNotification,\n    onNotificationClick: $options.handleNotificationClick,\n    onError: $options.handleNotificationError\n  }, null, 8 /* PROPS */, [\"onNewNotification\", \"onNotificationClick\", \"onError\"]), _createCommentVNode(\" User Profile \"), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"user-profile\", {\n      active: $props.showUserDropdown\n    }])\n  }, [_createElementVNode(\"button\", {\n    class: \"user-btn\",\n    onClick: _cache[9] || (_cache[9] = (...args) => $options.handleUserDropdownToggle && $options.handleUserDropdownToggle(...args)),\n    \"aria-label\": \"User menu\"\n  }, [_createElementVNode(\"div\", _hoisted_13, [$props.userAvatar ? (_openBlock(), _createElementBlock(\"img\", {\n    key: 0,\n    src: $props.userAvatar,\n    alt: $props.userName,\n    class: \"avatar-image\"\n  }, null, 8 /* PROPS */, _hoisted_14)) : (_openBlock(), _createElementBlock(\"i\", _hoisted_15))]), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"span\", _hoisted_17, _toDisplayString($props.userName), 1 /* TEXT */), _cache[25] || (_cache[25] = _createElementVNode(\"span\", {\n    class: \"user-role\"\n  }, \"Client Portal\", -1 /* HOISTED */))]), _cache[26] || (_cache[26] = _createElementVNode(\"i\", {\n    class: \"fas fa-chevron-down dropdown-arrow\"\n  }, null, -1 /* HOISTED */))]), $props.showUserDropdown ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"strong\", null, _toDisplayString($props.userName), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_21, _toDisplayString($props.userEmail), 1 /* TEXT */)])]), _cache[33] || (_cache[33] = _createElementVNode(\"div\", {\n    class: \"dropdown-divider\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"dropdown-item\",\n    onClick: _cache[10] || (_cache[10] = $event => $options.handleMenuAction('profile')),\n    role: \"menuitem\"\n  }, _cache[27] || (_cache[27] = [_createElementVNode(\"i\", {\n    class: \"fas fa-user\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"My Profile\", -1 /* HOISTED */)])), _createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"dropdown-item\",\n    onClick: _cache[11] || (_cache[11] = $event => $options.handleMenuAction('settings')),\n    role: \"menuitem\"\n  }, _cache[28] || (_cache[28] = [_createElementVNode(\"i\", {\n    class: \"fas fa-cog\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Account Settings\", -1 /* HOISTED */)])), _createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"dropdown-item\",\n    onClick: _cache[12] || (_cache[12] = $event => $options.handleMenuAction('documents')),\n    role: \"menuitem\"\n  }, _cache[29] || (_cache[29] = [_createElementVNode(\"i\", {\n    class: \"fas fa-folder\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"My Documents\", -1 /* HOISTED */)])), _createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"dropdown-item\",\n    onClick: _cache[13] || (_cache[13] = $event => $options.handleMenuAction('history')),\n    role: \"menuitem\"\n  }, _cache[30] || (_cache[30] = [_createElementVNode(\"i\", {\n    class: \"fas fa-history\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Request History\", -1 /* HOISTED */)])), _cache[34] || (_cache[34] = _createElementVNode(\"div\", {\n    class: \"dropdown-divider\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"dropdown-item\",\n    onClick: _cache[14] || (_cache[14] = $event => $options.handleMenuAction('help')),\n    role: \"menuitem\"\n  }, _cache[31] || (_cache[31] = [_createElementVNode(\"i\", {\n    class: \"fas fa-question-circle\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Help & Support\", -1 /* HOISTED */)])), _cache[35] || (_cache[35] = _createElementVNode(\"div\", {\n    class: \"dropdown-divider\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"dropdown-item logout-item\",\n    onClick: _cache[15] || (_cache[15] = (...args) => $options.handleLogout && $options.handleLogout(...args)),\n    role: \"menuitem\"\n  }, _cache[32] || (_cache[32] = [_createElementVNode(\"i\", {\n    class: \"fas fa-sign-out-alt\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"Sign Out\", -1 /* HOISTED */)]))])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)])])]), _createCommentVNode(\" Breadcrumb Navigation \"), $props.showBreadcrumbs ? (_openBlock(), _createElementBlock(\"div\", _hoisted_22, [_createElementVNode(\"div\", _hoisted_23, [_createElementVNode(\"nav\", _hoisted_24, [_createElementVNode(\"ol\", _hoisted_25, [_createElementVNode(\"li\", _hoisted_26, [_createElementVNode(\"a\", {\n    href: \"#\",\n    onClick: _cache[16] || (_cache[16] = $event => $options.handleMenuAction('dashboard'))\n  }, _cache[36] || (_cache[36] = [_createElementVNode(\"i\", {\n    class: \"fas fa-home\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Dashboard \")]))]), _createElementVNode(\"li\", _hoisted_27, _toDisplayString($options.getPageTitle()), 1 /* TEXT */)])])])])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "role", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "onClick", "_cache", "args", "$options", "handleSidebarToggle", "_hoisted_5", "_hoisted_6", "_hoisted_7", "href", "_normalizeClass", "active", "$props", "activeMenu", "$event", "handleMenuAction", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_ctx", "toggleSearch", "$data", "showSearch", "type", "placeholder", "searchQuery", "onKeyup", "_with<PERSON><PERSON><PERSON>", "performSearch", "_createVNode", "_component_ClientNotifications", "onNewNotification", "handleNewNotification", "onNotificationClick", "handleNotificationClick", "onError", "handleNotificationError", "showUserDropdown", "handleUserDropdownToggle", "_hoisted_13", "userAvatar", "src", "alt", "userName", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_toDisplayString", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "userEmail", "handleLogout", "showBreadcrumbs", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27", "getPageTitle"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\client\\ClientHeader.vue"], "sourcesContent": ["<template>\n  <header class=\"client-header\" role=\"banner\">\n    <!-- Government Banner -->\n    <div class=\"gov-banner\">\n      <div class=\"gov-banner-content\">\n        <div class=\"gov-banner-flag\">\n          <img src=\"/assets/images/us_flag_small.png\" alt=\"U.S. flag\" class=\"flag-icon\" />\n        </div>\n        <div class=\"gov-banner-text\">\n          <span class=\"gov-banner-label\">An official website of the</span>\n          <strong class=\"gov-banner-agency\">Barangay Bula General Santos City</strong>\n        </div>\n        <div class=\"gov-banner-secure\">\n          <i class=\"fas fa-lock\"></i>\n          <span>Secure</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Main Header -->\n    <div class=\"main-header\">\n      <div class=\"header-container\">\n        <!-- Left Section: Logo and Navigation -->\n        <div class=\"header-left\">\n          <div class=\"logo-section\">\n            <img src=\"/assets/images/barangay-logo.png\" alt=\"Barangay Bula Logo\" class=\"logo\" />\n            <div class=\"site-identity\">\n              <h1 class=\"site-title\">Barangay Bula</h1>\n              <span class=\"site-subtitle\">Document Request System</span>\n            </div>\n          </div>\n\n          <!-- Mobile Menu Toggle -->\n          <button class=\"mobile-menu-toggle\" @click=\"handleSidebarToggle\" aria-label=\"Toggle navigation menu\">\n            <span class=\"hamburger-line\"></span>\n            <span class=\"hamburger-line\"></span>\n            <span class=\"hamburger-line\"></span>\n          </button>\n        </div>\n\n        <!-- Center Section: Navigation (Desktop) -->\n        <nav class=\"main-navigation\" role=\"navigation\" aria-label=\"Main navigation\">\n          <ul class=\"nav-list\">\n            <li class=\"nav-item\">\n              <a href=\"#\" class=\"nav-link\" :class=\"{ active: activeMenu === 'dashboard' }\" @click=\"handleMenuAction('dashboard')\">\n                <i class=\"fas fa-home\"></i>\n                <span>Dashboard</span>\n              </a>\n            </li>\n            <li class=\"nav-item\">\n              <a href=\"#\" class=\"nav-link\" :class=\"{ active: activeMenu === 'services' }\" @click=\"handleMenuAction('services')\">\n                <i class=\"fas fa-file-alt\"></i>\n                <span>Services</span>\n              </a>\n            </li>\n            <li class=\"nav-item\">\n              <a href=\"#\" class=\"nav-link\" :class=\"{ active: activeMenu === 'requests' }\" @click=\"handleMenuAction('requests')\">\n                <i class=\"fas fa-clock\"></i>\n                <span>My Requests</span>\n              </a>\n            </li>\n            <li class=\"nav-item\">\n              <a href=\"#\" class=\"nav-link\" :class=\"{ active: activeMenu === 'help' }\" @click=\"handleMenuAction('help')\">\n                <i class=\"fas fa-question-circle\"></i>\n                <span>Help</span>\n              </a>\n            </li>\n          </ul>\n        </nav>\n\n        <!-- Right Section: User Actions -->\n        <div class=\"header-actions\">\n          <!-- Search -->\n          <div class=\"search-container\">\n            <button class=\"search-toggle\" @click=\"toggleSearch\" aria-label=\"Search\">\n              <i class=\"fas fa-search\"></i>\n            </button>\n            <div class=\"search-box\" :class=\"{ active: showSearch }\">\n              <input\n                type=\"search\"\n                placeholder=\"Search documents, services...\"\n                class=\"search-input\"\n                v-model=\"searchQuery\"\n                @keyup.enter=\"performSearch\"\n              />\n              <button class=\"search-submit\" @click=\"performSearch\" aria-label=\"Submit search\">\n                <i class=\"fas fa-search\"></i>\n              </button>\n            </div>\n          </div>\n\n          <!-- Notifications -->\n          <ClientNotifications\n            @new-notification=\"handleNewNotification\"\n            @notification-click=\"handleNotificationClick\"\n            @error=\"handleNotificationError\"\n          />\n\n          <!-- User Profile -->\n          <div class=\"user-profile\" :class=\"{ active: showUserDropdown }\">\n            <button class=\"user-btn\" @click=\"handleUserDropdownToggle\" aria-label=\"User menu\">\n              <div class=\"user-avatar\">\n                <img v-if=\"userAvatar\" :src=\"userAvatar\" :alt=\"userName\" class=\"avatar-image\" />\n                <i v-else class=\"fas fa-user-circle avatar-icon\"></i>\n              </div>\n              <div class=\"user-info\">\n                <span class=\"user-name\">{{ userName }}</span>\n                <span class=\"user-role\">Client Portal</span>\n              </div>\n              <i class=\"fas fa-chevron-down dropdown-arrow\"></i>\n            </button>\n\n            <div v-if=\"showUserDropdown\" class=\"user-dropdown-menu\" role=\"menu\">\n              <div class=\"dropdown-header\">\n                <div class=\"user-details\">\n                  <strong>{{ userName }}</strong>\n                  <span class=\"user-email\">{{ userEmail }}</span>\n                </div>\n              </div>\n              <div class=\"dropdown-divider\"></div>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('profile')\" role=\"menuitem\">\n                <i class=\"fas fa-user\"></i>\n                <span>My Profile</span>\n              </a>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('settings')\" role=\"menuitem\">\n                <i class=\"fas fa-cog\"></i>\n                <span>Account Settings</span>\n              </a>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('documents')\" role=\"menuitem\">\n                <i class=\"fas fa-folder\"></i>\n                <span>My Documents</span>\n              </a>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('history')\" role=\"menuitem\">\n                <i class=\"fas fa-history\"></i>\n                <span>Request History</span>\n              </a>\n              <div class=\"dropdown-divider\"></div>\n              <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('help')\" role=\"menuitem\">\n                <i class=\"fas fa-question-circle\"></i>\n                <span>Help & Support</span>\n              </a>\n              <div class=\"dropdown-divider\"></div>\n              <a href=\"#\" class=\"dropdown-item logout-item\" @click=\"handleLogout\" role=\"menuitem\">\n                <i class=\"fas fa-sign-out-alt\"></i>\n                <span>Sign Out</span>\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Breadcrumb Navigation -->\n    <div class=\"breadcrumb-section\" v-if=\"showBreadcrumbs\">\n      <div class=\"header-container\">\n        <nav class=\"breadcrumb-nav\" aria-label=\"Breadcrumb\">\n          <ol class=\"breadcrumb-list\">\n            <li class=\"breadcrumb-item\">\n              <a href=\"#\" @click=\"handleMenuAction('dashboard')\">\n                <i class=\"fas fa-home\"></i>\n                Dashboard\n              </a>\n            </li>\n            <li class=\"breadcrumb-item active\" aria-current=\"page\">\n              {{ getPageTitle() }}\n            </li>\n          </ol>\n        </nav>\n      </div>\n    </div>\n  </header>\n</template>\n\n<script>\nimport ClientNotifications from './ClientNotifications.vue';\n\nexport default {\n  name: 'ClientHeader',\n  components: {\n    ClientNotifications\n  },\n  props: {\n    userName: {\n      type: String,\n      default: 'User'\n    },\n    userEmail: {\n      type: String,\n      default: '<EMAIL>'\n    },\n    userAvatar: {\n      type: String,\n      default: null\n    },\n    showUserDropdown: {\n      type: Boolean,\n      default: false\n    },\n    sidebarCollapsed: {\n      type: Boolean,\n      default: false\n    },\n    activeMenu: {\n      type: String,\n      default: 'dashboard'\n    },\n    showBreadcrumbs: {\n      type: Boolean,\n      default: true\n    }\n  },\n\n  data() {\n    return {\n      showSearch: false,\n      searchQuery: ''\n    };\n  },\n\n  emits: [\n    'sidebar-toggle',\n    'user-dropdown-toggle',\n    'menu-action',\n    'logout',\n    'error'\n  ],\n\n  mounted() {\n    // Setup event listeners for outside clicks\n    document.addEventListener('click', this.handleOutsideClick);\n  },\n\n  beforeUnmount() {\n    // Clean up event listeners\n    document.removeEventListener('click', this.handleOutsideClick);\n  },\n\n  methods: {\n    // Get page title based on active menu\n    getPageTitle() {\n      const titles = {\n        'dashboard': 'Dashboard',\n        'services': 'Services',\n        'requests': 'My Requests',\n        'documents': 'Documents',\n        'profile': 'Profile',\n        'notifications': 'Notifications',\n        'help': 'Help & Support'\n      };\n      return titles[this.activeMenu] || 'Dashboard';\n    },\n\n    // Handle sidebar toggle\n    handleSidebarToggle() {\n      this.$emit('sidebar-toggle');\n    },\n\n    // Handle user dropdown toggle\n    handleUserDropdownToggle() {\n      this.$emit('user-dropdown-toggle');\n    },\n\n    // Handle menu actions (profile, settings, etc.)\n    handleMenuAction(action) {\n      this.$emit('menu-action', action);\n    },\n\n    // Handle logout\n    handleLogout() {\n      this.$emit('logout');\n    },\n\n    // Handle outside clicks to close dropdowns\n    handleOutsideClick(event) {\n      // Check if click is outside user dropdown\n      if (!event.target.closest('.user-dropdown')) {\n        if (this.showUserDropdown) {\n          this.$emit('user-dropdown-toggle');\n        }\n      }\n    },\n\n    // Notification event handlers\n    handleNewNotification(notification) {\n      console.log('New notification received:', notification);\n      // Handle new notification - could show toast, update UI, etc.\n    },\n\n    async handleNotificationClick(notification) {\n      console.log('🔔 ClientHeader: Notification clicked:', notification);\n\n      // Ensure we have a valid notification object\n      if (!notification || typeof notification !== 'object') {\n        console.error('Invalid notification object received:', notification);\n        return;\n      }\n\n      try {\n        // The ClientNotifications component now handles navigation internally,\n        // but we can add additional logic here if needed\n\n        // Parse notification data\n        const notificationData = typeof notification.data === 'string'\n          ? JSON.parse(notification.data)\n          : notification.data || {};\n\n        // Log navigation for debugging\n        console.log('📊 ClientHeader: Notification data:', notificationData);\n\n        // Additional header-specific logic can go here\n        // For example, updating header state, showing badges, etc.\n\n        // The navigation is now handled by the ClientNotifications component\n        // This handler can focus on header-specific updates\n\n      } catch (error) {\n        console.error('❌ ClientHeader: Error handling notification click:', error);\n      }\n\n      // Always emit the event for other components that might need it\n      this.$emit('notification-click', notification);\n    },\n\n    handleNotificationError(error) {\n      console.error('Notification error:', error);\n      this.$emit('error', error);\n    }\n  }\n};\n</script>\n\n<style scoped src=\"./css/clientHeader.css\"></style>\n"], "mappings": ";;EACUA,KAAK,EAAC,eAAe;EAACC,IAAI,EAAC;;;EAmB5BD,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAkB;;EAEtBA,KAAK,EAAC;AAAa;;EAkBnBA,KAAK,EAAC,iBAAiB;EAACC,IAAI,EAAC,YAAY;EAAC,YAAU,EAAC;;;EACpDD,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAU;;EAMhBA,KAAK,EAAC;AAAU;;EAMhBA,KAAK,EAAC;AAAU;;EAMhBA,KAAK,EAAC;AAAU;;EAUnBA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAkB;;EA4BpBA,KAAK,EAAC;AAAa;;;;EAEZA,KAAK,EAAC;;;EAEbA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAW;;;EAMEA,KAAK,EAAC,oBAAoB;EAACC,IAAI,EAAC;;;EACtDD,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAc;;EAEjBA,KAAK,EAAC;AAAY;;;EAqCjCA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC,gBAAgB;EAAC,YAAU,EAAC;;;EACjCA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAiB;;EAMvBA,KAAK,EAAC,wBAAwB;EAAC,cAAY,EAAC;;;;uBAlK1DE,mBAAA,CAyKS,UAzKTC,UAyKS,GAxKPC,mBAAA,uBAA0B,E,qqBAiB1BA,mBAAA,iBAAoB,EACpBC,mBAAA,CAkIM,OAlINC,UAkIM,GAjIJD,mBAAA,CAgIM,OAhINE,UAgIM,GA/HJH,mBAAA,uCAA0C,EAC1CC,mBAAA,CAeM,OAfNG,UAeM,G,4YANJJ,mBAAA,wBAA2B,EAC3BC,mBAAA,CAIS;IAJDL,KAAK,EAAC,oBAAoB;IAAES,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,mBAAA,IAAAD,QAAA,CAAAC,mBAAA,IAAAF,IAAA,CAAmB;IAAE,YAAU,EAAC;kCACzEN,mBAAA,CAAoC;IAA9BL,KAAK,EAAC;EAAgB,4BAC5BK,mBAAA,CAAoC;IAA9BL,KAAK,EAAC;EAAgB,4BAC5BK,mBAAA,CAAoC;IAA9BL,KAAK,EAAC;EAAgB,2B,MAIhCI,mBAAA,0CAA6C,EAC7CC,mBAAA,CA2BM,OA3BNS,UA2BM,GA1BJT,mBAAA,CAyBK,MAzBLU,UAyBK,GAxBHV,mBAAA,CAKK,MALLW,UAKK,GAJHX,mBAAA,CAGI;IAHDY,IAAI,EAAC,GAAG;IAACjB,KAAK,EAAAkB,eAAA,EAAC,UAAU;MAAAC,MAAA,EAAmBC,MAAA,CAAAC,UAAU;IAAA;IAAqBZ,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAY,MAAA,IAAEV,QAAA,CAAAW,gBAAgB;kCACnGlB,mBAAA,CAA2B;IAAxBL,KAAK,EAAC;EAAa,4BACtBK,mBAAA,CAAsB,cAAhB,WAAS,oB,qBAGnBA,mBAAA,CAKK,MALLmB,UAKK,GAJHnB,mBAAA,CAGI;IAHDY,IAAI,EAAC,GAAG;IAACjB,KAAK,EAAAkB,eAAA,EAAC,UAAU;MAAAC,MAAA,EAAmBC,MAAA,CAAAC,UAAU;IAAA;IAAoBZ,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAY,MAAA,IAAEV,QAAA,CAAAW,gBAAgB;kCAClGlB,mBAAA,CAA+B;IAA5BL,KAAK,EAAC;EAAiB,4BAC1BK,mBAAA,CAAqB,cAAf,UAAQ,oB,qBAGlBA,mBAAA,CAKK,MALLoB,UAKK,GAJHpB,mBAAA,CAGI;IAHDY,IAAI,EAAC,GAAG;IAACjB,KAAK,EAAAkB,eAAA,EAAC,UAAU;MAAAC,MAAA,EAAmBC,MAAA,CAAAC,UAAU;IAAA;IAAoBZ,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAY,MAAA,IAAEV,QAAA,CAAAW,gBAAgB;kCAClGlB,mBAAA,CAA4B;IAAzBL,KAAK,EAAC;EAAc,4BACvBK,mBAAA,CAAwB,cAAlB,aAAW,oB,qBAGrBA,mBAAA,CAKK,MALLqB,WAKK,GAJHrB,mBAAA,CAGI;IAHDY,IAAI,EAAC,GAAG;IAACjB,KAAK,EAAAkB,eAAA,EAAC,UAAU;MAAAC,MAAA,EAAmBC,MAAA,CAAAC,UAAU;IAAA;IAAgBZ,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAY,MAAA,IAAEV,QAAA,CAAAW,gBAAgB;kCAC9FlB,mBAAA,CAAsC;IAAnCL,KAAK,EAAC;EAAwB,4BACjCK,mBAAA,CAAiB,cAAX,MAAI,oB,yBAMlBD,mBAAA,iCAAoC,EACpCC,mBAAA,CA6EM,OA7ENsB,WA6EM,GA5EJvB,mBAAA,YAAe,EACfC,mBAAA,CAgBM,OAhBNuB,WAgBM,GAfJvB,mBAAA,CAES;IAFDL,KAAK,EAAC,eAAe;IAAES,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEkB,IAAA,CAAAC,YAAA,IAAAD,IAAA,CAAAC,YAAA,IAAAnB,IAAA,CAAY;IAAE,YAAU,EAAC;kCAC7DN,mBAAA,CAA6B;IAA1BL,KAAK,EAAC;EAAe,2B,IAE1BK,mBAAA,CAWM;IAXDL,KAAK,EAAAkB,eAAA,EAAC,YAAY;MAAAC,MAAA,EAAmBY,KAAA,CAAAC;IAAU;sBAClD3B,mBAAA,CAME;IALA4B,IAAI,EAAC,QAAQ;IACbC,WAAW,EAAC,+BAA+B;IAC3ClC,KAAK,EAAC,cAAc;+DACX+B,KAAA,CAAAI,WAAW,GAAAb,MAAA;IACnBc,OAAK,EAAA1B,MAAA,QAAAA,MAAA,MAAA2B,SAAA,KAAA1B,IAAA,KAAQkB,IAAA,CAAAS,aAAA,IAAAT,IAAA,CAAAS,aAAA,IAAA3B,IAAA,CAAa;iEADlBoB,KAAA,CAAAI,WAAW,E,GAGtB9B,mBAAA,CAES;IAFDL,KAAK,EAAC,eAAe;IAAES,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEkB,IAAA,CAAAS,aAAA,IAAAT,IAAA,CAAAS,aAAA,IAAA3B,IAAA,CAAa;IAAE,YAAU,EAAC;kCAC9DN,mBAAA,CAA6B;IAA1BL,KAAK,EAAC;EAAe,2B,uBAK9BI,mBAAA,mBAAsB,EACtBmC,YAAA,CAIEC,8BAAA;IAHCC,iBAAgB,EAAE7B,QAAA,CAAA8B,qBAAqB;IACvCC,mBAAkB,EAAE/B,QAAA,CAAAgC,uBAAuB;IAC3CC,OAAK,EAAEjC,QAAA,CAAAkC;oFAGV1C,mBAAA,kBAAqB,EACrBC,mBAAA,CAgDM;IAhDDL,KAAK,EAAAkB,eAAA,EAAC,cAAc;MAAAC,MAAA,EAAmBC,MAAA,CAAA2B;IAAgB;MAC1D1C,mBAAA,CAUS;IAVDL,KAAK,EAAC,UAAU;IAAES,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAoC,wBAAA,IAAApC,QAAA,CAAAoC,wBAAA,IAAArC,IAAA,CAAwB;IAAE,YAAU,EAAC;MACpEN,mBAAA,CAGM,OAHN4C,WAGM,GAFO7B,MAAA,CAAA8B,UAAU,I,cAArBhD,mBAAA,CAAgF;;IAAxDiD,GAAG,EAAE/B,MAAA,CAAA8B,UAAU;IAAGE,GAAG,EAAEhC,MAAA,CAAAiC,QAAQ;IAAErD,KAAK,EAAC;yDAC/DE,mBAAA,CAAqD,KAArDoD,WAAqD,G,GAEvDjD,mBAAA,CAGM,OAHNkD,WAGM,GAFJlD,mBAAA,CAA6C,QAA7CmD,WAA6C,EAAAC,gBAAA,CAAlBrC,MAAA,CAAAiC,QAAQ,kB,4BACnChD,mBAAA,CAA4C;IAAtCL,KAAK,EAAC;EAAW,GAAC,eAAa,qB,+BAEvCK,mBAAA,CAAkD;IAA/CL,KAAK,EAAC;EAAoC,4B,GAGpCoB,MAAA,CAAA2B,gBAAgB,I,cAA3B7C,mBAAA,CAkCM,OAlCNwD,WAkCM,GAjCJrD,mBAAA,CAKM,OALNsD,WAKM,GAJJtD,mBAAA,CAGM,OAHNuD,WAGM,GAFJvD,mBAAA,CAA+B,gBAAAoD,gBAAA,CAApBrC,MAAA,CAAAiC,QAAQ,kBACnBhD,mBAAA,CAA+C,QAA/CwD,WAA+C,EAAAJ,gBAAA,CAAnBrC,MAAA,CAAA0C,SAAS,iB,iCAGzCzD,mBAAA,CAAoC;IAA/BL,KAAK,EAAC;EAAkB,6BAC7BK,mBAAA,CAGI;IAHDY,IAAI,EAAC,GAAG;IAACjB,KAAK,EAAC,eAAe;IAAES,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAY,MAAA,IAAEV,QAAA,CAAAW,gBAAgB;IAAatB,IAAI,EAAC;kCAC1EI,mBAAA,CAA2B;IAAxBL,KAAK,EAAC;EAAa,4BACtBK,mBAAA,CAAuB,cAAjB,YAAU,oB,IAElBA,mBAAA,CAGI;IAHDY,IAAI,EAAC,GAAG;IAACjB,KAAK,EAAC,eAAe;IAAES,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAY,MAAA,IAAEV,QAAA,CAAAW,gBAAgB;IAActB,IAAI,EAAC;kCAC3EI,mBAAA,CAA0B;IAAvBL,KAAK,EAAC;EAAY,4BACrBK,mBAAA,CAA6B,cAAvB,kBAAgB,oB,IAExBA,mBAAA,CAGI;IAHDY,IAAI,EAAC,GAAG;IAACjB,KAAK,EAAC,eAAe;IAAES,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAY,MAAA,IAAEV,QAAA,CAAAW,gBAAgB;IAAetB,IAAI,EAAC;kCAC5EI,mBAAA,CAA6B;IAA1BL,KAAK,EAAC;EAAe,4BACxBK,mBAAA,CAAyB,cAAnB,cAAY,oB,IAEpBA,mBAAA,CAGI;IAHDY,IAAI,EAAC,GAAG;IAACjB,KAAK,EAAC,eAAe;IAAES,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAY,MAAA,IAAEV,QAAA,CAAAW,gBAAgB;IAAatB,IAAI,EAAC;kCAC1EI,mBAAA,CAA8B;IAA3BL,KAAK,EAAC;EAAgB,4BACzBK,mBAAA,CAA4B,cAAtB,iBAAe,oB,gCAEvBA,mBAAA,CAAoC;IAA/BL,KAAK,EAAC;EAAkB,6BAC7BK,mBAAA,CAGI;IAHDY,IAAI,EAAC,GAAG;IAACjB,KAAK,EAAC,eAAe;IAAES,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAY,MAAA,IAAEV,QAAA,CAAAW,gBAAgB;IAAUtB,IAAI,EAAC;kCACvEI,mBAAA,CAAsC;IAAnCL,KAAK,EAAC;EAAwB,4BACjCK,mBAAA,CAA2B,cAArB,gBAAc,oB,gCAEtBA,mBAAA,CAAoC;IAA/BL,KAAK,EAAC;EAAkB,6BAC7BK,mBAAA,CAGI;IAHDY,IAAI,EAAC,GAAG;IAACjB,KAAK,EAAC,2BAA2B;IAAES,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEC,QAAA,CAAAmD,YAAA,IAAAnD,QAAA,CAAAmD,YAAA,IAAApD,IAAA,CAAY;IAAEV,IAAI,EAAC;kCACvEI,mBAAA,CAAmC;IAAhCL,KAAK,EAAC;EAAqB,4BAC9BK,mBAAA,CAAqB,cAAf,UAAQ,oB,kEAQ1BD,mBAAA,2BAA8B,EACQgB,MAAA,CAAA4C,eAAe,I,cAArD9D,mBAAA,CAgBM,OAhBN+D,WAgBM,GAfJ5D,mBAAA,CAcM,OAdN6D,WAcM,GAbJ7D,mBAAA,CAYM,OAZN8D,WAYM,GAXJ9D,mBAAA,CAUK,MAVL+D,WAUK,GATH/D,mBAAA,CAKK,MALLgE,WAKK,GAJHhE,mBAAA,CAGI;IAHDY,IAAI,EAAC,GAAG;IAAER,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAY,MAAA,IAAEV,QAAA,CAAAW,gBAAgB;kCAClClB,mBAAA,CAA2B;IAAxBL,KAAK,EAAC;EAAa,4B,iBAAK,aAE7B,E,MAEFK,mBAAA,CAEK,MAFLiE,WAEK,EAAAb,gBAAA,CADA7C,QAAA,CAAA2D,YAAY,mB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}