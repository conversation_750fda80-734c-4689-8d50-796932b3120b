{"ast": null, "code": "import { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, Fragment as _Fragment, renderList as _renderList, normalizeClass as _normalizeClass, createStaticVNode as _createStaticVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"services-section\",\n  \"aria-labelledby\": \"services-title\",\n  ref: \"servicesSection\"\n};\nconst _hoisted_2 = {\n  class: \"container\"\n};\nconst _hoisted_3 = {\n  key: 0,\n  class: \"loading-container\",\n  role: \"status\",\n  \"aria-live\": \"polite\"\n};\nconst _hoisted_4 = {\n  class: \"error-container\",\n  role: \"alert\"\n};\nconst _hoisted_5 = {\n  class: \"error-content\"\n};\nconst _hoisted_6 = {\n  class: \"document-types-grid\"\n};\nconst _hoisted_7 = [\"onClick\", \"disabled\", \"aria-describedby\"];\nconst _hoisted_8 = {\n  class: \"document-header\"\n};\nconst _hoisted_9 = {\n  class: \"document-icon\"\n};\nconst _hoisted_10 = {\n  \"aria-hidden\": \"true\",\n  viewBox: \"0 0 24 24\",\n  fill: \"currentColor\"\n};\nconst _hoisted_11 = [\"d\"];\nconst _hoisted_12 = {\n  class: \"document-status\"\n};\nconst _hoisted_13 = {\n  key: 0,\n  class: \"status-badge unavailable\",\n  \"aria-label\": \"Service unavailable\"\n};\nconst _hoisted_14 = {\n  key: 1,\n  class: \"status-badge available\",\n  \"aria-label\": \"Service available\"\n};\nconst _hoisted_15 = {\n  class: \"document-content\"\n};\nconst _hoisted_16 = {\n  class: \"document-title\"\n};\nconst _hoisted_17 = [\"id\"];\nconst _hoisted_18 = {\n  class: \"document-details\"\n};\nconst _hoisted_19 = {\n  class: \"detail-item\"\n};\nconst _hoisted_20 = {\n  class: \"detail-value fee-amount\"\n};\nconst _hoisted_21 = {\n  class: \"detail-item\"\n};\nconst _hoisted_22 = {\n  class: \"detail-value\"\n};\nconst _hoisted_23 = {\n  key: 0,\n  class: \"document-action\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"section\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[10] || (_cache[10] = _createElementVNode(\"div\", {\n    class: \"section-header\"\n  }, [_createElementVNode(\"h2\", {\n    id: \"services-title\",\n    class: \"section-title\"\n  }, \"Available Document Services\"), _createElementVNode(\"p\", {\n    class: \"section-description\"\n  }, \"Select the type of document you need to request\")], -1 /* HOISTED */)), _createCommentVNode(\" Loading State \"), $props.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, _cache[1] || (_cache[1] = [_createStaticVNode(\"<div class=\\\"loading-spinner\\\" data-v-47f4d56f><svg class=\\\"spinner\\\" aria-hidden=\\\"true\\\" viewBox=\\\"0 0 24 24\\\" data-v-47f4d56f><circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\" stroke=\\\"currentColor\\\" stroke-width=\\\"2\\\" fill=\\\"none\\\" stroke-dasharray=\\\"31.416\\\" stroke-dashoffset=\\\"31.416\\\" data-v-47f4d56f><animate attributeName=\\\"stroke-dasharray\\\" dur=\\\"2s\\\" values=\\\"0 31.416;15.708 15.708;0 31.416\\\" repeatCount=\\\"indefinite\\\" data-v-47f4d56f></animate><animate attributeName=\\\"stroke-dashoffset\\\" dur=\\\"2s\\\" values=\\\"0;-15.708;-31.416\\\" repeatCount=\\\"indefinite\\\" data-v-47f4d56f></animate></circle></svg></div><p data-v-47f4d56f>Loading available services...</p>\", 2)]))) : $props.error ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" Error State \"), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_cache[3] || (_cache[3] = _createElementVNode(\"svg\", {\n    class: \"error-icon\",\n    \"aria-hidden\": \"true\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z\"\n  })], -1 /* HOISTED */)), _cache[4] || (_cache[4] = _createElementVNode(\"h3\", null, \"Unable to Load Services\", -1 /* HOISTED */)), _createElementVNode(\"p\", null, _toDisplayString($props.error), 1 /* TEXT */), _createElementVNode(\"button\", {\n    class: \"btn btn-primary retry-btn\",\n    onClick: _cache[0] || (_cache[0] = $event => _ctx.$emit('retry')),\n    type: \"button\"\n  }, _cache[2] || (_cache[2] = [_createElementVNode(\"svg\", {\n    class: \"btn-icon\",\n    \"aria-hidden\": \"true\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z\"\n  })], -1 /* HOISTED */), _createTextVNode(\" Try Again \")]))])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 2\n  }, [_createCommentVNode(\" Document Types Grid \"), _createElementVNode(\"div\", _hoisted_6, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($props.documentTypes, documentType => {\n    return _openBlock(), _createElementBlock(\"button\", {\n      key: documentType.id,\n      class: _normalizeClass([\"document-card\", {\n        'disabled': !documentType.is_active\n      }]),\n      onClick: $event => $options.selectDocumentType(documentType),\n      disabled: !documentType.is_active,\n      \"aria-describedby\": `doc-${documentType.id}-desc`,\n      type: \"button\"\n    }, [_createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [(_openBlock(), _createElementBlock(\"svg\", _hoisted_10, [_createElementVNode(\"path\", {\n      d: $options.getDocumentIconPath(documentType.type_name)\n    }, null, 8 /* PROPS */, _hoisted_11)]))]), _createElementVNode(\"div\", _hoisted_12, [!documentType.is_active ? (_openBlock(), _createElementBlock(\"span\", _hoisted_13, \" Unavailable \")) : (_openBlock(), _createElementBlock(\"span\", _hoisted_14, \" Available \"))])]), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"h3\", _hoisted_16, _toDisplayString(documentType.type_name), 1 /* TEXT */), _createElementVNode(\"p\", {\n      id: `doc-${documentType.id}-desc`,\n      class: \"document-description\"\n    }, _toDisplayString(documentType.description), 9 /* TEXT, PROPS */, _hoisted_17), _createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_cache[5] || (_cache[5] = _createElementVNode(\"svg\", {\n      class: \"detail-icon\",\n      \"aria-hidden\": \"true\",\n      viewBox: \"0 0 24 24\",\n      fill: \"currentColor\"\n    }, [_createElementVNode(\"path\", {\n      d: \"M7,15H9C9,16.08 10.37,17 12,17C13.63,17 15,16.08 15,15C15,13.9 13.96,13.5 11.76,12.97C9.64,12.44 7,11.78 7,9C7,7.21 8.47,5.69 10.5,5.18V3H13.5V5.18C15.53,5.69 17,7.21 17,9H15C15,7.92 13.63,7 12,7C10.37,7 9,7.92 9,9C9,10.1 10.04,10.5 12.24,11.03C14.36,11.56 17,12.22 17,15C17,16.79 15.53,18.31 13.5,18.82V21H10.5V18.82C8.47,18.31 7,16.79 7,15Z\"\n    })], -1 /* HOISTED */)), _cache[6] || (_cache[6] = _createElementVNode(\"span\", {\n      class: \"detail-label\"\n    }, \"Fee:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_20, \"₱\" + _toDisplayString($options.formatCurrency(documentType.base_fee)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_21, [_cache[7] || (_cache[7] = _createElementVNode(\"svg\", {\n      class: \"detail-icon\",\n      \"aria-hidden\": \"true\",\n      viewBox: \"0 0 24 24\",\n      fill: \"currentColor\"\n    }, [_createElementVNode(\"path\", {\n      d: \"M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z\"\n    })], -1 /* HOISTED */)), _cache[8] || (_cache[8] = _createElementVNode(\"span\", {\n      class: \"detail-label\"\n    }, \"Processing:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_22, _toDisplayString($options.getProcessingTime(documentType.type_name)), 1 /* TEXT */)])])]), documentType.is_active ? (_openBlock(), _createElementBlock(\"div\", _hoisted_23, [...(_cache[9] || (_cache[9] = [_createElementVNode(\"svg\", {\n      \"aria-hidden\": \"true\",\n      viewBox: \"0 0 24 24\",\n      fill: \"currentColor\"\n    }, [_createElementVNode(\"path\", {\n      d: \"M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z\"\n    })], -1 /* HOISTED */)]))])) : _createCommentVNode(\"v-if\", true)], 10 /* CLASS, PROPS */, _hoisted_7);\n  }), 128 /* KEYED_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))])], 512 /* NEED_PATCH */);\n}", "map": {"version": 3, "names": ["class", "ref", "role", "viewBox", "fill", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "id", "_createCommentVNode", "$props", "loading", "_hoisted_3", "_cache", "error", "_Fragment", "key", "_hoisted_4", "_hoisted_5", "d", "_toDisplayString", "onClick", "$event", "_ctx", "$emit", "type", "_hoisted_6", "_renderList", "documentTypes", "documentType", "_normalizeClass", "is_active", "$options", "selectDocumentType", "disabled", "_hoisted_8", "_hoisted_9", "_hoisted_10", "getDocumentIconPath", "type_name", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "description", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "formatCurrency", "base_fee", "_hoisted_21", "_hoisted_22", "getProcessingTime", "_hoisted_23"], "sources": ["D:\\cap2_rhai_front_and_back\\BOSFDR\\src\\components\\client\\sections\\DocumentServicesSection.vue"], "sourcesContent": ["<template>\n  <section class=\"services-section\" aria-labelledby=\"services-title\" ref=\"servicesSection\">\n    <div class=\"container\">\n      <div class=\"section-header\">\n        <h2 id=\"services-title\" class=\"section-title\">Available Document Services</h2>\n        <p class=\"section-description\">Select the type of document you need to request</p>\n      </div>\n\n      <!-- Loading State -->\n      <div v-if=\"loading\" class=\"loading-container\" role=\"status\" aria-live=\"polite\">\n        <div class=\"loading-spinner\">\n          <svg class=\"spinner\" aria-hidden=\"true\" viewBox=\"0 0 24 24\">\n            <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"2\" fill=\"none\" stroke-dasharray=\"31.416\" stroke-dashoffset=\"31.416\">\n              <animate attributeName=\"stroke-dasharray\" dur=\"2s\" values=\"0 31.416;15.708 15.708;0 31.416\" repeatCount=\"indefinite\"/>\n              <animate attributeName=\"stroke-dashoffset\" dur=\"2s\" values=\"0;-15.708;-31.416\" repeatCount=\"indefinite\"/>\n            </circle>\n          </svg>\n        </div>\n        <p>Loading available services...</p>\n      </div>\n\n      <!-- Error State -->\n      <div v-else-if=\"error\" class=\"error-container\" role=\"alert\">\n        <div class=\"error-content\">\n          <svg class=\"error-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n            <path d=\"M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z\"/>\n          </svg>\n          <h3>Unable to Load Services</h3>\n          <p>{{ error }}</p>\n          <button class=\"btn btn-primary retry-btn\" @click=\"$emit('retry')\" type=\"button\">\n            <svg class=\"btn-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z\"/>\n            </svg>\n            Try Again\n          </button>\n        </div>\n      </div>\n\n      <!-- Document Types Grid -->\n      <div v-else class=\"document-types-grid\">\n        <button\n          v-for=\"documentType in documentTypes\"\n          :key=\"documentType.id\"\n          class=\"document-card\"\n          @click=\"selectDocumentType(documentType)\"\n          :class=\"{ 'disabled': !documentType.is_active }\"\n          :disabled=\"!documentType.is_active\"\n          :aria-describedby=\"`doc-${documentType.id}-desc`\"\n          type=\"button\"\n        >\n          <div class=\"document-header\">\n            <div class=\"document-icon\">\n              <svg aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path :d=\"getDocumentIconPath(documentType.type_name)\"/>\n              </svg>\n            </div>\n            <div class=\"document-status\">\n              <span \n                v-if=\"!documentType.is_active\" \n                class=\"status-badge unavailable\"\n                aria-label=\"Service unavailable\"\n              >\n                Unavailable\n              </span>\n              <span \n                v-else \n                class=\"status-badge available\"\n                aria-label=\"Service available\"\n              >\n                Available\n              </span>\n            </div>\n          </div>\n\n          <div class=\"document-content\">\n            <h3 class=\"document-title\">{{ documentType.type_name }}</h3>\n            <p :id=\"`doc-${documentType.id}-desc`\" class=\"document-description\">\n              {{ documentType.description }}\n            </p>\n\n            <div class=\"document-details\">\n              <div class=\"detail-item\">\n                <svg class=\"detail-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M7,15H9C9,16.08 10.37,17 12,17C13.63,17 15,16.08 15,15C15,13.9 13.96,13.5 11.76,12.97C9.64,12.44 7,11.78 7,9C7,7.21 8.47,5.69 10.5,5.18V3H13.5V5.18C15.53,5.69 17,7.21 17,9H15C15,7.92 13.63,7 12,7C10.37,7 9,7.92 9,9C9,10.1 10.04,10.5 12.24,11.03C14.36,11.56 17,12.22 17,15C17,16.79 15.53,18.31 13.5,18.82V21H10.5V18.82C8.47,18.31 7,16.79 7,15Z\"/>\n                </svg>\n                <span class=\"detail-label\">Fee:</span>\n                <span class=\"detail-value fee-amount\">₱{{ formatCurrency(documentType.base_fee) }}</span>\n              </div>\n\n              <div class=\"detail-item\">\n                <svg class=\"detail-icon\" aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z\"/>\n                </svg>\n                <span class=\"detail-label\">Processing:</span>\n                <span class=\"detail-value\">{{ getProcessingTime(documentType.type_name) }}</span>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"document-action\" v-if=\"documentType.is_active\">\n            <svg aria-hidden=\"true\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z\"/>\n            </svg>\n          </div>\n        </button>\n      </div>\n    </div>\n  </section>\n</template>\n\n<script>\nexport default {\n  name: 'DocumentServicesSection',\n  props: {\n    documentTypes: {\n      type: Array,\n      default: () => []\n    },\n    loading: {\n      type: Boolean,\n      default: false\n    },\n    error: {\n      type: String,\n      default: null\n    }\n  },\n  emits: [\n    'select-document-type',\n    'retry'\n  ],\n  methods: {\n    selectDocumentType(documentType) {\n      if (documentType.is_active) {\n        this.$emit('select-document-type', documentType);\n      }\n    },\n    \n    formatCurrency(amount) {\n      return new Intl.NumberFormat('en-PH', {\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n      }).format(amount || 0);\n    },\n    \n    getProcessingTime(typeName) {\n      const processingTimes = {\n        'Barangay Clearance': '1-2 business days',\n        'Certificate of Residency': '1-2 business days',\n        'Certificate of Indigency': '2-3 business days',\n        'Business Permit': '3-5 business days',\n        'Barangay ID': '5-7 business days',\n        'Certificate of Good Moral': '1-2 business days'\n      };\n      return processingTimes[typeName] || '3-5 business days';\n    },\n    \n    getDocumentIconPath(typeName) {\n      const iconPaths = {\n        'Barangay Clearance': 'M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z',\n        'Certificate of Residency': 'M12,3L2,12H5V20H19V12H22L12,3M12,8.75A2.25,2.25 0 0,1 14.25,11A2.25,2.25 0 0,1 12,13.25A2.25,2.25 0 0,1 9.75,11A2.25,2.25 0 0,1 12,8.75Z',\n        'Certificate of Indigency': 'M17,18C15.89,18 15,18.89 15,20A3,3 0 0,0 18,23A3,3 0 0,0 21,20C21,18.89 20.1,18 19,18H17M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C7.59,22 4,18.41 4,14C4,9.59 7.59,6 12,6Z',\n        'Business Permit': 'M20,6C20.58,6 21.05,6.2 21.42,6.59C21.8,7 22,7.45 22,8V19C22,19.55 21.8,20 21.42,20.41C21.05,20.8 20.58,21 20,21H4C3.42,21 2.95,20.8 2.58,20.41C2.2,20 2,19.55 2,19V8C2,7.45 2.2,7 2.58,6.59C2.95,6.2 3.42,6 4,6H8V4C8,3.42 8.2,2.95 8.58,2.58C8.95,2.2 9.42,2 10,2H14C14.58,2 15.05,2.2 15.42,2.58C15.8,2.95 16,3.42 16,4V6H20M4,8V19H20V8H4M10,4V6H14V4H10Z',\n        'Barangay ID': 'M2,3H22C23.05,3 24,3.95 24,5V19C24,20.05 23.05,21 22,21H2C0.95,21 0,20.05 0,19V5C0,3.95 0.95,3 2,3M14,6V7H22V6H14M14,8V9H21.5L22,9V8H14M14,10V11H21V10H14M8,13.91C6,13.91 2,15 2,17V18H14V17C14,15 10,13.91 8,13.91M8,6A3,3 0 0,0 5,9A3,3 0 0,0 8,12A3,3 0 0,0 11,9A3,3 0 0,0 8,6Z',\n        'Certificate of Good Moral': 'M23,12L20.56,9.22L20.9,5.54L17.29,4.72L15.4,1.54L12,3L8.6,1.54L6.71,4.72L3.1,5.53L3.44,9.21L1,12L3.44,14.78L3.1,18.47L6.71,19.29L8.6,22.47L12,21L15.4,22.46L17.29,19.28L20.9,18.46L20.56,14.78L23,12M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z'\n      };\n      return iconPaths[typeName] || iconPaths['Barangay Clearance'];\n    }\n  }\n};\n</script>\n\n<style scoped>\n/* Document Services Section */\n.services-section {\n  padding: var(--spacing-10) 0;\n  background: var(--color-bg-primary);\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 var(--spacing-4);\n}\n\n.section-header {\n  text-align: center;\n  margin-bottom: var(--spacing-10);\n}\n\n.section-title {\n  font-size: var(--font-size-2xl);\n  font-weight: 700;\n  color: var(--color-text-primary);\n  margin: 0 0 var(--spacing-3);\n  line-height: var(--line-height-3);\n}\n\n.section-description {\n  font-size: var(--font-size-lg);\n  color: var(--color-text-secondary);\n  margin: 0;\n  line-height: var(--line-height-5);\n}\n\n/* Loading State */\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: var(--spacing-12) var(--spacing-4);\n  color: var(--color-text-secondary);\n}\n\n.loading-spinner {\n  width: 48px;\n  height: 48px;\n  margin-bottom: var(--spacing-4);\n  color: var(--color-primary);\n}\n\n.spinner {\n  width: 100%;\n  height: 100%;\n}\n\n/* Error State */\n.error-container {\n  display: flex;\n  justify-content: center;\n  padding: var(--spacing-12) var(--spacing-4);\n}\n\n.error-content {\n  text-align: center;\n  max-width: 400px;\n}\n\n.error-icon {\n  width: 48px;\n  height: 48px;\n  color: var(--color-error);\n  margin-bottom: var(--spacing-4);\n}\n\n.error-content h3 {\n  font-size: var(--font-size-xl);\n  font-weight: 600;\n  color: var(--color-text-primary);\n  margin: 0 0 var(--spacing-3);\n}\n\n.error-content p {\n  color: var(--color-text-secondary);\n  margin: 0 0 var(--spacing-6);\n  line-height: var(--line-height-5);\n}\n\n.btn {\n  display: inline-flex;\n  align-items: center;\n  gap: var(--spacing-2);\n  padding: var(--spacing-3) var(--spacing-6);\n  border: 2px solid transparent;\n  border-radius: var(--radius-lg);\n  font-size: var(--font-size-md);\n  font-weight: 600;\n  text-decoration: none;\n  cursor: pointer;\n  transition: all var(--duration-base) var(--easing-standard);\n  min-height: 48px;\n}\n\n.btn-primary {\n  background: var(--color-primary);\n  color: var(--color-text-inverse);\n  border-color: var(--color-primary);\n}\n\n.btn-primary:hover,\n.btn-primary:focus {\n  background: var(--color-primary-dark);\n  border-color: var(--color-primary-dark);\n  transform: translateY(-2px);\n  box-shadow: var(--shadow-3);\n}\n\n.btn:focus {\n  outline: var(--focus-ring-width) solid var(--color-accent);\n  outline-offset: var(--focus-ring-offset);\n}\n\n.btn-icon {\n  width: 20px;\n  height: 20px;\n}\n\n/* Document Types Grid */\n.document-types-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\n  gap: var(--spacing-6);\n}\n\n.document-card {\n  display: flex;\n  flex-direction: column;\n  padding: var(--spacing-6);\n  background: var(--color-bg-primary);\n  border: 2px solid var(--color-border-light);\n  border-radius: var(--radius-xl);\n  transition: all var(--duration-base) var(--easing-standard);\n  cursor: pointer;\n  text-align: left;\n  width: 100%;\n}\n\n.document-card:hover:not(.disabled) {\n  border-color: var(--color-primary);\n  transform: translateY(-4px);\n  box-shadow: var(--shadow-4);\n}\n\n.document-card:focus:not(.disabled) {\n  outline: var(--focus-ring-width) solid var(--color-accent);\n  outline-offset: var(--focus-ring-offset);\n  border-color: var(--color-primary);\n}\n\n.document-card.disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  background: var(--color-bg-secondary);\n}\n\n.document-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: var(--spacing-4);\n}\n\n.document-icon {\n  width: 48px;\n  height: 48px;\n  background: var(--color-primary-lighter);\n  color: var(--color-primary);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.document-icon svg {\n  width: 24px;\n  height: 24px;\n}\n\n.status-badge {\n  padding: var(--spacing-1) var(--spacing-3);\n  border-radius: var(--radius-full);\n  font-size: var(--font-size-xs);\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.status-badge.available {\n  background: var(--color-success);\n  color: var(--color-text-inverse);\n}\n\n.status-badge.unavailable {\n  background: var(--color-error);\n  color: var(--color-text-inverse);\n}\n\n.document-content {\n  flex: 1;\n  margin-bottom: var(--spacing-4);\n}\n\n.document-title {\n  font-size: var(--font-size-lg);\n  font-weight: 600;\n  color: var(--color-text-primary);\n  margin: 0 0 var(--spacing-2);\n  line-height: var(--line-height-3);\n}\n\n.document-description {\n  font-size: var(--font-size-sm);\n  color: var(--color-text-secondary);\n  margin: 0 0 var(--spacing-4);\n  line-height: var(--line-height-5);\n}\n\n.document-details {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-2);\n}\n\n.detail-item {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-2);\n  font-size: var(--font-size-sm);\n}\n\n.detail-icon {\n  width: 16px;\n  height: 16px;\n  color: var(--color-text-tertiary);\n}\n\n.detail-label {\n  color: var(--color-text-secondary);\n  font-weight: 500;\n}\n\n.detail-value {\n  color: var(--color-text-primary);\n  font-weight: 600;\n}\n\n.fee-amount {\n  color: var(--color-primary);\n}\n\n.document-action {\n  align-self: flex-end;\n  width: 24px;\n  height: 24px;\n  color: var(--color-text-tertiary);\n  transition: all var(--duration-base) var(--easing-standard);\n}\n\n.document-card:hover:not(.disabled) .document-action {\n  color: var(--color-primary);\n  transform: translateX(4px);\n}\n\n.document-action svg {\n  width: 100%;\n  height: 100%;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .document-types-grid {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-4);\n  }\n  \n  .document-card {\n    padding: var(--spacing-5);\n  }\n}\n</style>\n"], "mappings": ";;EACWA,KAAK,EAAC,kBAAkB;EAAC,iBAAe,EAAC,gBAAgB;EAACC,GAAG,EAAC;;;EAChED,KAAK,EAAC;AAAW;;;EAOAA,KAAK,EAAC,mBAAmB;EAACE,IAAI,EAAC,QAAQ;EAAC,WAAS,EAAC;;;EAa/CF,KAAK,EAAC,iBAAiB;EAACE,IAAI,EAAC;;;EAC7CF,KAAK,EAAC;AAAe;;EAgBhBA,KAAK,EAAC;AAAqB;;;EAW9BA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAe;;EACnB,aAAW,EAAC,MAAM;EAACG,OAAO,EAAC,WAAW;EAACC,IAAI,EAAC;;;;EAI9CJ,KAAK,EAAC;AAAiB;;;EAGxBA,KAAK,EAAC,0BAA0B;EAChC,YAAU,EAAC;;;;EAMXA,KAAK,EAAC,wBAAwB;EAC9B,YAAU,EAAC;;;EAOZA,KAAK,EAAC;AAAkB;;EACvBA,KAAK,EAAC;AAAgB;;;EAKrBA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAa;;EAKhBA,KAAK,EAAC;AAAyB;;EAGlCA,KAAK,EAAC;AAAa;;EAKhBA,KAAK,EAAC;AAAc;;;EAK3BA,KAAK,EAAC;;;uBAlGnBK,mBAAA,CA0GU,WA1GVC,UA0GU,GAzGRC,mBAAA,CAwGM,OAxGNC,UAwGM,G,4BAvGJD,mBAAA,CAGM;IAHDP,KAAK,EAAC;EAAgB,IACzBO,mBAAA,CAA8E;IAA1EE,EAAE,EAAC,gBAAgB;IAACT,KAAK,EAAC;KAAgB,6BAA2B,GACzEO,mBAAA,CAAkF;IAA/EP,KAAK,EAAC;EAAqB,GAAC,iDAA+C,E,sBAGhFU,mBAAA,mBAAsB,EACXC,MAAA,CAAAC,OAAO,I,cAAlBP,mBAAA,CAUM,OAVNQ,UAUM,EAAAC,MAAA,QAAAA,MAAA,O,krBAGUH,MAAA,CAAAI,KAAK,I,cAArBV,mBAAA,CAcMW,SAAA;IAAAC,GAAA;EAAA,IAfNP,mBAAA,iBAAoB,EACpBH,mBAAA,CAcM,OAdNW,UAcM,GAbJX,mBAAA,CAYM,OAZNY,UAYM,G,0BAXJZ,mBAAA,CAEM;IAFDP,KAAK,EAAC,YAAY;IAAC,aAAW,EAAC,MAAM;IAACG,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MAClEG,mBAAA,CAA8D;IAAxDa,CAAC,EAAC;EAAoD,G,gDAE9Db,mBAAA,CAAgC,YAA5B,yBAAuB,sBAC3BA,mBAAA,CAAkB,WAAAc,gBAAA,CAAZV,MAAA,CAAAI,KAAK,kBACXR,mBAAA,CAKS;IALDP,KAAK,EAAC,2BAA2B;IAAEsB,OAAK,EAAAR,MAAA,QAAAA,MAAA,MAAAS,MAAA,IAAEC,IAAA,CAAAC,KAAK;IAAWC,IAAI,EAAC;gCACrEnB,mBAAA,CAEM;IAFDP,KAAK,EAAC,UAAU;IAAC,aAAW,EAAC,MAAM;IAACG,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC;MAChEG,mBAAA,CAA8N;IAAxNa,CAAC,EAAC;EAAoN,G,sCACxN,aAER,E,0EAKJf,mBAAA,CAkEMW,SAAA;IAAAC,GAAA;EAAA,IAnENP,mBAAA,yBAA4B,EAC5BH,mBAAA,CAkEM,OAlENoB,UAkEM,I,kBAjEJtB,mBAAA,CAgESW,SAAA,QAAAY,WAAA,CA/DgBjB,MAAA,CAAAkB,aAAa,EAA7BC,YAAY;yBADrBzB,mBAAA,CAgES;MA9DNY,GAAG,EAAEa,YAAY,CAACrB,EAAE;MACrBT,KAAK,EAAA+B,eAAA,EAAC,eAAe;QAAA,aAEED,YAAY,CAACE;MAAS;MAD5CV,OAAK,EAAAC,MAAA,IAAEU,QAAA,CAAAC,kBAAkB,CAACJ,YAAY;MAEtCK,QAAQ,GAAGL,YAAY,CAACE,SAAS;MACjC,kBAAgB,SAASF,YAAY,CAACrB,EAAE;MACzCiB,IAAI,EAAC;QAELnB,mBAAA,CAsBM,OAtBN6B,UAsBM,GArBJ7B,mBAAA,CAIM,OAJN8B,UAIM,I,cAHJhC,mBAAA,CAEM,OAFNiC,WAEM,GADJ/B,mBAAA,CAAwD;MAAjDa,CAAC,EAAEa,QAAA,CAAAM,mBAAmB,CAACT,YAAY,CAACU,SAAS;+CAGxDjC,mBAAA,CAeM,OAfNkC,WAeM,G,CAbKX,YAAY,CAACE,SAAS,I,cAD/B3B,mBAAA,CAMO,QANPqC,WAMO,EAFN,eAED,M,cACArC,mBAAA,CAMO,QANPsC,WAMO,EAFN,aAED,G,KAIJpC,mBAAA,CAuBM,OAvBNqC,WAuBM,GAtBJrC,mBAAA,CAA4D,MAA5DsC,WAA4D,EAAAxB,gBAAA,CAA9BS,YAAY,CAACU,SAAS,kBACpDjC,mBAAA,CAEI;MAFAE,EAAE,SAASqB,YAAY,CAACrB,EAAE;MAAST,KAAK,EAAC;wBACxC8B,YAAY,CAACgB,WAAW,wBAAAC,WAAA,GAG7BxC,mBAAA,CAgBM,OAhBNyC,WAgBM,GAfJzC,mBAAA,CAMM,OANN0C,WAMM,G,0BALJ1C,mBAAA,CAEM;MAFDP,KAAK,EAAC,aAAa;MAAC,aAAW,EAAC,MAAM;MAACG,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC;QACnEG,mBAAA,CAAkW;MAA5Va,CAAC,EAAC;IAAwV,G,gDAElWb,mBAAA,CAAsC;MAAhCP,KAAK,EAAC;IAAc,GAAC,MAAI,sBAC/BO,mBAAA,CAAyF,QAAzF2C,WAAyF,EAAnD,GAAC,GAAA7B,gBAAA,CAAGY,QAAA,CAAAkB,cAAc,CAACrB,YAAY,CAACsB,QAAQ,kB,GAGhF7C,mBAAA,CAMM,OANN8C,WAMM,G,0BALJ9C,mBAAA,CAEM;MAFDP,KAAK,EAAC,aAAa;MAAC,aAAW,EAAC,MAAM;MAACG,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC;QACnEG,mBAAA,CAA8M;MAAxMa,CAAC,EAAC;IAAoM,G,gDAE9Mb,mBAAA,CAA6C;MAAvCP,KAAK,EAAC;IAAc,GAAC,aAAW,sBACtCO,mBAAA,CAAiF,QAAjF+C,WAAiF,EAAAjC,gBAAA,CAAnDY,QAAA,CAAAsB,iBAAiB,CAACzB,YAAY,CAACU,SAAS,kB,OAKzCV,YAAY,CAACE,SAAS,I,cAAzD3B,mBAAA,CAIM,OAJNmD,WAIM,OAAA1C,MAAA,QAAAA,MAAA,OAHJP,mBAAA,CAEM;MAFD,aAAW,EAAC,MAAM;MAACJ,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC;QAC/CG,mBAAA,CAAmF;MAA7Ea,CAAC,EAAC;IAAyE,G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}