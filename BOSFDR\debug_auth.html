<!DOCTYPE html>
<html>
<head>
    <title>Debug Authentication</title>
</head>
<body>
    <h1>Authentication Debug</h1>
    <div id="debug-info"></div>
    
    <script>
        function debugAuth() {
            const debugInfo = document.getElementById('debug-info');
            
            const authToken = localStorage.getItem('auth_token');
            const authUser = localStorage.getItem('auth_user');
            const clientToken = localStorage.getItem('clientToken');
            const clientData = localStorage.getItem('clientData');
            
            debugInfo.innerHTML = `
                <h2>Current Authentication State:</h2>
                <p><strong>auth_token:</strong> ${authToken ? 'EXISTS' : 'NOT SET'}</p>
                <p><strong>auth_user:</strong> ${authUser ? 'EXISTS' : 'NOT SET'}</p>
                <p><strong>clientToken:</strong> ${clientToken ? 'EXISTS' : 'NOT SET'}</p>
                <p><strong>clientData:</strong> ${clientData ? 'EXISTS' : 'NOT SET'}</p>
                
                <h3>Token Values:</h3>
                <p><strong>auth_token:</strong> ${authToken || 'null'}</p>
                <p><strong>clientToken:</strong> ${clientToken || 'null'}</p>
                
                <h3>User Data:</h3>
                <pre><strong>auth_user:</strong> ${authUser || 'null'}</pre>
                <pre><strong>clientData:</strong> ${clientData || 'null'}</pre>
                
                <h3>Actions:</h3>
                <button onclick="clearAll()">Clear All Auth Data</button>
                <button onclick="migrateToUnified()">Migrate to Unified Auth</button>
            `;
        }
        
        function clearAll() {
            localStorage.removeItem('auth_token');
            localStorage.removeItem('auth_user');
            localStorage.removeItem('clientToken');
            localStorage.removeItem('clientData');
            sessionStorage.removeItem('auth_token');
            sessionStorage.removeItem('auth_user');
            alert('All auth data cleared!');
            debugAuth();
        }
        
        function migrateToUnified() {
            const clientToken = localStorage.getItem('clientToken');
            const clientData = localStorage.getItem('clientData');
            
            if (clientToken && clientData) {
                localStorage.setItem('auth_token', clientToken);
                localStorage.setItem('auth_user', clientData);
                alert('Migrated to unified auth!');
                debugAuth();
            } else {
                alert('No client auth data to migrate!');
            }
        }
        
        // Run debug on load
        debugAuth();
    </script>
</body>
</html>
