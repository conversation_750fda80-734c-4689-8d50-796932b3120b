/* Modern Government Client Header Styles - USWDS Inspired */
:root {
  /* Primary Government Colors */
  --gov-blue: #005ea2;
  --gov-blue-dark: #0f4c96;
  --gov-blue-light: #2378c3;
  --gov-blue-lighter: #e7f6f8;
  --gov-red: #d63384;
  --gov-green: #00a91c;
  --gov-yellow: #ffbe2e;
  --gov-yellow-light: #fef0cd;

  /* Neutral Colors */
  --text-primary: #1b1b1b;
  --text-secondary: #454545;
  --text-light: #757575;
  --text-white: #ffffff;

  /* Background Colors */
  --bg-white: #ffffff;
  --bg-gray-5: #f9f9f9;
  --bg-gray-10: #f0f0f0;
  --bg-gray-20: #dfe1e2;
  --bg-gray-30: #a9aeb1;

  /* Border Colors */
  --border-light: #dfe1e2;
  --border-medium: #a9aeb1;
  --border-dark: #565c65;

  /* Shadows */
  --shadow-1: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
  --shadow-2: 0 1px 4px 0 rgba(0, 0, 0, 0.1);
  --shadow-3: 0 4px 8px 0 rgba(0, 0, 0, 0.1);
  --shadow-4: 0 8px 16px 0 rgba(0, 0, 0, 0.1);
  --shadow-5: 0 16px 32px 0 rgba(0, 0, 0, 0.1);

  /* Typography */
  --font-family-sans: 'Source Sans Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  --font-family-serif: 'Merriweather', Georgia, Cambria, 'Times New Roman', Times, serif;
  --font-family-mono: 'Roboto Mono', 'Bitstream Vera Sans Mono', 'Lucida Console', Consolas, Monaco, monospace;

  /* Spacing */
  --spacing-05: 0.125rem;
  --spacing-1: 0.25rem;
  --spacing-105: 0.375rem;
  --spacing-2: 0.5rem;
  --spacing-205: 0.625rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-7: 1.75rem;
  --spacing-8: 2rem;
  --spacing-9: 2.25rem;
  --spacing-10: 2.5rem;

  /* Border Radius */
  --border-radius-sm: 0.125rem;
  --border-radius-md: 0.25rem;
  --border-radius-lg: 0.5rem;

  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-base: 0.2s ease-in-out;
  --transition-slow: 0.3s ease-in-out;
}

/* Reset and Base Styles */
.client-header * {
  box-sizing: border-box;
}

.client-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: var(--bg-white);
  box-shadow: var(--shadow-2);
  font-family: var(--font-family-sans);
  border-bottom: 1px solid var(--border-light);
}

/* Screen Reader Only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Government Banner */
.gov-banner {
  background: var(--gov-blue);
  color: var(--text-white);
  padding: var(--spacing-2) 0;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.3;
}

.gov-banner-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.gov-banner-flag {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.flag-icon {
  width: 20px;
  height: auto;
  display: block;
}

.gov-banner-text {
  flex: 1;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  min-width: 0;
}

.gov-banner-label {
  opacity: 0.9;
  font-weight: 400;
}

.gov-banner-agency {
  color: var(--gov-yellow);
  font-weight: 700;
}

.gov-banner-secure {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  font-size: 0.75rem;
  opacity: 0.9;
  flex-shrink: 0;
}

.gov-banner-secure i {
  font-size: 0.875rem;
}

/* Main Header */
.main-header {
  background: var(--bg-white);
  border-bottom: 1px solid var(--border-light);
  padding: var(--spacing-4) 0;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-8);
}

/* Logo and Site Identity */
.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  flex-shrink: 0;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  text-decoration: none;
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: var(--border-radius-md);
  transition: background-color var(--transition-fast);
}

.logo-section:hover,
.logo-section:focus {
  background-color: var(--bg-gray-5);
  outline: 2px solid var(--gov-blue);
  outline-offset: 2px;
}

.logo {
  width: 48px;
  height: 48px;
  object-fit: contain;
  border-radius: var(--border-radius-sm);
}

.site-identity {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-05);
}

.site-title {
  font-size: 1.375rem;
  font-weight: 700;
  color: var(--gov-blue);
  margin: 0;
  line-height: 1.2;
  font-family: var(--font-family-serif);
}

.site-subtitle {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 400;
  line-height: 1.3;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 44px;
  height: 44px;
  background: none;
  border: 2px solid transparent;
  cursor: pointer;
  padding: var(--spacing-2);
  gap: var(--spacing-1);
  border-radius: var(--border-radius-md);
  transition: all var(--transition-fast);
}

.mobile-menu-toggle:hover,
.mobile-menu-toggle:focus {
  border-color: var(--gov-blue);
  background-color: var(--bg-gray-5);
}

.hamburger-line {
  width: 20px;
  height: 3px;
  background: var(--gov-blue);
  transition: all var(--transition-base);
  border-radius: var(--border-radius-sm);
}

.mobile-menu-toggle:hover .hamburger-line {
  background: var(--gov-blue-dark);
}

/* Main Navigation */
.main-navigation {
  flex: 1;
  display: flex;
  justify-content: center;
}

.nav-list {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  list-style: none;
  margin: 0;
  padding: 0;
  background: var(--bg-gray-5);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-1);
}

.nav-item {
  margin: 0;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-4);
  color: var(--text-primary);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  border-radius: var(--border-radius-md);
  transition: all var(--transition-fast);
  position: relative;
  white-space: nowrap;
}

.nav-link:hover {
  background: var(--bg-white);
  color: var(--gov-blue);
  text-decoration: none;
  box-shadow: var(--shadow-1);
}

.nav-link.active {
  background: var(--gov-blue);
  color: var(--text-white);
  font-weight: 600;
  box-shadow: var(--shadow-2);
}

.nav-link:focus {
  outline: 2px solid var(--gov-blue);
  outline-offset: 2px;
}

.nav-link i {
  font-size: 1rem;
  width: 16px;
  text-align: center;
}

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-shrink: 0;
}

/* Search Container */
.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: none;
  border: 1px solid var(--border-gray);
  border-radius: 8px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.search-toggle:hover {
  background: var(--bg-gray-50);
  color: var(--gov-blue);
  border-color: var(--gov-blue);
}

.search-box {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--bg-white);
  border: 1px solid var(--border-gray);
  border-radius: 8px;
  box-shadow: var(--shadow-lg);
  padding: 0.5rem;
  min-width: 300px;
  margin-top: 0.5rem;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;
  z-index: 1001;
}

.search-box.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.search-box {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.search-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid var(--border-gray);
  border-radius: 6px;
  font-size: 0.875rem;
  outline: none;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  border-color: var(--gov-blue);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.search-submit {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: var(--gov-blue);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.search-submit:hover {
  background: var(--gov-blue-dark);
}

/* User Profile */
.user-profile {
  position: relative;
}

.user-profile.active .dropdown-arrow {
  transform: rotate(180deg);
}

.user-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0.75rem;
  background: var(--bg-white);
  border: 1px solid var(--border-gray);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: var(--text-primary);
}

.user-btn:hover {
  background: var(--bg-gray-50);
  border-color: var(--gov-blue);
  box-shadow: var(--shadow-sm);
}

.user-avatar {
  position: relative;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  background: var(--bg-gray-100);
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-icon {
  font-size: 1.5rem;
  color: var(--text-light);
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
  min-width: 0;
}

.user-name {
  font-weight: 600;
  font-size: 0.875rem;
  line-height: 1.2;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.user-role {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1;
}

.dropdown-arrow {
  font-size: 0.75rem;
  color: var(--text-light);
  transition: transform 0.2s ease;
  margin-left: auto;
}

/* User Dropdown Menu */
.user-dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--bg-white);
  border: 1px solid var(--border-gray);
  border-radius: 12px;
  box-shadow: var(--shadow-xl);
  padding: 0.5rem 0;
  min-width: 280px;
  z-index: 1002;
  margin-top: 0.5rem;
  overflow: hidden;
}

.dropdown-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-gray);
  background: var(--bg-gray-50);
}

.user-details strong {
  display: block;
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.user-email {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  color: var(--text-primary);
  text-decoration: none;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
}

.dropdown-item:hover {
  background: var(--bg-gray-50);
  color: var(--gov-blue);
}

.dropdown-item i {
  width: 16px;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.dropdown-item:hover i {
  color: var(--gov-blue);
}

.logout-item {
  color: var(--gov-red);
}

.logout-item:hover {
  background: #fef2f2;
  color: var(--gov-red);
}

.logout-item i {
  color: var(--gov-red);
}

.dropdown-divider {
  height: 1px;
  background: var(--border-gray);
  margin: 0.5rem 0;
}

/* Breadcrumb Section */
.breadcrumb-section {
  background: var(--bg-gray-50);
  border-bottom: 1px solid var(--border-gray);
  padding: 0.75rem 0;
}

.breadcrumb-nav {
  font-size: 0.875rem;
}

.breadcrumb-list {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  list-style: none;
  margin: 0;
  padding: 0;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.breadcrumb-item:not(:last-child)::after {
  content: '/';
  color: var(--text-light);
  margin-left: 0.5rem;
}

.breadcrumb-item a {
  color: var(--gov-blue);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  transition: color 0.2s ease;
}

.breadcrumb-item a:hover {
  color: var(--gov-blue-dark);
  text-decoration: underline;
}

.breadcrumb-item.active {
  color: var(--text-secondary);
  font-weight: 500;
}

.breadcrumb-item i {
  font-size: 0.75rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .header-container {
    padding: 0 1.5rem;
  }

  .main-navigation {
    display: none;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .search-box {
    min-width: 280px;
  }
}

@media (max-width: 768px) {
  .client-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
  }

  .gov-banner-content {
    padding: 0 1rem;
    gap: 0.75rem;
  }

  .gov-banner-text {
    font-size: 0.75rem;
  }

  .gov-banner-secure {
    display: none;
  }

  .main-header {
    padding: 0.5rem 0;
  }

  .header-container {
    padding: 0 1rem;
    gap: 1rem;
  }

  .logo {
    width: 32px;
    height: 32px;
  }

  .site-title {
    font-size: 1rem;
  }

  .site-subtitle {
    font-size: 0.7rem;
  }

  .header-actions {
    gap: 0.5rem;
  }

  .user-info {
    display: none;
  }

  .user-btn {
    padding: 0.5rem;
    min-width: 40px;
    min-height: 40px;
  }

  .search-toggle {
    width: 36px;
    height: 36px;
  }

  .search-box {
    min-width: 260px;
    right: -1rem;
  }

  .user-dropdown-menu {
    min-width: 240px;
    right: -1rem;
  }

  .breadcrumb-section {
    padding: 0.5rem 0;
  }

  .breadcrumb-list {
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .gov-banner {
    padding: 0.375rem 0;
  }

  .gov-banner-content {
    padding: 0 0.75rem;
    gap: 0.5rem;
  }

  .flag-icon {
    width: 16px;
  }

  .gov-banner-text {
    font-size: 0.7rem;
  }

  .gov-banner-agency {
    display: block;
    margin-top: 0.125rem;
  }

  .header-container {
    padding: 0 0.75rem;
    gap: 0.75rem;
  }

  .logo {
    width: 28px;
    height: 28px;
  }

  .site-title {
    font-size: 0.9rem;
  }

  .site-subtitle {
    font-size: 0.65rem;
  }

  .header-actions {
    gap: 0.375rem;
  }

  .search-toggle,
  .user-btn {
    width: 32px;
    height: 32px;
    padding: 0.375rem;
  }

  .search-box {
    min-width: 240px;
    right: -0.75rem;
  }

  .search-input {
    padding: 0.625rem;
    font-size: 0.8rem;
  }

  .search-submit {
    width: 32px;
    height: 32px;
  }

  .user-dropdown-menu {
    min-width: 200px;
    right: -0.75rem;
  }

  .dropdown-header {
    padding: 0.75rem 1rem;
  }

  .dropdown-item {
    padding: 0.625rem 1rem;
    font-size: 0.8rem;
  }

  .breadcrumb-section {
    padding: 0.375rem 0;
  }

  .breadcrumb-list {
    font-size: 0.7rem;
  }
}

/* Focus and Accessibility Styles */
.search-toggle:focus,
.user-btn:focus,
.mobile-menu-toggle:focus,
.nav-link:focus,
.dropdown-item:focus {
  outline: 2px solid var(--gov-blue);
  outline-offset: 2px;
}

.search-input:focus {
  outline: none;
  border-color: var(--gov-blue);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .client-header *,
  .client-header *::before,
  .client-header *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .client-header {
    border-bottom: 2px solid;
  }

  .nav-link.active {
    background: var(--text-primary);
    color: var(--bg-white);
  }

  .user-btn {
    border: 2px solid;
  }
}

/* Print Styles */
@media print {
  .client-header {
    display: none;
  }
}


