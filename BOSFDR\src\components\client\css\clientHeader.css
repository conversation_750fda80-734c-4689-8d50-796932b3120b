/* Modern Government Header Styles - USWDS 3.0 Inspired */

/* Skip link for accessibility */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #005ea2;
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 0 0 4px 4px;
  z-index: 1100;
  font-weight: 600;
  transition: top 0.2s ease;
}

.skip-link:focus {
  top: 0;
}

/* Screen reader only text */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Header Container */
.client-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-family: 'Source Sans Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Government Banner */
.gov-banner {
  background: #0f4c96;
  color: #ffffff;
  padding: 0.5rem 0;
  font-size: 0.75rem;
}

.gov-banner-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.gov-banner-flag {
  display: flex;
  align-items: center;
}

.flag-icon {
  border-radius: 0.125rem;
}

.gov-banner-text {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.gov-banner-label {
  opacity: 0.9;
}

.gov-banner-agency {
  font-weight: 700;
}

.gov-banner-secure {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.625rem;
  opacity: 0.9;
}

/* Main Header */
.main-header {
  background: #ffffff;
  border-bottom: 1px solid #dfe1e2;
  padding: 1rem 0;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1.5rem;
}

/* Header Brand */
.header-brand {
  display: flex;
  align-items: center;
}

.logo-button {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.logo-button:hover,
.logo-button:focus {
  background: #f9f9f9;
  outline: 2px solid #ffbe2e;
  outline-offset: 2px;
}

.logo {
  border-radius: 0.25rem;
  object-fit: cover;
}

.site-identity {
  display: flex;
  flex-direction: column;
}

.site-title {
  font-size: 1.125rem;
  font-weight: 700;
  color: #0f4c96;
  margin: 0;
  line-height: 1.2;
  font-family: 'Merriweather', Georgia, serif;
}

.site-subtitle {
  font-size: 0.875rem;
  color: #454545;
  line-height: 1.2;
}

/* Primary Navigation */
.primary-nav {
  flex: 1;
  display: flex;
  justify-content: center;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 0.5rem;
}

.nav-item {
  position: relative;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  color: #454545;
  background: none;
  border: 2px solid transparent;
  border-radius: 0.25rem;
  font-weight: 500;
  font-size: 0.875rem;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.nav-link:hover,
.nav-link:focus {
  color: #005ea2;
  background: #e7f6f8;
  border-color: #2378c3;
  outline: none;
}

.nav-link.active {
  color: #0f4c96;
  background: #e7f6f8;
  border-color: #005ea2;
  font-weight: 700;
}

.nav-link i {
  font-size: 1rem;
}

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Search Container */
.search-container {
  position: relative;
}

.search-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: none;
  border: 2px solid transparent;
  border-radius: 0.25rem;
  color: #454545;
  cursor: pointer;
  transition: all 0.2s ease;
}

.search-toggle:hover,
.search-toggle:focus {
  color: #005ea2;
  background: #e7f6f8;
  border-color: #2378c3;
  outline: none;
}

.search-box {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  background: #ffffff;
  border: 2px solid #005ea2;
  border-radius: 0.25rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  min-width: 300px;
  z-index: 1010;
}

.search-input {
  flex: 1;
  padding: 0.75rem;
  border: none;
  background: none;
  font-size: 0.875rem;
  outline: none;
}

.search-submit {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #005ea2;
  color: #ffffff;
  border: none;
  border-radius: 0 0.25rem 0.25rem 0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.search-submit:hover,
.search-submit:focus {
  background: #0f4c96;
  outline: 2px solid #ffbe2e;
  outline-offset: 2px;
}

/* Search Transitions */
.search-slide-enter-active,
.search-slide-leave-active {
  transition: all 0.3s ease;
}

.search-slide-enter-from {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

.search-slide-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

/* User Profile */
.user-profile {
  position: relative;
}

.user-button {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: none;
  border: 2px solid transparent;
  border-radius: 0.25rem;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.user-button:hover,
.user-button:focus {
  background: #f9f9f9;
  border-color: #2378c3;
  outline: none;
}

.user-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-icon {
  font-size: 1.5rem;
  color: #454545;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-size: 0.875rem;
  font-weight: 700;
  color: #1b1b1b;
  line-height: 1.2;
}

.user-role {
  font-size: 0.75rem;
  color: #454545;
  line-height: 1.2;
}

.dropdown-arrow {
  font-size: 0.75rem;
  color: #454545;
  transition: transform 0.2s ease;
}

.dropdown-arrow.rotated {
  transform: rotate(180deg);
}

/* User Dropdown */
.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  background: #ffffff;
  border: 2px solid #dfe1e2;
  border-radius: 0.25rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  min-width: 240px;
  z-index: 1010;
}

.dropdown-header {
  padding: 1rem;
  border-bottom: 1px solid #dfe1e2;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.user-display-name {
  font-size: 0.875rem;
  font-weight: 700;
  color: #1b1b1b;
}

.user-email {
  font-size: 0.75rem;
  color: #454545;
}

.dropdown-divider {
  height: 1px;
  background: #dfe1e2;
  margin: 0;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
  padding: 0.75rem 1rem;
  background: none;
  border: none;
  color: #1b1b1b;
  font-size: 0.875rem;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dropdown-item:hover,
.dropdown-item:focus {
  background: #f9f9f9;
  color: #005ea2;
  outline: none;
}

.dropdown-item.logout-item {
  color: #d63384;
}

.dropdown-item.logout-item:hover,
.dropdown-item.logout-item:focus {
  background: #fef0cd;
  color: #d63384;
}

.dropdown-item i {
  font-size: 1rem;
  width: 16px;
  text-align: center;
}

/* Dropdown Transitions */
.dropdown-fade-enter-active,
.dropdown-fade-leave-active {
  transition: all 0.3s ease;
}

.dropdown-fade-enter-from {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

.dropdown-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.mobile-menu-toggle:hover,
.mobile-menu-toggle:focus {
  background: #f9f9f9;
  outline: 2px solid #ffbe2e;
  outline-offset: 2px;
}

.hamburger-line {
  width: 24px;
  height: 3px;
  background: #005ea2;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.hamburger-line.active:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.hamburger-line.active:nth-child(2) {
  opacity: 0;
}

.hamburger-line.active:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Mobile Navigation */
.mobile-nav {
  position: fixed;
  top: 100%;
  left: 0;
  right: 0;
  background: #ffffff;
  border-top: 1px solid #dfe1e2;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 999;
}

.mobile-nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.mobile-nav-menu {
  width: 100%;
}

.mobile-nav-list {
  display: flex;
  flex-direction: column;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 0.5rem;
}

.mobile-nav-item {
  width: 100%;
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
  padding: 1rem;
  color: #454545;
  background: none;
  border: 2px solid transparent;
  border-radius: 0.25rem;
  font-weight: 500;
  font-size: 1rem;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mobile-nav-link:hover,
.mobile-nav-link:focus {
  color: #005ea2;
  background: #e7f6f8;
  border-color: #2378c3;
  outline: none;
}

.mobile-nav-link.active {
  color: #0f4c96;
  background: #e7f6f8;
  border-color: #005ea2;
  font-weight: 700;
}

.mobile-nav-link i {
  font-size: 1.25rem;
  width: 20px;
  text-align: center;
}

/* Mobile Menu Transitions */
.mobile-menu-slide-enter-active,
.mobile-menu-slide-leave-active {
  transition: all 0.3s ease;
}

.mobile-menu-slide-enter-from {
  opacity: 0;
  transform: translateY(-100%);
}

.mobile-menu-slide-leave-to {
  opacity: 0;
  transform: translateY(-100%);
}

/* Breadcrumb Navigation */
.breadcrumb-section {
  background: #f9f9f9;
  border-bottom: 1px solid #dfe1e2;
  padding: 0.75rem 0;
}

.breadcrumb-nav {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.breadcrumb-list {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 0.5rem;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.breadcrumb-item:not(:last-child)::after {
  content: '>';
  color: #757575;
  font-size: 0.875rem;
}

.breadcrumb-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #005ea2;
  background: none;
  border: none;
  font-size: 0.875rem;
  text-decoration: none;
  cursor: pointer;
  transition: color 0.2s ease;
}

.breadcrumb-link:hover,
.breadcrumb-link:focus {
  color: #0f4c96;
  text-decoration: underline;
  outline: none;
}

.breadcrumb-item.active span {
  color: #454545;
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .header-container {
    gap: 1rem;
  }

  .primary-nav {
    display: none;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .user-info {
    display: none;
  }
}

@media (max-width: 768px) {
  .gov-banner-content {
    padding: 0 0.5rem;
    gap: 0.5rem;
  }

  .gov-banner-text {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .header-container {
    padding: 0 0.5rem;
    gap: 0.5rem;
  }

  .logo-button {
    gap: 0.5rem;
  }

  .site-title {
    font-size: 1rem;
  }

  .site-subtitle {
    font-size: 0.75rem;
  }

  .header-actions {
    gap: 0.5rem;
  }

  .search-box {
    min-width: 250px;
    right: -50px;
  }

  .user-dropdown {
    min-width: 200px;
    right: -20px;
  }
}

@media (max-width: 480px) {
  .gov-banner {
    padding: 0.25rem 0;
    font-size: 0.625rem;
  }

  .gov-banner-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .gov-banner-secure {
    align-self: flex-end;
  }

  .main-header {
    padding: 0.5rem 0;
  }

  .header-container {
    gap: 0.25rem;
  }

  .logo-button {
    padding: 0.25rem;
  }

  .site-title {
    font-size: 0.875rem;
  }

  .site-subtitle {
    display: none;
  }

  .search-box {
    min-width: 200px;
    right: -75px;
  }

  .user-dropdown {
    right: -50px;
  }

  .mobile-nav-content {
    padding: 0.5rem;
  }

  .mobile-nav-link {
    padding: 0.75rem;
    font-size: 0.875rem;
  }

  .breadcrumb-section {
    padding: 0.5rem 0;
  }

  .breadcrumb-nav {
    padding: 0 0.5rem;
  }

  .breadcrumb-link,
  .breadcrumb-item.active span {
    font-size: 0.75rem;
  }
}

/* Focus and Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .client-header {
    border-bottom: 2px solid;
  }

  .nav-link:focus,
  .search-toggle:focus,
  .user-button:focus,
  .mobile-menu-toggle:focus {
    outline: 3px solid;
    outline-offset: 2px;
  }
}
